using Microsoft.EntityFrameworkCore;
using Baytara.Shared.Models;

namespace Baytara.API.Data;

public class BaytaraDbContext : DbContext
{
    public BaytaraDbContext(DbContextOptions<BaytaraDbContext> options) : base(options)
    {
    }

    // DbSets
    public DbSet<Branch> Branches { get; set; }
    public DbSet<Breeder> Breeders { get; set; }
    public DbSet<AnimalType> AnimalTypes { get; set; }
    public DbSet<Animal> Animals { get; set; }
    public DbSet<Disease> Diseases { get; set; }
    public DbSet<Medicine> Medicines { get; set; }
    public DbSet<Treatment> Treatments { get; set; }
    public DbSet<TreatmentMedicine> TreatmentMedicines { get; set; }
    public DbSet<LabTest> LabTests { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // تكوين الفرع
        modelBuilder.Entity<Branch>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Address).HasMaxLength(200);
            entity.Property(e => e.Phone).HasMaxLength(20);
            entity.HasIndex(e => e.Name).IsUnique();
        });

        // تكوين المربي
        modelBuilder.Entity<Breeder>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Phone).IsRequired().HasMaxLength(20);
            entity.Property(e => e.Address).HasMaxLength(200);
            entity.Property(e => e.Region).HasMaxLength(100);
            entity.Property(e => e.NationalId).HasMaxLength(50);
            
            entity.HasOne(e => e.Branch)
                  .WithMany(b => b.Breeders)
                  .HasForeignKey(e => e.BranchId)
                  .OnDelete(DeleteBehavior.Restrict);
                  
            entity.HasIndex(e => e.Phone).IsUnique();
        });

        // تكوين نوع الحيوان
        modelBuilder.Entity<AnimalType>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Description).HasMaxLength(200);
            entity.HasIndex(e => e.Name).IsUnique();
        });

        // تكوين الحيوان
        modelBuilder.Entity<Animal>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).HasMaxLength(100);
            entity.Property(e => e.Notes).HasMaxLength(500);
            entity.Property(e => e.Category).HasConversion<int>();
            entity.Property(e => e.Status).HasConversion<int>();
            
            entity.HasOne(e => e.AnimalType)
                  .WithMany(at => at.Animals)
                  .HasForeignKey(e => e.AnimalTypeId)
                  .OnDelete(DeleteBehavior.Restrict);
                  
            entity.HasOne(e => e.Breeder)
                  .WithMany(b => b.Animals)
                  .HasForeignKey(e => e.BreederId)
                  .OnDelete(DeleteBehavior.Restrict);
                  
            entity.HasOne(e => e.Branch)
                  .WithMany(b => b.Animals)
                  .HasForeignKey(e => e.BranchId)
                  .OnDelete(DeleteBehavior.Restrict);
        });

        // تكوين المرض
        modelBuilder.Entity<Disease>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Description).HasMaxLength(500);
            entity.Property(e => e.Symptoms).HasMaxLength(500);
            entity.Property(e => e.Prevention).HasMaxLength(500);
            entity.HasIndex(e => e.Name).IsUnique();
        });

        // تكوين الدواء
        modelBuilder.Entity<Medicine>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.TradeName).HasMaxLength(100);
            entity.Property(e => e.Description).HasMaxLength(500);
            entity.Property(e => e.Manufacturer).HasMaxLength(200);
            entity.Property(e => e.UsageInstructions).HasMaxLength(500);
            entity.Property(e => e.Warnings).HasMaxLength(500);
            entity.Property(e => e.Type).HasConversion<int>();
            entity.Property(e => e.Unit).HasConversion<int>();
            entity.Property(e => e.AvailableQuantity).HasPrecision(18, 2);
            entity.Property(e => e.Price).HasPrecision(18, 2);
        });

        // تكوين العلاج
        modelBuilder.Entity<Treatment>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Symptoms).HasMaxLength(500);
            entity.Property(e => e.Diagnosis).HasMaxLength(500);
            entity.Property(e => e.TreatmentPlan).HasMaxLength(1000);
            entity.Property(e => e.Notes).HasMaxLength(500);
            entity.Property(e => e.VeterinarianName).HasMaxLength(100);
            entity.Property(e => e.Status).HasConversion<int>();
            entity.Property(e => e.Cost).HasPrecision(18, 2);
            
            entity.HasOne(e => e.Animal)
                  .WithMany(a => a.Treatments)
                  .HasForeignKey(e => e.AnimalId)
                  .OnDelete(DeleteBehavior.Restrict);
                  
            entity.HasOne(e => e.Disease)
                  .WithMany(d => d.Treatments)
                  .HasForeignKey(e => e.DiseaseId)
                  .OnDelete(DeleteBehavior.SetNull);
                  
            entity.HasOne(e => e.Branch)
                  .WithMany(b => b.Treatments)
                  .HasForeignKey(e => e.BranchId)
                  .OnDelete(DeleteBehavior.Restrict);
        });

        // تكوين ربط العلاج بالأدوية
        modelBuilder.Entity<TreatmentMedicine>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Instructions).HasMaxLength(200);
            entity.Property(e => e.Quantity).HasPrecision(18, 2);
            
            entity.HasOne(e => e.Treatment)
                  .WithMany(t => t.TreatmentMedicines)
                  .HasForeignKey(e => e.TreatmentId)
                  .OnDelete(DeleteBehavior.Cascade);
                  
            entity.HasOne(e => e.Medicine)
                  .WithMany(m => m.TreatmentMedicines)
                  .HasForeignKey(e => e.MedicineId)
                  .OnDelete(DeleteBehavior.Restrict);
        });

        // تكوين الفحص المختبري
        modelBuilder.Entity<LabTest>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.TestName).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Description).HasMaxLength(500);
            entity.Property(e => e.Results).HasMaxLength(2000);
            entity.Property(e => e.Interpretation).HasMaxLength(1000);
            entity.Property(e => e.Notes).HasMaxLength(500);
            entity.Property(e => e.LabName).HasMaxLength(100);
            entity.Property(e => e.TechnicianName).HasMaxLength(100);
            entity.Property(e => e.Type).HasConversion<int>();
            entity.Property(e => e.Status).HasConversion<int>();
            entity.Property(e => e.Cost).HasPrecision(18, 2);
            
            entity.HasOne(e => e.Animal)
                  .WithMany(a => a.LabTests)
                  .HasForeignKey(e => e.AnimalId)
                  .OnDelete(DeleteBehavior.Restrict);
                  
            entity.HasOne(e => e.Treatment)
                  .WithMany(t => t.LabTests)
                  .HasForeignKey(e => e.TreatmentId)
                  .OnDelete(DeleteBehavior.SetNull);
                  
            entity.HasOne(e => e.Branch)
                  .WithMany()
                  .HasForeignKey(e => e.BranchId)
                  .OnDelete(DeleteBehavior.Restrict);
        });

        // بيانات أولية
        SeedData(modelBuilder);
    }

    private void SeedData(ModelBuilder modelBuilder)
    {
        // فرع افتراضي
        modelBuilder.Entity<Branch>().HasData(
            new Branch
            {
                Id = 1,
                Name = "الفرع الرئيسي",
                Address = "مسقط، سلطنة عمان",
                Phone = "+968 24123456",
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            }
        );

        // أنواع الحيوانات الافتراضية
        modelBuilder.Entity<AnimalType>().HasData(
            new AnimalType { Id = 1, Name = "جمل", Description = "الإبل", IsActive = true, CreatedAt = DateTime.UtcNow },
            new AnimalType { Id = 2, Name = "ماعز", Description = "الماعز", IsActive = true, CreatedAt = DateTime.UtcNow },
            new AnimalType { Id = 3, Name = "غنم", Description = "الأغنام", IsActive = true, CreatedAt = DateTime.UtcNow },
            new AnimalType { Id = 4, Name = "بقر", Description = "الأبقار", IsActive = true, CreatedAt = DateTime.UtcNow },
            new AnimalType { Id = 5, Name = "دجاج", Description = "الدواجن", IsActive = true, CreatedAt = DateTime.UtcNow }
        );
    }
}
