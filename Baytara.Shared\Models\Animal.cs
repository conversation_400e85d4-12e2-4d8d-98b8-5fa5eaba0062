using System.ComponentModel.DataAnnotations;

namespace Baytara.Shared.Models;

// تصنيف الحيوان
public enum AnimalCategory
{
    Free = 1,      // مجاني
    Economic = 2,  // اقتصادي
    NonEconomic = 3 // غير اقتصادي
}

// حالة الحيوان
public enum AnimalStatus
{
    Healthy = 1,    // سليم
    Sick = 2,       // مريض
    UnderTreatment = 3, // تحت العلاج
    Recovered = 4,  // متعافي
    Dead = 5        // نافق
}

// نموذج الحيوان
public class Animal
{
    public int Id { get; set; }
    
    [StringLength(100, ErrorMessage = "اسم الحيوان يجب أن يكون أقل من 100 حرف")]
    public string? Name { get; set; }
    
    [Required(ErrorMessage = "عدد الحيوانات مطلوب")]
    [Range(1, int.MaxValue, ErrorMessage = "عدد الحيوانات يجب أن يكون أكبر من صفر")]
    public int Count { get; set; } = 1;
    
    [Required(ErrorMessage = "تصنيف الحيوان مطلوب")]
    public AnimalCategory Category { get; set; }
    
    public AnimalStatus Status { get; set; } = AnimalStatus.Healthy;
    
    [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
    public string? Notes { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? LastUpdated { get; set; }
    
    // المفاتيح الخارجية
    [Required(ErrorMessage = "نوع الحيوان مطلوب")]
    public int AnimalTypeId { get; set; }
    public virtual AnimalType AnimalType { get; set; } = null!;
    
    [Required(ErrorMessage = "المربي مطلوب")]
    public int BreederId { get; set; }
    public virtual Breeder Breeder { get; set; } = null!;
    
    public int BranchId { get; set; }
    public virtual Branch Branch { get; set; } = null!;
    
    // العلاقات
    public virtual ICollection<Treatment> Treatments { get; set; } = new List<Treatment>();
    public virtual ICollection<LabTest> LabTests { get; set; } = new List<LabTest>();
}
