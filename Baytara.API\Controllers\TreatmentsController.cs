using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Baytara.API.Data;
using Baytara.Shared.Models;

namespace Baytara.API.Controllers;

public class TreatmentDto
{
    public int Id { get; set; }
    public DateTime TreatmentDate { get; set; }
    public string? Symptoms { get; set; }
    public string? Diagnosis { get; set; }
    public string? TreatmentPlan { get; set; }
    public string? Notes { get; set; }
    public TreatmentStatus Status { get; set; }
    public decimal Cost { get; set; }
    public DateTime? NextVisitDate { get; set; }
    public string? VeterinarianName { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastUpdated { get; set; }
    
    public int AnimalId { get; set; }
    public string AnimalName { get; set; } = string.Empty;
    public string AnimalTypeName { get; set; } = string.Empty;
    public string BreederName { get; set; } = string.Empty;
    public string BreederPhone { get; set; } = string.Empty;
    
    public int? DiseaseId { get; set; }
    public string? DiseaseName { get; set; }
    
    public int BranchId { get; set; }
    public string BranchName { get; set; } = string.Empty;
    
    public List<TreatmentMedicineDto> Medicines { get; set; } = new();
}

public class TreatmentMedicineDto
{
    public int Id { get; set; }
    public int MedicineId { get; set; }
    public string MedicineName { get; set; } = string.Empty;
    public decimal Quantity { get; set; }
    public string? Instructions { get; set; }
    public int? DurationDays { get; set; }
    public int? DosesPerDay { get; set; }
}

public class CreateTreatmentDto
{
    public DateTime TreatmentDate { get; set; } = DateTime.UtcNow;
    public string? Symptoms { get; set; }
    public string? Diagnosis { get; set; }
    public string? TreatmentPlan { get; set; }
    public string? Notes { get; set; }
    public TreatmentStatus Status { get; set; } = TreatmentStatus.Planned;
    public decimal Cost { get; set; }
    public DateTime? NextVisitDate { get; set; }
    public string? VeterinarianName { get; set; }
    public int AnimalId { get; set; }
    public int? DiseaseId { get; set; }
    public int BranchId { get; set; }
    public List<CreateTreatmentMedicineDto> Medicines { get; set; } = new();
}

public class CreateTreatmentMedicineDto
{
    public int MedicineId { get; set; }
    public decimal Quantity { get; set; }
    public string? Instructions { get; set; }
    public int? DurationDays { get; set; }
    public int? DosesPerDay { get; set; }
}

[ApiController]
[Route("api/[controller]")]
public class TreatmentsController : ControllerBase
{
    private readonly BaytaraDbContext _context;

    public TreatmentsController(BaytaraDbContext context)
    {
        _context = context;
    }

    // GET: api/Treatments
    [HttpGet]
    public async Task<ActionResult<IEnumerable<TreatmentDto>>> GetTreatments(
        [FromQuery] string? search = null,
        [FromQuery] int? branchId = null,
        [FromQuery] int? animalId = null,
        [FromQuery] int? diseaseId = null,
        [FromQuery] TreatmentStatus? status = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10)
    {
        var query = _context.Treatments
            .Include(t => t.Animal)
                .ThenInclude(a => a.AnimalType)
            .Include(t => t.Animal)
                .ThenInclude(a => a.Breeder)
            .Include(t => t.Disease)
            .Include(t => t.Branch)
            .Include(t => t.TreatmentMedicines)
                .ThenInclude(tm => tm.Medicine)
            .AsQueryable();

        // تطبيق الفلاتر
        if (!string.IsNullOrEmpty(search))
        {
            query = query.Where(t => (t.Symptoms != null && t.Symptoms.Contains(search)) ||
                                   (t.Diagnosis != null && t.Diagnosis.Contains(search)) ||
                                   (t.VeterinarianName != null && t.VeterinarianName.Contains(search)) ||
                                   t.Animal.Breeder.Name.Contains(search));
        }

        if (branchId.HasValue)
        {
            query = query.Where(t => t.BranchId == branchId.Value);
        }

        if (animalId.HasValue)
        {
            query = query.Where(t => t.AnimalId == animalId.Value);
        }

        if (diseaseId.HasValue)
        {
            query = query.Where(t => t.DiseaseId == diseaseId.Value);
        }

        if (status.HasValue)
        {
            query = query.Where(t => t.Status == status.Value);
        }

        if (fromDate.HasValue)
        {
            query = query.Where(t => t.TreatmentDate.Date >= fromDate.Value.Date);
        }

        if (toDate.HasValue)
        {
            query = query.Where(t => t.TreatmentDate.Date <= toDate.Value.Date);
        }

        // ترتيب النتائج
        query = query.OrderByDescending(t => t.TreatmentDate);

        // تطبيق التصفح
        var totalCount = await query.CountAsync();
        var treatments = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .Select(t => new TreatmentDto
            {
                Id = t.Id,
                TreatmentDate = t.TreatmentDate,
                Symptoms = t.Symptoms,
                Diagnosis = t.Diagnosis,
                TreatmentPlan = t.TreatmentPlan,
                Notes = t.Notes,
                Status = t.Status,
                Cost = t.Cost,
                NextVisitDate = t.NextVisitDate,
                VeterinarianName = t.VeterinarianName,
                CreatedAt = t.CreatedAt,
                LastUpdated = t.LastUpdated,
                AnimalId = t.AnimalId,
                AnimalName = t.Animal.Name ?? "",
                AnimalTypeName = t.Animal.AnimalType.Name,
                BreederName = t.Animal.Breeder.Name,
                BreederPhone = t.Animal.Breeder.Phone,
                DiseaseId = t.DiseaseId,
                DiseaseName = t.Disease != null ? t.Disease.Name : null,
                BranchId = t.BranchId,
                BranchName = t.Branch.Name,
                Medicines = t.TreatmentMedicines.Select(tm => new TreatmentMedicineDto
                {
                    Id = tm.Id,
                    MedicineId = tm.MedicineId,
                    MedicineName = tm.Medicine.Name,
                    Quantity = tm.Quantity,
                    Instructions = tm.Instructions,
                    DurationDays = tm.DurationDays,
                    DosesPerDay = tm.DosesPerDay
                }).ToList()
            })
            .ToListAsync();

        Response.Headers["X-Total-Count"] = totalCount.ToString();
        Response.Headers["X-Page"] = page.ToString();
        Response.Headers["X-Page-Size"] = pageSize.ToString();

        return Ok(treatments);
    }

    // GET: api/Treatments/5
    [HttpGet("{id}")]
    public async Task<ActionResult<TreatmentDto>> GetTreatment(int id)
    {
        var treatment = await _context.Treatments
            .Include(t => t.Animal)
                .ThenInclude(a => a.AnimalType)
            .Include(t => t.Animal)
                .ThenInclude(a => a.Breeder)
            .Include(t => t.Disease)
            .Include(t => t.Branch)
            .Include(t => t.TreatmentMedicines)
                .ThenInclude(tm => tm.Medicine)
            .FirstOrDefaultAsync(t => t.Id == id);

        if (treatment == null)
        {
            return NotFound();
        }

        var treatmentDto = new TreatmentDto
        {
            Id = treatment.Id,
            TreatmentDate = treatment.TreatmentDate,
            Symptoms = treatment.Symptoms,
            Diagnosis = treatment.Diagnosis,
            TreatmentPlan = treatment.TreatmentPlan,
            Notes = treatment.Notes,
            Status = treatment.Status,
            Cost = treatment.Cost,
            NextVisitDate = treatment.NextVisitDate,
            VeterinarianName = treatment.VeterinarianName,
            CreatedAt = treatment.CreatedAt,
            LastUpdated = treatment.LastUpdated,
            AnimalId = treatment.AnimalId,
            AnimalName = treatment.Animal.Name ?? "",
            AnimalTypeName = treatment.Animal.AnimalType.Name,
            BreederName = treatment.Animal.Breeder.Name,
            BreederPhone = treatment.Animal.Breeder.Phone,
            DiseaseId = treatment.DiseaseId,
            DiseaseName = treatment.Disease?.Name,
            BranchId = treatment.BranchId,
            BranchName = treatment.Branch.Name,
            Medicines = treatment.TreatmentMedicines.Select(tm => new TreatmentMedicineDto
            {
                Id = tm.Id,
                MedicineId = tm.MedicineId,
                MedicineName = tm.Medicine.Name,
                Quantity = tm.Quantity,
                Instructions = tm.Instructions,
                DurationDays = tm.DurationDays,
                DosesPerDay = tm.DosesPerDay
            }).ToList()
        };

        return Ok(treatmentDto);
    }

    // POST: api/Treatments
    [HttpPost]
    public async Task<ActionResult<TreatmentDto>> CreateTreatment(CreateTreatmentDto createTreatmentDto)
    {
        // التحقق من وجود الحيوان
        var animal = await _context.Animals.FindAsync(createTreatmentDto.AnimalId);
        if (animal == null)
        {
            return BadRequest("الحيوان المحدد غير موجود");
        }

        // التحقق من وجود الفرع
        var branch = await _context.Branches.FindAsync(createTreatmentDto.BranchId);
        if (branch == null)
        {
            return BadRequest("الفرع المحدد غير موجود");
        }

        // التحقق من وجود المرض إذا تم تحديده
        if (createTreatmentDto.DiseaseId.HasValue)
        {
            var disease = await _context.Diseases.FindAsync(createTreatmentDto.DiseaseId.Value);
            if (disease == null)
            {
                return BadRequest("المرض المحدد غير موجود");
            }
        }

        var treatment = new Treatment
        {
            TreatmentDate = createTreatmentDto.TreatmentDate,
            Symptoms = createTreatmentDto.Symptoms,
            Diagnosis = createTreatmentDto.Diagnosis,
            TreatmentPlan = createTreatmentDto.TreatmentPlan,
            Notes = createTreatmentDto.Notes,
            Status = createTreatmentDto.Status,
            Cost = createTreatmentDto.Cost,
            NextVisitDate = createTreatmentDto.NextVisitDate,
            VeterinarianName = createTreatmentDto.VeterinarianName,
            AnimalId = createTreatmentDto.AnimalId,
            DiseaseId = createTreatmentDto.DiseaseId,
            BranchId = createTreatmentDto.BranchId,
            CreatedAt = DateTime.UtcNow
        };

        _context.Treatments.Add(treatment);
        await _context.SaveChangesAsync();

        // إضافة الأدوية
        foreach (var medicineDto in createTreatmentDto.Medicines)
        {
            var medicine = await _context.Medicines.FindAsync(medicineDto.MedicineId);
            if (medicine != null)
            {
                var treatmentMedicine = new TreatmentMedicine
                {
                    TreatmentId = treatment.Id,
                    MedicineId = medicineDto.MedicineId,
                    Quantity = medicineDto.Quantity,
                    Instructions = medicineDto.Instructions,
                    DurationDays = medicineDto.DurationDays,
                    DosesPerDay = medicineDto.DosesPerDay,
                    CreatedAt = DateTime.UtcNow
                };

                _context.TreatmentMedicines.Add(treatmentMedicine);
            }
        }

        await _context.SaveChangesAsync();

        return CreatedAtAction(nameof(GetTreatment), new { id = treatment.Id }, await GetTreatmentDto(treatment.Id));
    }

    private async Task<TreatmentDto> GetTreatmentDto(int treatmentId)
    {
        var treatment = await _context.Treatments
            .Include(t => t.Animal)
                .ThenInclude(a => a.AnimalType)
            .Include(t => t.Animal)
                .ThenInclude(a => a.Breeder)
            .Include(t => t.Disease)
            .Include(t => t.Branch)
            .Include(t => t.TreatmentMedicines)
                .ThenInclude(tm => tm.Medicine)
            .FirstAsync(t => t.Id == treatmentId);

        return new TreatmentDto
        {
            Id = treatment.Id,
            TreatmentDate = treatment.TreatmentDate,
            Symptoms = treatment.Symptoms,
            Diagnosis = treatment.Diagnosis,
            TreatmentPlan = treatment.TreatmentPlan,
            Notes = treatment.Notes,
            Status = treatment.Status,
            Cost = treatment.Cost,
            NextVisitDate = treatment.NextVisitDate,
            VeterinarianName = treatment.VeterinarianName,
            CreatedAt = treatment.CreatedAt,
            LastUpdated = treatment.LastUpdated,
            AnimalId = treatment.AnimalId,
            AnimalName = treatment.Animal.Name ?? "",
            AnimalTypeName = treatment.Animal.AnimalType.Name,
            BreederName = treatment.Animal.Breeder.Name,
            BreederPhone = treatment.Animal.Breeder.Phone,
            DiseaseId = treatment.DiseaseId,
            DiseaseName = treatment.Disease?.Name,
            BranchId = treatment.BranchId,
            BranchName = treatment.Branch.Name,
            Medicines = treatment.TreatmentMedicines.Select(tm => new TreatmentMedicineDto
            {
                Id = tm.Id,
                MedicineId = tm.MedicineId,
                MedicineName = tm.Medicine.Name,
                Quantity = tm.Quantity,
                Instructions = tm.Instructions,
                DurationDays = tm.DurationDays,
                DosesPerDay = tm.DosesPerDay
            }).ToList()
        };
    }

    // PUT: api/Treatments/5
    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateTreatment(int id, CreateTreatmentDto updateTreatmentDto)
    {
        var treatment = await _context.Treatments
            .Include(t => t.TreatmentMedicines)
            .FirstOrDefaultAsync(t => t.Id == id);

        if (treatment == null)
        {
            return NotFound();
        }

        treatment.TreatmentDate = updateTreatmentDto.TreatmentDate;
        treatment.Symptoms = updateTreatmentDto.Symptoms;
        treatment.Diagnosis = updateTreatmentDto.Diagnosis;
        treatment.TreatmentPlan = updateTreatmentDto.TreatmentPlan;
        treatment.Notes = updateTreatmentDto.Notes;
        treatment.Status = updateTreatmentDto.Status;
        treatment.Cost = updateTreatmentDto.Cost;
        treatment.NextVisitDate = updateTreatmentDto.NextVisitDate;
        treatment.VeterinarianName = updateTreatmentDto.VeterinarianName;
        treatment.DiseaseId = updateTreatmentDto.DiseaseId;
        treatment.LastUpdated = DateTime.UtcNow;

        // حذف الأدوية القديمة وإضافة الجديدة
        _context.TreatmentMedicines.RemoveRange(treatment.TreatmentMedicines);

        foreach (var medicineDto in updateTreatmentDto.Medicines)
        {
            var treatmentMedicine = new TreatmentMedicine
            {
                TreatmentId = treatment.Id,
                MedicineId = medicineDto.MedicineId,
                Quantity = medicineDto.Quantity,
                Instructions = medicineDto.Instructions,
                DurationDays = medicineDto.DurationDays,
                DosesPerDay = medicineDto.DosesPerDay,
                CreatedAt = DateTime.UtcNow
            };

            _context.TreatmentMedicines.Add(treatmentMedicine);
        }

        try
        {
            await _context.SaveChangesAsync();
        }
        catch (DbUpdateConcurrencyException)
        {
            if (!TreatmentExists(id))
            {
                return NotFound();
            }
            throw;
        }

        return NoContent();
    }

    // DELETE: api/Treatments/5
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteTreatment(int id)
    {
        var treatment = await _context.Treatments
            .Include(t => t.TreatmentMedicines)
            .Include(t => t.LabTests)
            .FirstOrDefaultAsync(t => t.Id == id);

        if (treatment == null)
        {
            return NotFound();
        }

        _context.Treatments.Remove(treatment);
        await _context.SaveChangesAsync();

        return NoContent();
    }

    private bool TreatmentExists(int id)
    {
        return _context.Treatments.Any(e => e.Id == id);
    }
}
