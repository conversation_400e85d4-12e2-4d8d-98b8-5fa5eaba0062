﻿<div style="padding: 20px 0;">
    <!-- شعار النظام -->
    <div style="text-align: center; padding: 20px 0; border-bottom: 1px solid rgba(255,255,255,0.2); margin-bottom: 20px;">
        <!-- صورة الطبيب -->
        <div style="margin-bottom: 12px;">
            <MudIcon Icon="@Icons.Material.Filled.LocalHospital"
                     Style="color: white; font-size: 48px; background: rgba(255,255,255,0.1); border-radius: 50%; padding: 12px; width: 72px; height: 72px; display: inline-flex; align-items: center; justify-content: center;" />
        </div>

        <!-- اسم النظام -->
        <MudText Typo="Typo.h5" Style="color: white; font-weight: 600; font-family: 'Cairo', sans-serif; margin-bottom: 4px;">
            بيطره
        </MudText>

        <!-- النص التحتي -->
        <MudText Typo="Typo.body2" Style="color: rgba(255,255,255,0.8); font-family: 'Cairo', sans-serif; font-size: 13px; line-height: 1.3;">
            إدارة العيادات البيطرية
        </MudText>
    </div>

    <MudNavMenu Style="color: white;">
        <!-- الرئيسية -->
        <div style="margin-bottom: 2px;">
            <MudNavLink Href="/"
                       Style="color: white; margin: 1px 12px; padding: 6px 16px; border-radius: 12px; display: flex; align-items: center; gap: 2px;"
                       ActiveClass="mud-nav-link-active-custom">
                <MudIcon Icon="@Icons.Material.Filled.Home" Style="color: white;" />
                <span style="color: white; font-weight: 500; font-size: 15px; flex: 1;">الرئيسية</span>
            </MudNavLink>
        </div>

        <!-- لوحة التحكم -->
        <div style="margin-bottom: 0px;">
            <MudNavLink Href="/dashboard"
                       Style="color: white; margin: 0px 8px; padding: 4px 12px; border-radius: 8px; display: flex; align-items: center; gap: 6px;"
                       ActiveClass="mud-nav-link-active-custom">
                <MudIcon Icon="@Icons.Material.Filled.Dashboard" Style="color: white;" />
                <span style="color: white; font-weight: 500; font-size: 14px; flex: 1;">لوحة التحكم</span>
            </MudNavLink>
        </div>

        <!-- فاصل -->
        <div style="height: 1px; background: rgba(255,255,255,0.2); margin: 8px 16px;"></div>

        <!-- إدارة المربين -->
        <div style="margin-bottom: 0px;">
            <MudNavLink Href="/breeders"
                       Style="color: white; margin: 0px 8px; padding: 4px 12px; border-radius: 8px; display: flex; align-items: center; gap: 6px;"
                       ActiveClass="mud-nav-link-active-custom">
                <MudIcon Icon="@Icons.Material.Filled.Person" Style="color: white;" />
                <span style="color: white; font-weight: 500; font-size: 14px; flex: 1;">إدارة المربين</span>
            </MudNavLink>
        </div>

        <!-- إدارة الأدوية -->
        <div style="margin-bottom: 0px;">
            <MudNavLink Href="/medicines"
                       Style="color: white; margin: 0px 8px; padding: 4px 12px; border-radius: 8px; display: flex; align-items: center; gap: 6px;"
                       ActiveClass="mud-nav-link-active-custom">
                <MudIcon Icon="@Icons.Material.Filled.Medication" Style="color: white;" />
                <span style="color: white; font-weight: 500; font-size: 14px; flex: 1;">إدارة الأدوية</span>
            </MudNavLink>
        </div>

        <!-- فاصل -->
        <div style="height: 1px; background: rgba(255,255,255,0.2); margin: 8px 16px;"></div>

        <!-- العلاجات -->
        <div style="margin-bottom: 0px;">
            <MudNavLink Href="/treatments"
                       Style="color: white; margin: 0px 8px; padding: 4px 12px; border-radius: 8px; display: flex; align-items: center; gap: 6px;"
                       ActiveClass="mud-nav-link-active-custom">
                <MudIcon Icon="@Icons.Material.Filled.MedicalServices" Style="color: white;" />
                <span style="color: white; font-weight: 500; font-size: 14px; flex: 1;">العلاجات البيطرية</span>
            </MudNavLink>
        </div>

        <!-- المختبرات -->
        <div style="margin-bottom: 0px;">
            <MudNavLink Href="/lab-tests"
                       Style="color: white; margin: 0px 8px; padding: 4px 12px; border-radius: 8px; display: flex; align-items: center; gap: 6px;"
                       ActiveClass="mud-nav-link-active-custom">
                <MudIcon Icon="@Icons.Material.Filled.Science" Style="color: white;" />
                <span style="color: white; font-weight: 500; font-size: 14px; flex: 1;">المختبرات والفحوصات</span>
            </MudNavLink>
        </div>

        <!-- فاصل -->
        <div style="height: 1px; background: rgba(255,255,255,0.2); margin: 8px 16px;"></div>

        <!-- التقارير -->
        <div style="margin-bottom: 0px;">
            <MudNavLink Href="/reports"
                       Style="color: white; margin: 0px 8px; padding: 4px 12px; border-radius: 8px; display: flex; align-items: center; gap: 6px;"
                       ActiveClass="mud-nav-link-active-custom">
                <MudIcon Icon="@Icons.Material.Filled.Assessment" Style="color: white;" />
                <span style="color: white; font-weight: 500; font-size: 14px; flex: 1;">التقارير والإحصائيات</span>
            </MudNavLink>
        </div>

        <!-- الإعدادات -->
        <MudNavGroup Text="الإعدادات والتكوين"
                    Style="color: white; margin: 2px 8px; padding: 4px 12px; border-radius: 8px; background: rgba(255,255,255,0.05);"
                    Expanded="false">
            <div style="padding-right: 16px;">
                <MudNavLink Href="/branches"
                           Style="color: rgba(255,255,255,0.9); margin: 0px 6px; padding: 3px 10px; border-radius: 6px; display: flex; align-items: center; gap: 4px;"
                           ActiveClass="mud-nav-link-active-custom">
                    <MudIcon Icon="@Icons.Material.Filled.Business" Style="color: white; font-size: 14px;" />
                    <span style="color: rgba(255,255,255,0.9); font-weight: 400; font-size: 13px; flex: 1;">إدارة الفروع</span>
                </MudNavLink>
                <MudNavLink Href="/animal-types"
                           Style="color: rgba(255,255,255,0.9); margin: 0px 6px; padding: 3px 10px; border-radius: 6px; display: flex; align-items: center; gap: 4px;"
                           ActiveClass="mud-nav-link-active-custom">
                    <MudIcon Icon="@Icons.Material.Filled.Category" Style="color: white; font-size: 14px;" />
                    <span style="color: rgba(255,255,255,0.9); font-weight: 400; font-size: 13px; flex: 1;">أنواع الحيوانات</span>
                </MudNavLink>
                <MudNavLink Href="/diseases"
                           Style="color: rgba(255,255,255,0.9); margin: 0px 6px; padding: 3px 10px; border-radius: 6px; display: flex; align-items: center; gap: 4px;"
                           ActiveClass="mud-nav-link-active-custom">
                    <MudIcon Icon="@Icons.Material.Filled.Coronavirus" Style="color: white; font-size: 14px;" />
                    <span style="color: rgba(255,255,255,0.9); font-weight: 400; font-size: 13px; flex: 1;">إدارة الأمراض</span>
                </MudNavLink>
                <MudNavLink Href="/settings"
                           Style="color: rgba(255,255,255,0.9); margin: 0px 6px; padding: 3px 10px; border-radius: 6px; display: flex; align-items: center; gap: 4px;"
                           ActiveClass="mud-nav-link-active-custom">
                    <MudIcon Icon="@Icons.Material.Filled.Tune" Style="color: white; font-size: 14px;" />
                    <span style="color: rgba(255,255,255,0.9); font-weight: 400; font-size: 13px; flex: 1;">إعدادات عامة</span>
                </MudNavLink>
            </div>
        </MudNavGroup>

        <!-- اختبار النظام -->
        <div style="margin-top: 4px;">
            <MudNavLink Href="/test"
                       Style="color: rgba(255,255,255,0.7); margin: 0px 8px; padding: 3px 12px; border-radius: 6px; background: rgba(255,255,255,0.05); display: flex; align-items: center; gap: 4px;"
                       ActiveClass="mud-nav-link-active-custom">
                <MudIcon Icon="@Icons.Material.Filled.BugReport" Style="color: white; font-size: 14px;" />
                <span style="color: rgba(255,255,255,0.7); font-weight: 400; font-size: 13px; flex: 1;">اختبار النظام</span>
            </MudNavLink>
        </div>
    </MudNavMenu>
</div>

<style>
    /* Updated styles v2.0 */
    .mud-nav-link-active-custom {
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2)) !important;
        border-radius: 12px !important;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
        transform: translateX(-2px) !important;
    }

    .mud-nav-group .mud-nav-group-header:hover {
        background-color: rgba(255, 255, 255, 0.15) !important;
        border-radius: 12px !important;
        transition: all 0.3s ease !important;
    }

    .mud-nav-link:hover {
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08)) !important;
        border-radius: 12px !important;
        transform: translateX(-2px) !important;
        transition: all 0.3s ease !important;
        box-shadow: 0 2px 6px rgba(0,0,0,0.1) !important;
    }

    .mud-nav-menu {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    }

    /* تطبيق خط Cairo على جميع النصوص */
    span, .mud-text {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    }

    .mud-nav-group {
        margin-bottom: 8px !important;
    }

    .mud-nav-group .mud-nav-group-header {
        border-radius: 12px !important;
        margin: 4px 0 !important;
    }

    /* تحسين الأيقونات */
    .mud-nav-link .mud-icon {
        margin-left: 8px !important;
    }

    /* ضمان الترتيب الصحيح للعناصر */
    .mud-nav-link {
        flex-direction: row !important;
    }

    /* تأثير النقر */
    .mud-nav-link:active {
        transform: translateX(-1px) scale(0.98) !important;
    }
</style>

@code {
    private string GetCombinedAnimalIcon()
    {
        // أيقونة مدمجة للجمل/ماعز/غنم
        return Icons.Material.Filled.Pets;
    }
}
