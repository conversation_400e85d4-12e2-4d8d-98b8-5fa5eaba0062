using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Baytara.API.Data;
using Baytara.Shared.Models;

namespace Baytara.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class MedicinesController : ControllerBase
{
    private readonly BaytaraDbContext _context;

    public MedicinesController(BaytaraDbContext context)
    {
        _context = context;
    }

    // GET: api/Medicines
    [HttpGet]
    public async Task<ActionResult<IEnumerable<Medicine>>> GetMedicines(
        [FromQuery] string? search = null,
        [FromQuery] MedicineType? type = null,
        [FromQuery] bool? isActive = null,
        [FromQuery] bool? isExpired = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10)
    {
        var query = _context.Medicines.AsQueryable();

        if (!string.IsNullOrEmpty(search))
        {
            query = query.Where(m => m.Name.Contains(search) || 
                                   (m.TradeName != null && m.TradeName.Contains(search)) ||
                                   (m.Manufacturer != null && m.Manufacturer.Contains(search)));
        }

        if (type.HasValue)
        {
            query = query.Where(m => m.Type == type.Value);
        }

        if (isActive.HasValue)
        {
            query = query.Where(m => m.IsActive == isActive.Value);
        }

        if (isExpired.HasValue)
        {
            var today = DateTime.UtcNow.Date;
            if (isExpired.Value)
            {
                query = query.Where(m => m.ExpiryDate.HasValue && m.ExpiryDate.Value.Date <= today);
            }
            else
            {
                query = query.Where(m => !m.ExpiryDate.HasValue || m.ExpiryDate.Value.Date > today);
            }
        }

        // ترتيب النتائج
        query = query.OrderBy(m => m.Name);

        // تطبيق التصفح
        var totalCount = await query.CountAsync();
        var medicines = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        Response.Headers["X-Total-Count"] = totalCount.ToString();
        Response.Headers["X-Page"] = page.ToString();
        Response.Headers["X-Page-Size"] = pageSize.ToString();

        return Ok(medicines);
    }

    // GET: api/Medicines/5
    [HttpGet("{id}")]
    public async Task<ActionResult<Medicine>> GetMedicine(int id)
    {
        var medicine = await _context.Medicines.FindAsync(id);

        if (medicine == null)
        {
            return NotFound();
        }

        return Ok(medicine);
    }

    // POST: api/Medicines
    [HttpPost]
    public async Task<ActionResult<Medicine>> CreateMedicine(Medicine medicine)
    {
        medicine.CreatedAt = DateTime.UtcNow;
        _context.Medicines.Add(medicine);
        await _context.SaveChangesAsync();

        return CreatedAtAction(nameof(GetMedicine), new { id = medicine.Id }, medicine);
    }

    // PUT: api/Medicines/5
    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateMedicine(int id, Medicine medicine)
    {
        if (id != medicine.Id)
        {
            return BadRequest();
        }

        var existingMedicine = await _context.Medicines.FindAsync(id);
        if (existingMedicine == null)
        {
            return NotFound();
        }

        existingMedicine.Name = medicine.Name;
        existingMedicine.TradeName = medicine.TradeName;
        existingMedicine.Type = medicine.Type;
        existingMedicine.Description = medicine.Description;
        existingMedicine.Manufacturer = medicine.Manufacturer;
        existingMedicine.Unit = medicine.Unit;
        existingMedicine.AvailableQuantity = medicine.AvailableQuantity;
        existingMedicine.Price = medicine.Price;
        existingMedicine.ExpiryDate = medicine.ExpiryDate;
        existingMedicine.UsageInstructions = medicine.UsageInstructions;
        existingMedicine.Warnings = medicine.Warnings;
        existingMedicine.IsActive = medicine.IsActive;

        try
        {
            await _context.SaveChangesAsync();
        }
        catch (DbUpdateConcurrencyException)
        {
            if (!MedicineExists(id))
            {
                return NotFound();
            }
            throw;
        }

        return NoContent();
    }

    // DELETE: api/Medicines/5
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteMedicine(int id)
    {
        var medicine = await _context.Medicines
            .Include(m => m.TreatmentMedicines)
            .FirstOrDefaultAsync(m => m.Id == id);

        if (medicine == null)
        {
            return NotFound();
        }

        // التحقق من وجود علاجات مرتبطة
        if (medicine.TreatmentMedicines.Any())
        {
            return BadRequest("لا يمكن حذف الدواء لوجود علاجات مرتبطة به");
        }

        _context.Medicines.Remove(medicine);
        await _context.SaveChangesAsync();

        return NoContent();
    }

    // PUT: api/Medicines/5/UpdateQuantity
    [HttpPut("{id}/UpdateQuantity")]
    public async Task<IActionResult> UpdateMedicineQuantity(int id, [FromBody] decimal newQuantity)
    {
        var medicine = await _context.Medicines.FindAsync(id);
        if (medicine == null)
        {
            return NotFound();
        }

        if (newQuantity < 0)
        {
            return BadRequest("الكمية يجب أن تكون أكبر من أو تساوي صفر");
        }

        medicine.AvailableQuantity = newQuantity;
        await _context.SaveChangesAsync();

        return NoContent();
    }

    // GET: api/Medicines/LowStock
    [HttpGet("LowStock")]
    public async Task<ActionResult<IEnumerable<Medicine>>> GetLowStockMedicines([FromQuery] decimal threshold = 10)
    {
        var medicines = await _context.Medicines
            .Where(m => m.IsActive && m.AvailableQuantity <= threshold)
            .OrderBy(m => m.AvailableQuantity)
            .ToListAsync();

        return Ok(medicines);
    }

    // GET: api/Medicines/Expired
    [HttpGet("Expired")]
    public async Task<ActionResult<IEnumerable<Medicine>>> GetExpiredMedicines()
    {
        var today = DateTime.UtcNow.Date;
        var medicines = await _context.Medicines
            .Where(m => m.IsActive && m.ExpiryDate.HasValue && m.ExpiryDate.Value.Date <= today)
            .OrderBy(m => m.ExpiryDate)
            .ToListAsync();

        return Ok(medicines);
    }

    // GET: api/Medicines/ExpiringIn/{days}
    [HttpGet("ExpiringIn/{days}")]
    public async Task<ActionResult<IEnumerable<Medicine>>> GetMedicinesExpiringIn(int days)
    {
        var targetDate = DateTime.UtcNow.Date.AddDays(days);
        var medicines = await _context.Medicines
            .Where(m => m.IsActive && m.ExpiryDate.HasValue && 
                       m.ExpiryDate.Value.Date <= targetDate && 
                       m.ExpiryDate.Value.Date > DateTime.UtcNow.Date)
            .OrderBy(m => m.ExpiryDate)
            .ToListAsync();

        return Ok(medicines);
    }

    private bool MedicineExists(int id)
    {
        return _context.Medicines.Any(e => e.Id == id);
    }
}
