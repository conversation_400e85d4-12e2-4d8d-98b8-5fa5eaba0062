@page "/breeders/edit/{id:int}"
@inject IBreederService BreederService
@inject IBranchService BranchService
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<PageTitle>تعديل المربي - نظام بيطره</PageTitle>

<MudContainer MaxWidth="MaxWidth.Medium">
    <MudPaper Class="pa-6" Elevation="3">
        <MudText Typo="Typo.h4" Color="Color.Primary" Class="mb-6">
            <MudIcon Icon="@Icons.Material.Filled.Edit" Class="ml-2" />
            تعديل بيانات المربي
        </MudText>

        @if (isLoading)
        {
            <MudProgressCircular Indeterminate="true" />
            <MudText>جاري تحميل بيانات المربي...</MudText>
        }
        else if (breeder == null)
        {
            <MudAlert Severity="Severity.Error">
                لم يتم العثور على المربي المطلوب
            </MudAlert>
        }
        else
        {
            <MudForm @ref="form" @bind-IsValid="isFormValid">
                <MudGrid>
                    <MudItem xs="12" md="6">
                        <MudTextField @bind-Value="breeder.Name" 
                                     Label="اسم المربي" 
                                     Required="true"
                                     RequiredError="اسم المربي مطلوب" />
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudTextField @bind-Value="breeder.Phone" 
                                     Label="رقم الهاتف" 
                                     Required="true"
                                     RequiredError="رقم الهاتف مطلوب" />
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudTextField @bind-Value="breeder.NationalId" 
                                     Label="الرقم المدني" />
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudSelect @bind-Value="breeder.BranchId" 
                                  Label="الفرع" 
                                  Required="true"
                                  RequiredError="الفرع مطلوب">
                            @foreach (var branch in branches)
                            {
                                <MudSelectItem Value="@branch.Id">@branch.Name</MudSelectItem>
                            }
                        </MudSelect>
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudTextField @bind-Value="breeder.Region" 
                                     Label="المنطقة" />
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudSwitch T="bool" @bind-Checked="breeder.IsActive"
                                  Label="نشط"
                                  Color="Color.Primary" />
                    </MudItem>
                    
                    <MudItem xs="12">
                        <MudTextField @bind-Value="breeder.Address" 
                                     Label="العنوان" 
                                     Lines="3" />
                    </MudItem>
                </MudGrid>
            </MudForm>

            <MudDivider Class="my-6" />

            <div class="d-flex justify-space-between">
                <MudButton Color="Color.Default" 
                          Variant="Variant.Outlined" 
                          StartIcon="@Icons.Material.Filled.ArrowBack"
                          OnClick="GoBack">
                    العودة
                </MudButton>
                
                <MudButton Color="Color.Primary" 
                          Variant="Variant.Filled" 
                          StartIcon="@Icons.Material.Filled.Save"
                          OnClick="Submit"
                          Disabled="@(!isFormValid || isSubmitting)">
                    @if (isSubmitting)
                    {
                        <MudProgressCircular Class="ml-2" Size="Size.Small" Indeterminate="true" />
                        <span>جاري الحفظ...</span>
                    }
                    else
                    {
                        <span>حفظ التغييرات</span>
                    }
                </MudButton>
            </div>
        }
    </MudPaper>
</MudContainer>

@code {
    [Parameter] public int Id { get; set; }
    
    private MudForm form = null!;
    private bool isFormValid;
    private bool isSubmitting;
    private bool isLoading = true;
    private UpdateBreederDto breeder = new();
    private List<Branch> branches = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadBranches();
        await LoadBreeder();
    }

    private async Task LoadBranches()
    {
        try
        {
            var result = await BranchService.GetBranchesAsync();
            branches = result.ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل الفروع: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadBreeder()
    {
        try
        {
            var result = await BreederService.GetBreederAsync(Id);
            if (result != null)
            {
                breeder = new UpdateBreederDto
                {
                    Name = result.Name,
                    Phone = result.Phone,
                    Address = result.Address,
                    Region = result.Region,
                    NationalId = result.NationalId,
                    BranchId = result.BranchId,
                    IsActive = result.IsActive
                };
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل بيانات المربي: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoading = false;
        }
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/breeders");
    }

    private async Task Submit()
    {
        if (!isFormValid) return;

        isSubmitting = true;
        try
        {
            await BreederService.UpdateBreederAsync(Id, breeder);
            Snackbar.Add("تم تحديث بيانات المربي بنجاح", Severity.Success);
            Navigation.NavigateTo("/breeders");
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحديث المربي: {ex.Message}", Severity.Error);
        }
        finally
        {
            isSubmitting = false;
        }
    }
}
