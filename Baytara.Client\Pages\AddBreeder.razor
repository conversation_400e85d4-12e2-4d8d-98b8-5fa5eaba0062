@page "/breeders/add"
@inject IBreederService BreederService
@inject IBranchService BranchService
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<PageTitle>إضافة مربي جديد - نظام بيطره</PageTitle>

<MudContainer MaxWidth="MaxWidth.Medium">
    <MudPaper Class="pa-6" Elevation="3">
        <MudText Typo="Typo.h4" Color="Color.Primary" Class="mb-6">
            <MudIcon Icon="@Icons.Material.Filled.PersonAdd" Class="ml-2" />
            إضافة مربي جديد
        </MudText>

        <MudForm @ref="form" @bind-IsValid="@isFormValid">
            <MudGrid>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="breeder.Name"
                                 Label="اسم المربي"
                                 Required="true"
                                 RequiredError="اسم المربي مطلوب"
                                 MaxLength="100" />
                </MudItem>
                
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="breeder.Phone"
                                 Label="رقم الهاتف"
                                 Required="true"
                                 RequiredError="رقم الهاتف مطلوب"
                                 MaxLength="20" />
                </MudItem>
                
                <MudItem xs="12">
                    <MudTextField @bind-Value="breeder.Address"
                                 Label="العنوان"
                                 Lines="2"
                                 MaxLength="200" />
                </MudItem>
                
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="breeder.Region"
                                 Label="المنطقة"
                                 MaxLength="100" />
                </MudItem>
                
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="breeder.NationalId"
                                 Label="رقم الهوية"
                                 MaxLength="50" />
                </MudItem>
                
                <MudItem xs="12">
                    <MudSelect T="int" @bind-Value="breeder.BranchId"
                              Label="الفرع"
                              Required="true"
                              RequiredError="الفرع مطلوب">
                        @foreach (var branch in branches)
                        {
                            <MudSelectItem T="int" Value="@branch.Id">@branch.Name</MudSelectItem>
                        }
                    </MudSelect>
                </MudItem>
                
                <MudItem xs="12" Class="text-center mt-6">
                    <MudButton Variant="Variant.Outlined" 
                              Color="Color.Secondary" 
                              StartIcon="@Icons.Material.Filled.ArrowBack"
                              OnClick="GoBack"
                              Class="ml-4">
                        العودة
                    </MudButton>
                    
                    <MudButton Color="Color.Primary" 
                              Variant="Variant.Filled" 
                              StartIcon="@Icons.Material.Filled.Save"
                              OnClick="Submit"
                              Disabled="@(!isFormValid || isSubmitting)">
                        @if (isSubmitting)
                        {
                            <MudProgressCircular Class="ml-2" Size="Size.Small" Indeterminate="true" />
                            <span>جاري الحفظ...</span>
                        }
                        else
                        {
                            <span>حفظ</span>
                        }
                    </MudButton>
                </MudItem>
            </MudGrid>
        </MudForm>
    </MudPaper>
</MudContainer>

@code {
    private MudForm form = null!;
    private bool isFormValid;
    private bool isSubmitting;
    private CreateBreederDto breeder = new();
    private List<Branch> branches = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadBranches();
    }

    private async Task LoadBranches()
    {
        try
        {
            branches = (await BranchService.GetBranchesAsync(isActive: true)).ToList();

            // تعيين الفرع الأول كافتراضي إذا كان متوفراً
            if (branches.Any() && breeder.BranchId == 0)
            {
                breeder.BranchId = branches.First().Id;
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل الفروع: {ex.Message}", Severity.Error);
        }
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/breeders");
    }

    private async Task Submit()
    {
        if (!isFormValid) return;

        isSubmitting = true;
        try
        {
            await BreederService.CreateBreederAsync(breeder);
            Snackbar.Add("تم إضافة المربي بنجاح", Severity.Success);
            Navigation.NavigateTo("/breeders");
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في إضافة المربي: {ex.Message}", Severity.Error);
        }
        finally
        {
            isSubmitting = false;
        }
    }
}
