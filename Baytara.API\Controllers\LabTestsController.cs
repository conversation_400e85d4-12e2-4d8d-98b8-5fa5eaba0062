using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Baytara.API.Data;
using Baytara.Shared.Models;

namespace Baytara.API.Controllers;

public class LabTestDto
{
    public int Id { get; set; }
    public LabTestType Type { get; set; }
    public string TestName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public DateTime RequestDate { get; set; }
    public DateTime? SampleCollectionDate { get; set; }
    public DateTime? ResultDate { get; set; }
    public LabTestStatus Status { get; set; }
    public string? Results { get; set; }
    public string? Interpretation { get; set; }
    public string? Notes { get; set; }
    public decimal Cost { get; set; }
    public string? LabName { get; set; }
    public string? TechnicianName { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastUpdated { get; set; }
    
    public int AnimalId { get; set; }
    public string AnimalName { get; set; } = string.Empty;
    public string AnimalTypeName { get; set; } = string.Empty;
    public string BreederName { get; set; } = string.Empty;
    public string BreederPhone { get; set; } = string.Empty;
    
    public int? TreatmentId { get; set; }
    public string? TreatmentInfo { get; set; }
    
    public int BranchId { get; set; }
    public string BranchName { get; set; } = string.Empty;
}

public class CreateLabTestDto
{
    public LabTestType Type { get; set; }
    public string TestName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public DateTime RequestDate { get; set; } = DateTime.UtcNow;
    public DateTime? SampleCollectionDate { get; set; }
    public DateTime? ResultDate { get; set; }
    public LabTestStatus Status { get; set; } = LabTestStatus.Pending;
    public string? Results { get; set; }
    public string? Interpretation { get; set; }
    public string? Notes { get; set; }
    public decimal Cost { get; set; }
    public string? LabName { get; set; }
    public string? TechnicianName { get; set; }
    public int AnimalId { get; set; }
    public int? TreatmentId { get; set; }
    public int BranchId { get; set; }
}

[ApiController]
[Route("api/[controller]")]
public class LabTestsController : ControllerBase
{
    private readonly BaytaraDbContext _context;

    public LabTestsController(BaytaraDbContext context)
    {
        _context = context;
    }

    // GET: api/LabTests
    [HttpGet]
    public async Task<ActionResult<IEnumerable<LabTestDto>>> GetLabTests(
        [FromQuery] string? search = null,
        [FromQuery] int? branchId = null,
        [FromQuery] int? animalId = null,
        [FromQuery] int? treatmentId = null,
        [FromQuery] LabTestType? type = null,
        [FromQuery] LabTestStatus? status = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10)
    {
        var query = _context.LabTests
            .Include(lt => lt.Animal)
                .ThenInclude(a => a.AnimalType)
            .Include(lt => lt.Animal)
                .ThenInclude(a => a.Breeder)
            .Include(lt => lt.Treatment)
            .Include(lt => lt.Branch)
            .AsQueryable();

        // تطبيق الفلاتر
        if (!string.IsNullOrEmpty(search))
        {
            query = query.Where(lt => lt.TestName.Contains(search) ||
                                    (lt.Description != null && lt.Description.Contains(search)) ||
                                    (lt.LabName != null && lt.LabName.Contains(search)) ||
                                    (lt.TechnicianName != null && lt.TechnicianName.Contains(search)) ||
                                    lt.Animal.Breeder.Name.Contains(search));
        }

        if (branchId.HasValue)
        {
            query = query.Where(lt => lt.BranchId == branchId.Value);
        }

        if (animalId.HasValue)
        {
            query = query.Where(lt => lt.AnimalId == animalId.Value);
        }

        if (treatmentId.HasValue)
        {
            query = query.Where(lt => lt.TreatmentId == treatmentId.Value);
        }

        if (type.HasValue)
        {
            query = query.Where(lt => lt.Type == type.Value);
        }

        if (status.HasValue)
        {
            query = query.Where(lt => lt.Status == status.Value);
        }

        if (fromDate.HasValue)
        {
            query = query.Where(lt => lt.RequestDate.Date >= fromDate.Value.Date);
        }

        if (toDate.HasValue)
        {
            query = query.Where(lt => lt.RequestDate.Date <= toDate.Value.Date);
        }

        // ترتيب النتائج
        query = query.OrderByDescending(lt => lt.RequestDate);

        // تطبيق التصفح
        var totalCount = await query.CountAsync();
        var labTests = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .Select(lt => new LabTestDto
            {
                Id = lt.Id,
                Type = lt.Type,
                TestName = lt.TestName,
                Description = lt.Description,
                RequestDate = lt.RequestDate,
                SampleCollectionDate = lt.SampleCollectionDate,
                ResultDate = lt.ResultDate,
                Status = lt.Status,
                Results = lt.Results,
                Interpretation = lt.Interpretation,
                Notes = lt.Notes,
                Cost = lt.Cost,
                LabName = lt.LabName,
                TechnicianName = lt.TechnicianName,
                CreatedAt = lt.CreatedAt,
                LastUpdated = lt.LastUpdated,
                AnimalId = lt.AnimalId,
                AnimalName = lt.Animal.Name ?? "",
                AnimalTypeName = lt.Animal.AnimalType.Name,
                BreederName = lt.Animal.Breeder.Name,
                BreederPhone = lt.Animal.Breeder.Phone,
                TreatmentId = lt.TreatmentId,
                TreatmentInfo = lt.Treatment != null ? $"علاج بتاريخ {lt.Treatment.TreatmentDate:yyyy/MM/dd}" : null,
                BranchId = lt.BranchId,
                BranchName = lt.Branch.Name
            })
            .ToListAsync();

        Response.Headers["X-Total-Count"] = totalCount.ToString();
        Response.Headers["X-Page"] = page.ToString();
        Response.Headers["X-Page-Size"] = pageSize.ToString();

        return Ok(labTests);
    }

    // GET: api/LabTests/5
    [HttpGet("{id}")]
    public async Task<ActionResult<LabTestDto>> GetLabTest(int id)
    {
        var labTest = await _context.LabTests
            .Include(lt => lt.Animal)
                .ThenInclude(a => a.AnimalType)
            .Include(lt => lt.Animal)
                .ThenInclude(a => a.Breeder)
            .Include(lt => lt.Treatment)
            .Include(lt => lt.Branch)
            .FirstOrDefaultAsync(lt => lt.Id == id);

        if (labTest == null)
        {
            return NotFound();
        }

        var labTestDto = new LabTestDto
        {
            Id = labTest.Id,
            Type = labTest.Type,
            TestName = labTest.TestName,
            Description = labTest.Description,
            RequestDate = labTest.RequestDate,
            SampleCollectionDate = labTest.SampleCollectionDate,
            ResultDate = labTest.ResultDate,
            Status = labTest.Status,
            Results = labTest.Results,
            Interpretation = labTest.Interpretation,
            Notes = labTest.Notes,
            Cost = labTest.Cost,
            LabName = labTest.LabName,
            TechnicianName = labTest.TechnicianName,
            CreatedAt = labTest.CreatedAt,
            LastUpdated = labTest.LastUpdated,
            AnimalId = labTest.AnimalId,
            AnimalName = labTest.Animal.Name ?? "",
            AnimalTypeName = labTest.Animal.AnimalType.Name,
            BreederName = labTest.Animal.Breeder.Name,
            BreederPhone = labTest.Animal.Breeder.Phone,
            TreatmentId = labTest.TreatmentId,
            TreatmentInfo = labTest.Treatment != null ? $"علاج بتاريخ {labTest.Treatment.TreatmentDate:yyyy/MM/dd}" : null,
            BranchId = labTest.BranchId,
            BranchName = labTest.Branch.Name
        };

        return Ok(labTestDto);
    }

    // POST: api/LabTests
    [HttpPost]
    public async Task<ActionResult<LabTestDto>> CreateLabTest(CreateLabTestDto createLabTestDto)
    {
        // التحقق من وجود الحيوان
        var animal = await _context.Animals.FindAsync(createLabTestDto.AnimalId);
        if (animal == null)
        {
            return BadRequest("الحيوان المحدد غير موجود");
        }

        // التحقق من وجود الفرع
        var branch = await _context.Branches.FindAsync(createLabTestDto.BranchId);
        if (branch == null)
        {
            return BadRequest("الفرع المحدد غير موجود");
        }

        // التحقق من وجود العلاج إذا تم تحديده
        if (createLabTestDto.TreatmentId.HasValue)
        {
            var treatment = await _context.Treatments.FindAsync(createLabTestDto.TreatmentId.Value);
            if (treatment == null)
            {
                return BadRequest("العلاج المحدد غير موجود");
            }
        }

        var labTest = new LabTest
        {
            Type = createLabTestDto.Type,
            TestName = createLabTestDto.TestName,
            Description = createLabTestDto.Description,
            RequestDate = createLabTestDto.RequestDate,
            SampleCollectionDate = createLabTestDto.SampleCollectionDate,
            ResultDate = createLabTestDto.ResultDate,
            Status = createLabTestDto.Status,
            Results = createLabTestDto.Results,
            Interpretation = createLabTestDto.Interpretation,
            Notes = createLabTestDto.Notes,
            Cost = createLabTestDto.Cost,
            LabName = createLabTestDto.LabName,
            TechnicianName = createLabTestDto.TechnicianName,
            AnimalId = createLabTestDto.AnimalId,
            TreatmentId = createLabTestDto.TreatmentId,
            BranchId = createLabTestDto.BranchId,
            CreatedAt = DateTime.UtcNow
        };

        _context.LabTests.Add(labTest);
        await _context.SaveChangesAsync();

        return CreatedAtAction(nameof(GetLabTest), new { id = labTest.Id }, await GetLabTestDto(labTest.Id));
    }

    // PUT: api/LabTests/5
    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateLabTest(int id, CreateLabTestDto updateLabTestDto)
    {
        var labTest = await _context.LabTests.FindAsync(id);
        if (labTest == null)
        {
            return NotFound();
        }

        labTest.Type = updateLabTestDto.Type;
        labTest.TestName = updateLabTestDto.TestName;
        labTest.Description = updateLabTestDto.Description;
        labTest.RequestDate = updateLabTestDto.RequestDate;
        labTest.SampleCollectionDate = updateLabTestDto.SampleCollectionDate;
        labTest.ResultDate = updateLabTestDto.ResultDate;
        labTest.Status = updateLabTestDto.Status;
        labTest.Results = updateLabTestDto.Results;
        labTest.Interpretation = updateLabTestDto.Interpretation;
        labTest.Notes = updateLabTestDto.Notes;
        labTest.Cost = updateLabTestDto.Cost;
        labTest.LabName = updateLabTestDto.LabName;
        labTest.TechnicianName = updateLabTestDto.TechnicianName;
        labTest.TreatmentId = updateLabTestDto.TreatmentId;
        labTest.LastUpdated = DateTime.UtcNow;

        try
        {
            await _context.SaveChangesAsync();
        }
        catch (DbUpdateConcurrencyException)
        {
            if (!LabTestExists(id))
            {
                return NotFound();
            }
            throw;
        }

        return NoContent();
    }

    // DELETE: api/LabTests/5
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteLabTest(int id)
    {
        var labTest = await _context.LabTests.FindAsync(id);
        if (labTest == null)
        {
            return NotFound();
        }

        _context.LabTests.Remove(labTest);
        await _context.SaveChangesAsync();

        return NoContent();
    }

    private async Task<LabTestDto> GetLabTestDto(int labTestId)
    {
        var labTest = await _context.LabTests
            .Include(lt => lt.Animal)
                .ThenInclude(a => a.AnimalType)
            .Include(lt => lt.Animal)
                .ThenInclude(a => a.Breeder)
            .Include(lt => lt.Treatment)
            .Include(lt => lt.Branch)
            .FirstAsync(lt => lt.Id == labTestId);

        return new LabTestDto
        {
            Id = labTest.Id,
            Type = labTest.Type,
            TestName = labTest.TestName,
            Description = labTest.Description,
            RequestDate = labTest.RequestDate,
            SampleCollectionDate = labTest.SampleCollectionDate,
            ResultDate = labTest.ResultDate,
            Status = labTest.Status,
            Results = labTest.Results,
            Interpretation = labTest.Interpretation,
            Notes = labTest.Notes,
            Cost = labTest.Cost,
            LabName = labTest.LabName,
            TechnicianName = labTest.TechnicianName,
            CreatedAt = labTest.CreatedAt,
            LastUpdated = labTest.LastUpdated,
            AnimalId = labTest.AnimalId,
            AnimalName = labTest.Animal.Name ?? "",
            AnimalTypeName = labTest.Animal.AnimalType.Name,
            BreederName = labTest.Animal.Breeder.Name,
            BreederPhone = labTest.Animal.Breeder.Phone,
            TreatmentId = labTest.TreatmentId,
            TreatmentInfo = labTest.Treatment != null ? $"علاج بتاريخ {labTest.Treatment.TreatmentDate:yyyy/MM/dd}" : null,
            BranchId = labTest.BranchId,
            BranchName = labTest.Branch.Name
        };
    }

    private bool LabTestExists(int id)
    {
        return _context.LabTests.Any(e => e.Id == id);
    }
}
