@page "/medicines"
@inject IMedicineService MedicineService
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<PageTitle>إدارة الأدوية - نظام بيطره</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge">
    <!-- العنوان وشريط البحث -->
    <MudPaper Class="pa-4 mb-4" Elevation="2">
        <MudGrid AlignItems="Center">
            <MudItem xs="12" md="6">
                <MudText Typo="Typo.h4" Color="Color.Primary">
                    <MudIcon Icon="@Icons.Material.Filled.Medication" Class="ml-2" />
                    💉 مخزن الأدوية البيطرية
                </MudText>
                <MudText Typo="Typo.body1" Class="mt-2">
                    إدارة شاملة للأدوية والمستلزمات البيطرية
                </MudText>
            </MudItem>
            <MudItem xs="12" md="6" Class="text-left">
                <MudButton Variant="Variant.Filled" 
                          Color="Color.Primary" 
                          StartIcon="@Icons.Material.Filled.Add"
                          Href="/medicines/add">
                    إضافة دواء جديد
                </MudButton>
            </MudItem>
        </MudGrid>
        
        <!-- شريط البحث والفلاتر -->
        <MudGrid Class="mt-4">
            <MudItem xs="12" md="4">
                <MudTextField @bind-Value="searchText" 
                             Label="البحث" 
                             Placeholder="البحث بالاسم أو الشركة المصنعة"
                             Adornment="Adornment.Start" 
                             AdornmentIcon="@Icons.Material.Filled.Search"
                             OnKeyUp="@(async (e) => { if (e.Key == "Enter") await SearchMedicines(); })" />
            </MudItem>
            <MudItem xs="12" md="3">
                <MudSelect @bind-Value="selectedType" Label="نوع الدواء" Clearable="true">
                    <MudSelectItem Value="@MedicineType.Antibiotic">مضاد حيوي</MudSelectItem>
                    <MudSelectItem Value="@MedicineType.Vaccine">لقاح</MudSelectItem>
                    <MudSelectItem Value="@MedicineType.Vitamin">فيتامين</MudSelectItem>
                    <MudSelectItem Value="@MedicineType.Painkiller">مسكن</MudSelectItem>
                    <MudSelectItem Value="@MedicineType.AntiInflammatory">مضاد التهاب</MudSelectItem>
                    <MudSelectItem Value="@MedicineType.Other">أخرى</MudSelectItem>
                </MudSelect>
            </MudItem>
            <MudItem xs="12" md="2">
                <MudSwitch @bind-Value="showExpiredOnly" Label="منتهية الصلاحية فقط" Color="Color.Warning" />
            </MudItem>
            <MudItem xs="12" md="2">
                <MudSwitch @bind-Value="showLowStockOnly" Label="مخزون منخفض فقط" Color="Color.Error" />
            </MudItem>
            <MudItem xs="12" md="1">
                <MudButton Variant="Variant.Filled" 
                          Color="Color.Secondary" 
                          FullWidth="true"
                          OnClick="SearchMedicines">
                    بحث
                </MudButton>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- جدول الأدوية -->
    <MudPaper Class="pa-4" Elevation="2">
        @if (isLoading)
        {
            <div class="text-center pa-8">
                <MudProgressCircular Indeterminate="true" />
                <MudText Class="mt-4">جاري تحميل البيانات...</MudText>
            </div>
        }
        else if (!medicines.Any())
        {
            <div class="text-center pa-8">
                <MudIcon Icon="@Icons.Material.Filled.SearchOff" Size="Size.Large" Color="Color.Secondary" />
                <MudText Typo="Typo.h6" Class="mt-4">لا توجد نتائج</MudText>
                <MudText>لم يتم العثور على أدوية مطابقة لمعايير البحث</MudText>
            </div>
        }
        else
        {
            <MudTable Items="@medicines" Hover="true" Striped="true" Dense="true">
                <HeaderContent>
                    <MudTh>الاسم</MudTh>
                    <MudTh>الاسم التجاري</MudTh>
                    <MudTh>النوع</MudTh>
                    <MudTh>الكمية المتوفرة</MudTh>
                    <MudTh>السعر (ر.ع)</MudTh>
                    <MudTh>تاريخ الانتهاء</MudTh>
                    <MudTh>الحالة</MudTh>
                    <MudTh>الإجراءات</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd DataLabel="الاسم">
                        <MudText Typo="Typo.body2" Style="font-weight: 500;">@context.Name</MudText>
                    </MudTd>
                    <MudTd DataLabel="الاسم التجاري">@(context.TradeName ?? "-")</MudTd>
                    <MudTd DataLabel="النوع">@GetMedicineTypeText(context.Type)</MudTd>
                    <MudTd DataLabel="الكمية المتوفرة">
                        <MudChip T="string" Size="Size.Small" 
                                Color="@GetQuantityColor(context.AvailableQuantity)">
                            @context.AvailableQuantity @GetUnitText(context.Unit)
                        </MudChip>
                    </MudTd>
                    <MudTd DataLabel="السعر">@context.Price.ToString("F2")</MudTd>
                    <MudTd DataLabel="تاريخ الانتهاء">
                        @if (context.ExpiryDate.HasValue)
                        {
                            <MudText Color="@GetExpiryColor(context.ExpiryDate.Value)">
                                @context.ExpiryDate.Value.ToString("yyyy/MM/dd")
                            </MudText>
                        }
                        else
                        {
                            <MudText>-</MudText>
                        }
                    </MudTd>
                    <MudTd DataLabel="الحالة">
                        <MudChip T="string" Size="Size.Small" 
                                Color="@(context.IsActive ? Color.Success : Color.Error)">
                            @(context.IsActive ? "نشط" : "غير نشط")
                        </MudChip>
                    </MudTd>
                    <MudTd DataLabel="الإجراءات">
                        <MudButtonGroup Variant="Variant.Text" Size="Size.Small">
                            <MudIconButton Icon="@Icons.Material.Filled.Edit" 
                                          Color="Color.Primary" 
                                          Size="Size.Small"
                                          Href="@($"/medicines/edit/{context.Id}")" />
                            <MudIconButton Icon="@Icons.Material.Filled.Delete" 
                                          Color="Color.Error" 
                                          Size="Size.Small"
                                          OnClick="@(() => DeleteMedicine(context.Id))" />
                        </MudButtonGroup>
                    </MudTd>
                </RowTemplate>
            </MudTable>

            <!-- التصفح -->
            <div class="d-flex justify-center mt-4">
                <MudPagination Count="@totalPages" 
                              Selected="@currentPage" 
                              SelectedChanged="@OnPageChanged" 
                              ShowFirstButton="true" 
                              ShowLastButton="true" />
            </div>
        }
    </MudPaper>
</MudContainer>

@code {
    private List<Medicine> medicines = new();
    private bool isLoading = true;
    private string searchText = "";
    private MedicineType? selectedType = null;
    private bool showExpiredOnly = false;
    private bool showLowStockOnly = false;
    private int currentPage = 1;
    private int pageSize = 10;
    private int totalPages = 1;

    protected override async Task OnInitializedAsync()
    {
        await LoadMedicines();
    }

    private async Task LoadMedicines()
    {
        isLoading = true;
        try
        {
            var result = await MedicineService.GetMedicinesAsync(
                search: string.IsNullOrEmpty(searchText) ? null : searchText,
                isActive: true,
                page: currentPage,
                pageSize: pageSize
            );
            
            medicines = result.ToList();
            
            // تطبيق الفلاتر المحلية
            if (selectedType.HasValue)
            {
                medicines = medicines.Where(m => m.Type == selectedType.Value).ToList();
            }
            
            if (showExpiredOnly)
            {
                var today = DateTime.UtcNow.Date;
                medicines = medicines.Where(m => m.ExpiryDate.HasValue && m.ExpiryDate.Value.Date <= today).ToList();
            }
            
            if (showLowStockOnly)
            {
                medicines = medicines.Where(m => m.AvailableQuantity <= 10).ToList();
            }
            
            totalPages = Math.Max(1, (int)Math.Ceiling(medicines.Count / (double)pageSize));
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل الأدوية: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task SearchMedicines()
    {
        currentPage = 1;
        await LoadMedicines();
    }

    private async Task OnPageChanged(int page)
    {
        currentPage = page;
        await LoadMedicines();
    }

    private async Task DeleteMedicine(int medicineId)
    {
        try
        {
            await MedicineService.DeleteMedicineAsync(medicineId);
            await LoadMedicines();
            Snackbar.Add("تم حذف الدواء بنجاح", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في حذف الدواء: {ex.Message}", Severity.Error);
        }
    }

    private string GetMedicineTypeText(MedicineType type)
    {
        return type switch
        {
            MedicineType.Antibiotic => "مضاد حيوي",
            MedicineType.Vaccine => "لقاح",
            MedicineType.Vitamin => "فيتامين",
            MedicineType.Painkiller => "مسكن",
            MedicineType.AntiInflammatory => "مضاد التهاب",
            MedicineType.Other => "أخرى",
            _ => "غير محدد"
        };
    }

    private string GetUnitText(MeasurementUnit unit)
    {
        return unit switch
        {
            MeasurementUnit.ML => "مل",
            MeasurementUnit.MG => "مج",
            MeasurementUnit.Tablet => "قرص",
            MeasurementUnit.Capsule => "كبسولة",
            MeasurementUnit.Injection => "حقنة",
            MeasurementUnit.Dose => "جرعة",
            _ => ""
        };
    }

    private Color GetQuantityColor(decimal quantity)
    {
        if (quantity <= 5) return Color.Error;
        if (quantity <= 10) return Color.Warning;
        return Color.Success;
    }

    private Color GetExpiryColor(DateTime expiryDate)
    {
        var today = DateTime.UtcNow.Date;
        var daysUntilExpiry = (expiryDate.Date - today).Days;
        
        if (daysUntilExpiry <= 0) return Color.Error;
        if (daysUntilExpiry <= 30) return Color.Warning;
        return Color.Default;
    }
}
