﻿using System.ComponentModel.DataAnnotations;

namespace Baytara.Shared.Models;

// نموذج الفرع
public class Branch
{
    public int Id { get; set; }

    [Required(ErrorMessage = "اسم الفرع مطلوب")]
    [StringLength(100, ErrorMessage = "اسم الفرع يجب أن يكون أقل من 100 حرف")]
    public string Name { get; set; } = string.Empty;

    [StringLength(200, ErrorMessage = "العنوان يجب أن يكون أقل من 200 حرف")]
    public string? Address { get; set; }

    [Phone(ErrorMessage = "رقم الهاتف غير صحيح")]
    public string? Phone { get; set; }

    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // العلاقات
    public virtual ICollection<Breeder> Breeders { get; set; } = new List<Breeder>();
    public virtual ICollection<Animal> Animals { get; set; } = new List<Animal>();
    public virtual ICollection<Treatment> Treatments { get; set; } = new List<Treatment>();
}

// نموذج المربي
public class Breeder
{
    public int Id { get; set; }

    [Required(ErrorMessage = "اسم المربي مطلوب")]
    [StringLength(100, ErrorMessage = "الاسم يجب أن يكون أقل من 100 حرف")]
    public string Name { get; set; } = string.Empty;

    [Required(ErrorMessage = "رقم الهاتف مطلوب")]
    [Phone(ErrorMessage = "رقم الهاتف غير صحيح")]
    public string Phone { get; set; } = string.Empty;

    [StringLength(200, ErrorMessage = "العنوان يجب أن يكون أقل من 200 حرف")]
    public string? Address { get; set; }

    [StringLength(100, ErrorMessage = "المنطقة يجب أن تكون أقل من 100 حرف")]
    public string? Region { get; set; }

    [StringLength(50, ErrorMessage = "رقم الهوية يجب أن يكون أقل من 50 حرف")]
    public string? NationalId { get; set; }

    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // مفتاح خارجي للفرع
    public int BranchId { get; set; }
    public virtual Branch Branch { get; set; } = null!;

    // العلاقات
    public virtual ICollection<Animal> Animals { get; set; } = new List<Animal>();
}

// نموذج نوع الحيوان
public class AnimalType
{
    public int Id { get; set; }

    [Required(ErrorMessage = "اسم نوع الحيوان مطلوب")]
    [StringLength(50, ErrorMessage = "اسم النوع يجب أن يكون أقل من 50 حرف")]
    public string Name { get; set; } = string.Empty;

    [StringLength(200, ErrorMessage = "الوصف يجب أن يكون أقل من 200 حرف")]
    public string? Description { get; set; }

    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // العلاقات
    public virtual ICollection<Animal> Animals { get; set; } = new List<Animal>();
}
