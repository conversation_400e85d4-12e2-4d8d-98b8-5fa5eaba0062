@page "/animal-types"
@inject IAnimalTypeService AnimalTypeService
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<PageTitle>إدارة أنواع الحيوانات - نظام بيطارة</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large">
    <!-- العنوان وشريط البحث -->
    <MudPaper Class="pa-4 mb-4" Elevation="2">
        <MudGrid AlignItems="Center">
            <MudItem xs="12" md="6">
                <MudText Typo="Typo.h4" Color="Color.Primary">
                    <MudIcon Icon="@Icons.Material.Filled.Category" Class="ml-2" />
                    إدارة أنواع الحيوانات
                </MudText>
            </MudItem>
            <MudItem xs="12" md="6" Class="text-left">
                <MudButton Variant="Variant.Filled" 
                          Color="Color.Primary" 
                          StartIcon="@Icons.Material.Filled.Add"
                          OnClick="OpenAddDialog">
                    إضافة نوع جديد
                </MudButton>
            </MudItem>
        </MudGrid>
        
        <!-- شريط البحث -->
        <MudGrid Class="mt-4">
            <MudItem xs="12" md="8">
                <MudTextField @bind-Value="searchText" 
                             Label="البحث" 
                             Placeholder="البحث بالاسم أو الوصف"
                             Adornment="Adornment.Start" 
                             AdornmentIcon="@Icons.Material.Filled.Search"
                             OnKeyUp="@(async (e) => { if (e.Key == "Enter") await SearchAnimalTypes(); })" />
            </MudItem>
            <MudItem xs="12" md="2">
                <MudSwitch @bind-Value="showActiveOnly" Label="النشطة فقط" Color="Color.Primary" />
            </MudItem>
            <MudItem xs="12" md="2">
                <MudButton Variant="Variant.Filled" 
                          Color="Color.Secondary" 
                          FullWidth="true"
                          OnClick="SearchAnimalTypes">
                    بحث
                </MudButton>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- جدول أنواع الحيوانات -->
    <MudPaper Class="pa-4" Elevation="2">
        @if (isLoading)
        {
            <div class="text-center pa-8">
                <MudProgressCircular Indeterminate="true" />
                <MudText Class="mt-4">جاري تحميل البيانات...</MudText>
            </div>
        }
        else if (!animalTypes.Any())
        {
            <div class="text-center pa-8">
                <MudIcon Icon="@Icons.Material.Filled.SearchOff" Size="Size.Large" Color="Color.Secondary" />
                <MudText Typo="Typo.h6" Class="mt-4">لا توجد نتائج</MudText>
                <MudText>لم يتم العثور على أنواع حيوانات مطابقة لمعايير البحث</MudText>
            </div>
        }
        else
        {
            <MudTable Items="@animalTypes" Hover="true" Striped="true" Dense="true">
                <HeaderContent>
                    <MudTh>الاسم</MudTh>
                    <MudTh>الوصف</MudTh>
                    <MudTh>الحالة</MudTh>
                    <MudTh>تاريخ الإضافة</MudTh>
                    <MudTh>الإجراءات</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd DataLabel="الاسم">
                        <MudText Typo="Typo.body2" Style="font-weight: 500;">@context.Name</MudText>
                    </MudTd>
                    <MudTd DataLabel="الوصف">
                        @(context.Description ?? "-")
                    </MudTd>
                    <MudTd DataLabel="الحالة">
                        <MudChip T="string" Size="Size.Small" 
                                Color="@(context.IsActive ? Color.Success : Color.Error)">
                            @(context.IsActive ? "نشط" : "غير نشط")
                        </MudChip>
                    </MudTd>
                    <MudTd DataLabel="تاريخ الإضافة">
                        @context.CreatedAt.ToString("yyyy/MM/dd")
                    </MudTd>
                    <MudTd DataLabel="الإجراءات">
                        <MudButtonGroup Variant="Variant.Text" Size="Size.Small">
                            <MudIconButton Icon="@Icons.Material.Filled.Edit" 
                                          Color="Color.Primary" 
                                          Size="Size.Small"
                                          OnClick="@(() => OpenEditDialog(context))"
                                          Title="تعديل" />
                            <MudIconButton Icon="@Icons.Material.Filled.Delete" 
                                          Color="Color.Error" 
                                          Size="Size.Small"
                                          OnClick="@(() => DeleteAnimalType(context.Id))"
                                          Title="حذف" />
                        </MudButtonGroup>
                    </MudTd>
                </RowTemplate>
            </MudTable>
        }
    </MudPaper>
</MudContainer>

<!-- حوار إضافة/تعديل نوع الحيوان -->
<MudDialog @bind-IsVisible="showDialog" Options="dialogOptions">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.Category" Class="ml-2" />
            @(isEditMode ? "تعديل نوع الحيوان" : "إضافة نوع حيوان جديد")
        </MudText>
    </TitleContent>
    <DialogContent>
        <MudForm @ref="form" @bind-IsValid="@isFormValid">
            <MudGrid>
                <MudItem xs="12">
                    <MudTextField @bind-Value="animalTypeModel.Name"
                                 Label="اسم نوع الحيوان"
                                 Required="true"
                                 RequiredError="اسم نوع الحيوان مطلوب"
                                 MaxLength="50" />
                </MudItem>
                
                <MudItem xs="12">
                    <MudTextField @bind-Value="animalTypeModel.Description"
                                 Label="الوصف"
                                 Lines="3"
                                 MaxLength="200" />
                </MudItem>
                
                @if (isEditMode)
                {
                    <MudItem xs="12">
                        <MudSwitch @bind-Value="animalTypeModel.IsActive" 
                                  Label="نشط" 
                                  Color="Color.Primary" />
                    </MudItem>
                }
            </MudGrid>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="CloseDialog">إلغاء</MudButton>
        <MudButton Color="Color.Primary" 
                  Variant="Variant.Filled" 
                  OnClick="SaveAnimalType"
                  Disabled="@(!isFormValid || isSubmitting)">
            @if (isSubmitting)
            {
                <MudProgressCircular Class="ml-2" Size="Size.Small" Indeterminate="true" />
                <span>جاري الحفظ...</span>
            }
            else
            {
                <span>@(isEditMode ? "حفظ التغييرات" : "إضافة")</span>
            }
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    private List<AnimalType> animalTypes = new();
    private bool isLoading = true;
    private string searchText = "";
    private bool showActiveOnly = true;
    private bool showDialog = false;
    private bool isEditMode = false;
    private bool isFormValid = false;
    private bool isSubmitting = false;
    private MudForm form = null!;
    private AnimalType animalTypeModel = new();
    
    private DialogOptions dialogOptions = new() { MaxWidth = MaxWidth.Small, FullWidth = true };

    protected override async Task OnInitializedAsync()
    {
        await LoadAnimalTypes();
    }

    private async Task LoadAnimalTypes()
    {
        isLoading = true;
        try
        {
            var result = await AnimalTypeService.GetAnimalTypesAsync(
                search: string.IsNullOrEmpty(searchText) ? null : searchText,
                isActive: showActiveOnly ? true : null
            );
            
            animalTypes = result.ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل أنواع الحيوانات: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task SearchAnimalTypes()
    {
        await LoadAnimalTypes();
    }

    private void OpenAddDialog()
    {
        animalTypeModel = new AnimalType { IsActive = true };
        isEditMode = false;
        showDialog = true;
    }

    private void OpenEditDialog(AnimalType animalType)
    {
        animalTypeModel = new AnimalType
        {
            Id = animalType.Id,
            Name = animalType.Name,
            Description = animalType.Description,
            IsActive = animalType.IsActive
        };
        isEditMode = true;
        showDialog = true;
    }

    private void CloseDialog()
    {
        showDialog = false;
        animalTypeModel = new();
        isEditMode = false;
        isSubmitting = false;
    }

    private async Task SaveAnimalType()
    {
        if (!isFormValid) return;

        isSubmitting = true;
        try
        {
            if (isEditMode)
            {
                await AnimalTypeService.UpdateAnimalTypeAsync(animalTypeModel.Id, animalTypeModel);
                Snackbar.Add("تم تحديث نوع الحيوان بنجاح", Severity.Success);
            }
            else
            {
                await AnimalTypeService.CreateAnimalTypeAsync(animalTypeModel);
                Snackbar.Add("تم إضافة نوع الحيوان بنجاح", Severity.Success);
            }
            
            CloseDialog();
            await LoadAnimalTypes();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في حفظ نوع الحيوان: {ex.Message}", Severity.Error);
        }
        finally
        {
            isSubmitting = false;
        }
    }

    private async Task DeleteAnimalType(int animalTypeId)
    {
        try
        {
            await AnimalTypeService.DeleteAnimalTypeAsync(animalTypeId);
            await LoadAnimalTypes();
            Snackbar.Add("تم حذف نوع الحيوان بنجاح", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في حذف نوع الحيوان: {ex.Message}", Severity.Error);
        }
    }
}
