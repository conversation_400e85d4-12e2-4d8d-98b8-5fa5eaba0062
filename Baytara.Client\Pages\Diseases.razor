@page "/diseases"
@inject IDiseaseService DiseaseService
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<PageTitle>إدارة الأمراض - نظام بيطارة</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge">
    <!-- العنوان وشريط البحث -->
    <MudPaper Class="pa-4 mb-4" Elevation="2">
        <MudGrid AlignItems="Center">
            <MudItem xs="12" md="6">
                <MudText Typo="Typo.h4" Color="Color.Primary">
                    <MudIcon Icon="@Icons.Material.Filled.Coronavirus" Class="ml-2" />
                    إدارة الأمراض
                </MudText>
            </MudItem>
            <MudItem xs="12" md="6" Class="text-left">
                <MudButton Variant="Variant.Filled" 
                          Color="Color.Primary" 
                          StartIcon="@Icons.Material.Filled.Add"
                          Href="/diseases/add">
                    إضافة مرض جديد
                </MudButton>
            </MudItem>
        </MudGrid>
        
        <!-- شريط البحث -->
        <MudGrid Class="mt-4">
            <MudItem xs="12" md="8">
                <MudTextField @bind-Value="searchText" 
                             Label="البحث" 
                             Placeholder="البحث بالاسم أو الأعراض أو الوصف"
                             Adornment="Adornment.Start" 
                             AdornmentIcon="@Icons.Material.Filled.Search"
                             OnKeyUp="@(async (e) => { if (e.Key == "Enter") await SearchDiseases(); })" />
            </MudItem>
            <MudItem xs="12" md="2">
                <MudSwitch @bind-Value="showActiveOnly" Label="النشطة فقط" Color="Color.Primary" />
            </MudItem>
            <MudItem xs="12" md="2">
                <MudButton Variant="Variant.Filled" 
                          Color="Color.Secondary" 
                          FullWidth="true"
                          OnClick="SearchDiseases">
                    بحث
                </MudButton>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- جدول الأمراض -->
    <MudPaper Class="pa-4" Elevation="2">
        @if (isLoading)
        {
            <div class="text-center pa-8">
                <MudProgressCircular Indeterminate="true" />
                <MudText Class="mt-4">جاري تحميل البيانات...</MudText>
            </div>
        }
        else if (!diseases.Any())
        {
            <div class="text-center pa-8">
                <MudIcon Icon="@Icons.Material.Filled.SearchOff" Size="Size.Large" Color="Color.Secondary" />
                <MudText Typo="Typo.h6" Class="mt-4">لا توجد نتائج</MudText>
                <MudText>لم يتم العثور على أمراض مطابقة لمعايير البحث</MudText>
            </div>
        }
        else
        {
            <MudTable Items="@diseases" Hover="true" Striped="true" Dense="true">
                <HeaderContent>
                    <MudTh>اسم المرض</MudTh>
                    <MudTh>الوصف</MudTh>
                    <MudTh>الأعراض</MudTh>
                    <MudTh>طرق الوقاية</MudTh>
                    <MudTh>الحالة</MudTh>
                    <MudTh>تاريخ الإضافة</MudTh>
                    <MudTh>الإجراءات</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd DataLabel="اسم المرض">
                        <MudText Typo="Typo.body2" Style="font-weight: 500;">@context.Name</MudText>
                    </MudTd>
                    <MudTd DataLabel="الوصف">
                        <MudText Typo="Typo.body2">
                            @if (!string.IsNullOrEmpty(context.Description))
                            {
                                @(context.Description.Length > 50 ? context.Description.Substring(0, 50) + "..." : context.Description)
                            }
                            else
                            {
                                <span>-</span>
                            }
                        </MudText>
                    </MudTd>
                    <MudTd DataLabel="الأعراض">
                        <MudText Typo="Typo.body2">
                            @if (!string.IsNullOrEmpty(context.Symptoms))
                            {
                                @(context.Symptoms.Length > 50 ? context.Symptoms.Substring(0, 50) + "..." : context.Symptoms)
                            }
                            else
                            {
                                <span>-</span>
                            }
                        </MudText>
                    </MudTd>
                    <MudTd DataLabel="طرق الوقاية">
                        <MudText Typo="Typo.body2">
                            @if (!string.IsNullOrEmpty(context.Prevention))
                            {
                                @(context.Prevention.Length > 50 ? context.Prevention.Substring(0, 50) + "..." : context.Prevention)
                            }
                            else
                            {
                                <span>-</span>
                            }
                        </MudText>
                    </MudTd>
                    <MudTd DataLabel="الحالة">
                        <MudChip T="string" Size="Size.Small" 
                                Color="@(context.IsActive ? Color.Success : Color.Error)">
                            @(context.IsActive ? "نشط" : "غير نشط")
                        </MudChip>
                    </MudTd>
                    <MudTd DataLabel="تاريخ الإضافة">
                        @context.CreatedAt.ToString("yyyy/MM/dd")
                    </MudTd>
                    <MudTd DataLabel="الإجراءات">
                        <MudButtonGroup Variant="Variant.Text" Size="Size.Small">
                            <MudIconButton Icon="@Icons.Material.Filled.Visibility" 
                                          Color="Color.Info" 
                                          Size="Size.Small"
                                          OnClick="@(() => ViewDisease(context))"
                                          Title="عرض التفاصيل" />
                            <MudIconButton Icon="@Icons.Material.Filled.Edit" 
                                          Color="Color.Primary" 
                                          Size="Size.Small"
                                          Href="@($"/diseases/edit/{context.Id}")"
                                          Title="تعديل" />
                            <MudIconButton Icon="@Icons.Material.Filled.Delete" 
                                          Color="Color.Error" 
                                          Size="Size.Small"
                                          OnClick="@(() => DeleteDisease(context.Id))"
                                          Title="حذف" />
                        </MudButtonGroup>
                    </MudTd>
                </RowTemplate>
            </MudTable>
        }
    </MudPaper>
</MudContainer>

<!-- حوار عرض تفاصيل المرض -->
<MudDialog @bind-IsVisible="showDetailsDialog" Options="dialogOptions">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.Coronavirus" Class="ml-2" />
            تفاصيل المرض
        </MudText>
    </TitleContent>
    <DialogContent>
        @if (selectedDisease != null)
        {
            <MudGrid>
                <MudItem xs="12">
                    <MudText Typo="Typo.h6" Color="Color.Primary">@selectedDisease.Name</MudText>
                </MudItem>
                
                @if (!string.IsNullOrEmpty(selectedDisease.Description))
                {
                    <MudItem xs="12">
                        <MudText Typo="Typo.subtitle2" Style="font-weight: 500;">الوصف:</MudText>
                        <MudText Typo="Typo.body2">@selectedDisease.Description</MudText>
                    </MudItem>
                }
                
                @if (!string.IsNullOrEmpty(selectedDisease.Symptoms))
                {
                    <MudItem xs="12">
                        <MudText Typo="Typo.subtitle2" Style="font-weight: 500;">الأعراض:</MudText>
                        <MudText Typo="Typo.body2">@selectedDisease.Symptoms</MudText>
                    </MudItem>
                }
                
                @if (!string.IsNullOrEmpty(selectedDisease.Prevention))
                {
                    <MudItem xs="12">
                        <MudText Typo="Typo.subtitle2" Style="font-weight: 500;">طرق الوقاية:</MudText>
                        <MudText Typo="Typo.body2">@selectedDisease.Prevention</MudText>
                    </MudItem>
                }
                
                <MudItem xs="12">
                    <MudText Typo="Typo.subtitle2" Style="font-weight: 500;">الحالة:</MudText>
                    <MudChip T="string" Size="Size.Small" 
                            Color="@(selectedDisease.IsActive ? Color.Success : Color.Error)">
                        @(selectedDisease.IsActive ? "نشط" : "غير نشط")
                    </MudChip>
                </MudItem>
                
                <MudItem xs="12">
                    <MudText Typo="Typo.subtitle2" Style="font-weight: 500;">تاريخ الإضافة:</MudText>
                    <MudText Typo="Typo.body2">@selectedDisease.CreatedAt.ToString("yyyy/MM/dd HH:mm")</MudText>
                </MudItem>
            </MudGrid>
        }
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="CloseDetailsDialog">إغلاق</MudButton>
    </DialogActions>
</MudDialog>

@code {
    private List<Disease> diseases = new();
    private bool isLoading = true;
    private string searchText = "";
    private bool showActiveOnly = true;
    private bool showDetailsDialog = false;
    private Disease? selectedDisease = null;
    
    private DialogOptions dialogOptions = new() { MaxWidth = MaxWidth.Medium, FullWidth = true };

    protected override async Task OnInitializedAsync()
    {
        await LoadDiseases();
    }

    private async Task LoadDiseases()
    {
        isLoading = true;
        try
        {
            var result = await DiseaseService.GetDiseasesAsync(
                search: string.IsNullOrEmpty(searchText) ? null : searchText,
                isActive: showActiveOnly ? true : null
            );
            
            diseases = result.ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل الأمراض: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task SearchDiseases()
    {
        await LoadDiseases();
    }

    private void ViewDisease(Disease disease)
    {
        selectedDisease = disease;
        showDetailsDialog = true;
    }

    private void CloseDetailsDialog()
    {
        showDetailsDialog = false;
        selectedDisease = null;
    }

    private async Task DeleteDisease(int diseaseId)
    {
        try
        {
            await DiseaseService.DeleteDiseaseAsync(diseaseId);
            await LoadDiseases();
            Snackbar.Add("تم حذف المرض بنجاح", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في حذف المرض: {ex.Message}", Severity.Error);
        }
    }
}
