@page "/test"
@inject HttpClient Http
@inject ISnackbar Snackbar

<PageTitle>اختبار النظام - نظام بيطره</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large">
    <MudPaper Class="pa-4 mb-4" Elevation="2">
        <MudText Typo="Typo.h4" Color="Color.Primary">
            <MudIcon Icon="@Icons.Material.Filled.BugReport" Class="ml-2" />
            اختبار النظام
        </MudText>
        <MudText Typo="Typo.body1" Class="mt-2">
            صفحة اختبار للتحقق من عمل جميع مكونات النظام
        </MudText>
    </MudPaper>

    <!-- اختبار الاتصال بالـ API -->
    <MudPaper Class="pa-4 mb-4" Elevation="2">
        <MudText Typo="Typo.h5" Class="mb-4">اختبار الاتصال بالـ API</MudText>
        
        <MudGrid>
            <MudItem xs="12" md="6">
                <MudButton Variant="Variant.Filled" 
                          Color="Color.Primary" 
                          OnClick="TestApiConnection"
                          Disabled="@isTestingApi">
                    @if (isTestingApi)
                    {
                        <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                        <span class="ml-2">جاري الاختبار...</span>
                    }
                    else
                    {
                        <MudIcon Icon="@Icons.Material.Filled.Wifi" Class="ml-2" />
                        <span>اختبار الاتصال</span>
                    }
                </MudButton>
            </MudItem>
            
            <MudItem xs="12" md="6">
                @if (!string.IsNullOrEmpty(apiTestResult))
                {
                    <MudAlert Severity="@(apiTestSuccess ? Severity.Success : Severity.Error)">
                        @apiTestResult
                    </MudAlert>
                }
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- اختبار قاعدة البيانات -->
    <MudPaper Class="pa-4 mb-4" Elevation="2">
        <MudText Typo="Typo.h5" Class="mb-4">اختبار قاعدة البيانات</MudText>
        
        <MudGrid>
            <MudItem xs="12" md="6">
                <MudButton Variant="Variant.Filled" 
                          Color="Color.Secondary" 
                          OnClick="TestDatabase"
                          Disabled="@isTestingDb">
                    @if (isTestingDb)
                    {
                        <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                        <span class="ml-2">جاري الاختبار...</span>
                    }
                    else
                    {
                        <MudIcon Icon="@Icons.Material.Filled.Storage" Class="ml-2" />
                        <span>اختبار قاعدة البيانات</span>
                    }
                </MudButton>
            </MudItem>
            
            <MudItem xs="12" md="6">
                @if (!string.IsNullOrEmpty(dbTestResult))
                {
                    <MudAlert Severity="@(dbTestSuccess ? Severity.Success : Severity.Error)">
                        @dbTestResult
                    </MudAlert>
                }
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- اختبار الخدمات -->
    <MudPaper Class="pa-4 mb-4" Elevation="2">
        <MudText Typo="Typo.h5" Class="mb-4">اختبار الخدمات</MudText>
        
        <MudGrid>
            @foreach (var service in services)
            {
                <MudItem xs="12" md="6" lg="4">
                    <MudCard Class="pa-3" Elevation="1">
                        <MudCardContent>
                            <MudText Typo="Typo.h6">@service.Name</MudText>
                            <MudText Typo="Typo.body2" Class="mb-2">@service.Description</MudText>
                            
                            @if (service.IsLoading)
                            {
                                <MudProgressLinear Indeterminate="true" />
                            }
                            else if (service.TestResult != null)
                            {
                                <MudChip T="string" Size="Size.Small" 
                                        Color="@(service.TestResult.Value ? Color.Success : Color.Error)">
                                    @(service.TestResult.Value ? "نجح" : "فشل")
                                </MudChip>
                            }
                        </MudCardContent>
                        <MudCardActions>
                            <MudButton Size="Size.Small" 
                                      Color="Color.Primary" 
                                      OnClick="@(() => TestService(service))"
                                      Disabled="@service.IsLoading">
                                اختبار
                            </MudButton>
                        </MudCardActions>
                    </MudCard>
                </MudItem>
            }
        </MudGrid>
    </MudPaper>

    <!-- نتائج الاختبار الشامل -->
    <MudPaper Class="pa-4" Elevation="2">
        <MudText Typo="Typo.h5" Class="mb-4">نتائج الاختبار الشامل</MudText>
        
        <MudButton Variant="Variant.Filled" 
                  Color="Color.Success" 
                  Size="Size.Large"
                  OnClick="RunAllTests"
                  Disabled="@isRunningAllTests">
            @if (isRunningAllTests)
            {
                <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                <span class="ml-2">جاري تشغيل جميع الاختبارات...</span>
            }
            else
            {
                <MudIcon Icon="@Icons.Material.Filled.PlayArrow" Class="ml-2" />
                <span>تشغيل جميع الاختبارات</span>
            }
        </MudButton>
        
        @if (allTestsCompleted)
        {
            <MudAlert Severity="@(allTestsPassed ? Severity.Success : Severity.Warning)" Class="mt-4">
                <MudText Typo="Typo.h6">
                    @if (allTestsPassed)
                    {
                        <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Class="ml-2" />
                        <span>جميع الاختبارات نجحت! النظام جاهز للاستخدام.</span>
                    }
                    else
                    {
                        <MudIcon Icon="@Icons.Material.Filled.Warning" Class="ml-2" />
                        <span>بعض الاختبارات فشلت. يرجى مراجعة النتائج أعلاه.</span>
                    }
                </MudText>
            </MudAlert>
        }
    </MudPaper>
</MudContainer>

@code {
    private bool isTestingApi = false;
    private bool isTestingDb = false;
    private bool isRunningAllTests = false;
    private bool allTestsCompleted = false;
    private bool allTestsPassed = false;
    
    private string apiTestResult = "";
    private bool apiTestSuccess = false;
    
    private string dbTestResult = "";
    private bool dbTestSuccess = false;

    public class ServiceTest
    {
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public bool IsLoading { get; set; } = false;
        public bool? TestResult { get; set; } = null;
        public Func<Task<bool>> TestAction { get; set; } = null!;
    }

    private List<ServiceTest> services = new();

    protected override void OnInitialized()
    {
        services = new List<ServiceTest>
        {
            new ServiceTest 
            { 
                Name = "خدمة المربين", 
                Description = "اختبار عمليات إدارة المربين",
                TestAction = TestBreedersService
            },
            new ServiceTest 
            { 
                Name = "خدمة الحيوانات", 
                Description = "اختبار عمليات إدارة الحيوانات",
                TestAction = TestAnimalsService
            },
            new ServiceTest 
            { 
                Name = "خدمة الأدوية", 
                Description = "اختبار عمليات إدارة الأدوية",
                TestAction = TestMedicinesService
            },
            new ServiceTest 
            { 
                Name = "خدمة الأمراض", 
                Description = "اختبار عمليات إدارة الأمراض",
                TestAction = TestDiseasesService
            },
            new ServiceTest 
            { 
                Name = "خدمة الفروع", 
                Description = "اختبار عمليات إدارة الفروع",
                TestAction = TestBranchesService
            }
        };
    }

    private async Task TestApiConnection()
    {
        isTestingApi = true;
        apiTestResult = "";
        
        try
        {
            var response = await Http.GetAsync("api/branches");
            if (response.IsSuccessStatusCode)
            {
                apiTestResult = "تم الاتصال بالـ API بنجاح!";
                apiTestSuccess = true;
            }
            else
            {
                apiTestResult = $"فشل الاتصال بالـ API. رمز الخطأ: {response.StatusCode}";
                apiTestSuccess = false;
            }
        }
        catch (Exception ex)
        {
            apiTestResult = $"خطأ في الاتصال: {ex.Message}";
            apiTestSuccess = false;
        }
        finally
        {
            isTestingApi = false;
        }
    }

    private async Task TestDatabase()
    {
        isTestingDb = true;
        dbTestResult = "";
        
        try
        {
            var response = await Http.GetAsync("api/branches");
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                dbTestResult = "قاعدة البيانات تعمل بشكل صحيح!";
                dbTestSuccess = true;
            }
            else
            {
                dbTestResult = "مشكلة في الوصول لقاعدة البيانات";
                dbTestSuccess = false;
            }
        }
        catch (Exception ex)
        {
            dbTestResult = $"خطأ في قاعدة البيانات: {ex.Message}";
            dbTestSuccess = false;
        }
        finally
        {
            isTestingDb = false;
        }
    }

    private async Task TestService(ServiceTest service)
    {
        service.IsLoading = true;
        service.TestResult = null;
        StateHasChanged();
        
        try
        {
            service.TestResult = await service.TestAction();
        }
        catch
        {
            service.TestResult = false;
        }
        finally
        {
            service.IsLoading = false;
            StateHasChanged();
        }
    }

    private async Task<bool> TestBreedersService()
    {
        try
        {
            var response = await Http.GetAsync("api/breeders");
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    private async Task<bool> TestAnimalsService()
    {
        try
        {
            var response = await Http.GetAsync("api/animals");
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    private async Task<bool> TestMedicinesService()
    {
        try
        {
            var response = await Http.GetAsync("api/medicines");
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    private async Task<bool> TestDiseasesService()
    {
        try
        {
            var response = await Http.GetAsync("api/diseases");
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    private async Task<bool> TestBranchesService()
    {
        try
        {
            var response = await Http.GetAsync("api/branches");
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    private async Task RunAllTests()
    {
        isRunningAllTests = true;
        allTestsCompleted = false;
        
        // اختبار الاتصال بالـ API
        await TestApiConnection();
        await Task.Delay(500);
        
        // اختبار قاعدة البيانات
        await TestDatabase();
        await Task.Delay(500);
        
        // اختبار جميع الخدمات
        foreach (var service in services)
        {
            await TestService(service);
            await Task.Delay(300);
        }
        
        // تحديد النتيجة النهائية
        allTestsPassed = apiTestSuccess && dbTestSuccess && services.All(s => s.TestResult == true);
        allTestsCompleted = true;
        isRunningAllTests = false;
        
        // إظهار رسالة للمستخدم
        if (allTestsPassed)
        {
            Snackbar.Add("جميع الاختبارات نجحت! النظام جاهز للاستخدام.", Severity.Success);
        }
        else
        {
            Snackbar.Add("بعض الاختبارات فشلت. يرجى مراجعة النتائج.", Severity.Warning);
        }
    }
}
