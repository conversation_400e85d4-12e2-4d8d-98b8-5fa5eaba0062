<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>نظام بيطره لإدارة الثروة الحيوانية</title>
    <base href="/" />

    <!-- خط Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- خط Noto Sans Arabic كبديل -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- MudBlazor CSS -->
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" rel="stylesheet" />
    <link href="_content/MudBlazor/MudBlazor.min.css" rel="stylesheet" />

    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <link rel="stylesheet" href="css/app.css" />
    <link rel="icon" type="image/png" href="favicon.png" />
    <link href="Baytara.Client.styles.css" rel="stylesheet" />

    <style>
        body {
            font-family: 'Cairo', 'Noto Sans Arabic', 'Roboto', sans-serif !important;
            direction: rtl;
            font-weight: 400;
        }

        .mud-rtl {
            direction: rtl;
        }

        /* تطبيق خط Cairo على جميع العناصر */
        * {
            font-family: 'Cairo', 'Noto Sans Arabic', 'Roboto', sans-serif !important;
        }

        /* تحسين النصوص العربية */
        .mud-typography, .mud-text, .mud-button, .mud-input, .mud-select, .mud-nav-link {
            font-family: 'Cairo', 'Noto Sans Arabic', sans-serif !important;
            font-weight: 400;
        }

        /* تحسين العناوين */
        h1, h2, h3, h4, h5, h6, .mud-typography-h1, .mud-typography-h2, .mud-typography-h3,
        .mud-typography-h4, .mud-typography-h5, .mud-typography-h6 {
            font-family: 'Cairo', 'Noto Sans Arabic', sans-serif !important;
            font-weight: 600;
        }

        /* تخصيص الألوان - أزرق فاتح */
        :root {
            --mud-palette-primary: #42A5F5;
            --mud-palette-primary-rgb: 66, 165, 245;
            --mud-palette-secondary: #FF8F00;
            --mud-palette-secondary-rgb: 255, 143, 0;
        }

        /* تخصيص النافجيشن منيو - أخضر مائل للأزرق متدرج */
        .mud-drawer {
            background: linear-gradient(45deg, #20B2AA 30%, #48CAE4 90%) !important;
        }

        .mud-appbar {
            background: linear-gradient(45deg, #20B2AA 30%, #48CAE4 90%) !important;
        }

        /* تحسين الشريط العلوي */
        .mud-appbar .mud-typography {
            font-family: 'Cairo', sans-serif !important;
        }
    </style>
</head>

<body>
    <div id="app">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
            <div style="margin-bottom: 20px;">
                <svg width="60" height="60" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12" r="10" stroke="#2E7D32" stroke-width="2"/>
                    <path d="M8 12l2 2 4-4" stroke="#2E7D32" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </div>
            <div style="color: #2E7D32; font-size: 18px; font-weight: 500;">
                جاري تحميل نظام بيطره...
            </div>
        </div>
    </div>

    <div id="blazor-error-ui">
        حدث خطأ غير متوقع.
        <a href="." class="reload">إعادة تحميل</a>
        <span class="dismiss">🗙</span>
    </div>

    <!-- MudBlazor JS -->
    <script src="_content/MudBlazor/MudBlazor.min.js"></script>
    <script src="_framework/blazor.webassembly.js"></script>
</body>

</html>
