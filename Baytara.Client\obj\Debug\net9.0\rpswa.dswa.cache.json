{"GlobalPropertiesHash": "ZmzYjovtY7jH2zPj48B93yly3L0jVUwnl1afiLOmcks=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["97NMwhqAD8hU39kx5XfCAd0USyl9tygqRooMtT9HTmA=", "OAJc/7RROBhbMbKuANHBjBljo+kPUYcqQBAGGUxLwZU=", "6+hDifhp4TpB04VYQEFP9jaT0HKjCUM5gIMsYqx1+Vw=", "IqIwy9HCkVwgaP0DQNslD/OZ/FUpYjUhriQH0gDbUJY=", "HXdBGFDVopFsd4kQMjGxmdnvgveW9ebEobIrAe58b9k=", "IRHrXwc/55NGUnNHjoOvlqT1cnyLDJfQVC+NvKnc8vc=", "u1YJmuqcgybV4uNg6kzJt1kqZ/GxdXgf6OaPhYiQEz0=", "V/5Wt+vDtPWdfftyraqcSzYez5GQdXAsDuU/vD02DMs=", "99lbOSFDtP9Jp/ZYQsYnEc9hJHMnbTFiRHSA6QygCHk=", "j3/q6o2AGRPtP80AVPdrnFPDjjTH4NY15H9u6kAmaG0=", "9U8Jg2yGiljYwmpSVotCggYIhnAhbENEio/7dlNuXug=", "5R6q3JJWmgk69JhbUBG6tnZcIOnJzxwrumtVeyyqLVE=", "UjnVRSVKODmhaZoXdAu482BaMNMNnku68/GQx10ZL+Y=", "rtdvWkEZ2vBpzTUi514cA6eGgxGgvpeXLHQkPOOIqL0=", "AdLo7vxg/fsYNJOFi4WU8h5FEzEwFQGHXZIeHGlsjXk=", "mA4o5A1vGSDbe0/b4pMxVNwT4K9de0R2Gb1q+mVGjYY=", "lj6iYuvSYg+NHzND68CuHO9RDjdqhY0WDBw9jJ/itDE=", "cFVlQDdC8CLTtzPYbsJJJ2C+GggUy4ReOfk3rbvAEEc=", "fWwkeVz5otlnIHDqLhfWTV3pNey1qwgjoU5oniN0NY8=", "HKLltDYjRdC50QS/bcWUqW2ECiRcfnmJfMBROfiXIIw=", "5L6zYojG59tcl+/8mAaa02BZH6y8shBCMBdBxFV98fo=", "EfJ5ovSzqMQZBsh0VIxbC/ihElnJ3Z3ChL6Ic6Q/eWk=", "lAxiuB4AzDhwSy5NIen+pbG9vYl1aWVwkmctLb9RaZ4=", "4P1lIVIxUZq2qFeTifBbmwOUwxo8zhXl4I7QVGWymvA=", "J+lXtdWqBjWzAB+eufPruZeQ4SpJP0u4DjdVVOXC4bU=", "IF9Q/VOrQzdDm9ZGHKaGLx57GQJL7W58If6fae9SngI=", "Po+7P3FX83Lb/7O+929h6w02wU/RrO4uRsaNTN7o19E=", "pXZG2xjr9lKETn70aKYYkh8/JpY79bAejDE4vIduHII=", "1A6VTSOXSkxvKt2GkNDdzT/DV9A4VYaDIjK3DcrH6Rs=", "VcgVHWqp+nwWXBWbaiuwEcEji+YZXYAcNX9kHj+MBBw=", "E03mzWe8r43xCKblX+VNOOU/JWQtYeAhJjXD//WLyNg=", "78CZZTkizTQyvMVlJhSMc+T24fH79HHP3Ly4UjG0kLY=", "QYj4NSbFbgD++Uv4mq0mlgtIfjqDmfIgx2Wam445zo8=", "CZXHB2Dykix4QkLC1f+kEqQeFgRqSJ6QXg5nBNwl5bM=", "EGWS1qNoA5lYkWKp/OdJ50YOjCHe7CgtbyzrE0fhTks=", "EzsaGYKr4bEUvn9tYUIglAflNxOXcYeyxwA6Vq6MokA=", "qEzmpQqQtbVU2JC3VIQNyG8G4xfYnHjHz4SamAAcAAo=", "CG//svKmMcj4xHy5vr/cPZwQ8KJec4kuV0avP6wGknk=", "ATPRupIRk6agsdTg5t0ASR72cqe1DxS0uJiytPGEiuA=", "dfr2dfjkuaJOSmsiL6TWhCFMDatbMu03RrRRTjQQ+i8=", "xc/LTSBTFeOitxpivDR7WsRlImGRBsSZDDlig+SumXc=", "V4Q/kF9emLf9oxbsulc4uPlNpGQdSjjVt7hbxXtTZ/Q=", "8uavnojYx+VGv1UXmDQ7g2bklX+Q2YB+/LZk+hLkfk0=", "/ORujYRouYFzpUG0F4DxpY05SewtvpGRuv4p40Dxtak=", "PUTxYvyI6D3f7vR6Je5MHmFZQIieK06DZHZFHjSHQ1o=", "tBMJHQMCAZBqQ9QMAooeTaIs1xs/jVs6Mf1pS9c9yTg=", "qmbg7e5I7+3VW83UXdfvNIUE6ndP2oetWkzBSLPbQqU=", "lkGqMwOkEwXTielxysPDOpup+zqnrq8X016Ak47Ubig=", "IkkkLzwhRBgi2ZZlRbNkRo8DZG46BNFvB9KcLEbRBIM=", "V7m/PFW8rN7lsaxAJ5Gow5KdNdbhYEmd1e5U7QWnnPg=", "ZliP2J4/MeJE6DLbOY7JWsemh8adXjDFK+OgURITVWE=", "iUuo4zJn9CglGCGyZiakL4hj7OJUL/nZ/45ipsc0Lb8=", "VJq/tngRymEjUQBjGnuu5ivaTlm164zw9mzQ99eZv9s=", "30egU1mQgCWrJuwQtE4GpWrjAjqWufYO5f/nCyjoCtg=", "Cjr5w+lF0MIARZXpgdsI2ubRyOSMsxyQvxrWE1E6B7g=", "rDKAZU+ddSWcGA9KYHVMeux8df7UBp6Yi7Lc3TTm5fc=", "9xNBbwslARI1IBuF/fIrYDq0bXD7LcuEiaGKfClC0gc=", "j2EqOQBDLtVM3APZApoLDAHU6xb4NIiPPXfX4MbcocQ=", "lSrDICXsLe8wzaRNnGhcI63l3xtMgvYJaA8O0UOqOg0=", "Vn2zQbJfqUVoSu6+ip0CezOSQgcSMRFxpNKhDCSyEus=", "GxgoVqUGHsDCchHAQMmq5J+N50y4OzgM6QKZbxSyLRI=", "+qPGdnshLnrDcaWvjBZoJui3OoMcL0TcgduplkhULHY=", "kqmH214Mi8hMq0HX/VUsprg2+SbrffNL4YrpdThlWeQ=", "SSjA0PNxknLxhm9zGNYmHBikaB6syrPYO5jpmJBoCGA=", "j7xh+Lt0TL1Y1hxvfHHIMUoAiSvd+0P2mXgB0V8MHOM=", "aqZf+90skAyuv0eoRbCbkFGlofIwuMGNDu/UKBOnFPg=", "eFGEWciIPhzQpBv9CQwS/Oi10zzlTiOdRZKD4V45Dx0=", "CwmIfXqTvyPwSB2XrAscofLG/FWOLsLZS0kBwMX2dK4=", "CbZyFS6OsD2uN1Bi8OZ/157o4+X3dlng3EMCKh1UMGw=", "1UB8EqsjzjkIklVMXo6OZhIU58Hruz3CM8mfhl8CV0g=", "Ml3ncaXFQnKW8JVhjd33QT9Q93ZdTKOAJseYbw3bjBc="], "CachedAssets": {"97NMwhqAD8hU39kx5XfCAd0USyl9tygqRooMtT9HTmA=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\css\\app.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8rbvw3on5j", "Integrity": "vGbjgqEqv4y3q5OB8W2R9LthkuF8mQfHFeNdKSReSmU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\app.css", "FileLength": 4151, "LastWriteTime": "2025-06-22T09:25:19.6272933+00:00"}, "OAJc/7RROBhbMbKuANHBjBljo+kPUYcqQBAGGUxLwZU=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\favicon.png", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png", "FileLength": 1148, "LastWriteTime": "2025-06-22T09:25:19.705428+00:00"}, "6+hDifhp4TpB04VYQEFP9jaT0HKjCUM5gIMsYqx1+Vw=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\icon-192.png", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "icon-192#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f9uvjujlxy", "Integrity": "DbpQaq68ZSb5IoPosBErM1QWBfsbTxpJqhU0REi6wP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\icon-192.png", "FileLength": 2626, "LastWriteTime": "2025-06-22T09:25:19.705428+00:00"}, "IqIwy9HCkVwgaP0DQNslD/OZ/FUpYjUhriQH0gDbUJY=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\index.html", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z76cthprpd", "Integrity": "fxvr457pP4FIZ7fZ6cJE/cXeK/m6xmAYlYd/4r9eXR0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 4086, "LastWriteTime": "2025-06-25T04:20:12.7703304+00:00"}, "HXdBGFDVopFsd4kQMjGxmdnvgveW9ebEobIrAe58b9k=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-06-22T09:25:19.7522979+00:00"}, "IRHrXwc/55NGUnNHjoOvlqT1cnyLDJfQVC+NvKnc8vc=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-06-22T09:25:19.7522979+00:00"}, "u1YJmuqcgybV4uNg6kzJt1kqZ/GxdXgf6OaPhYiQEz0=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-06-22T09:25:19.7522979+00:00"}, "V/5Wt+vDtPWdfftyraqcSzYez5GQdXAsDuU/vD02DMs=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-06-22T09:25:19.783545+00:00"}, "99lbOSFDtP9Jp/ZYQsYnEc9hJHMnbTFiRHSA6QygCHk=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-06-22T09:25:19.7991785+00:00"}, "j3/q6o2AGRPtP80AVPdrnFPDjjTH4NY15H9u6kAmaG0=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-06-22T09:25:19.7991785+00:00"}, "9U8Jg2yGiljYwmpSVotCggYIhnAhbENEio/7dlNuXug=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-06-22T09:25:19.7991785+00:00"}, "5R6q3JJWmgk69JhbUBG6tnZcIOnJzxwrumtVeyyqLVE=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-06-22T09:25:19.8304226+00:00"}, "UjnVRSVKODmhaZoXdAu482BaMNMNnku68/GQx10ZL+Y=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-06-22T09:25:19.8304226+00:00"}, "rtdvWkEZ2vBpzTUi514cA6eGgxGgvpeXLHQkPOOIqL0=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-06-22T09:25:19.8304226+00:00"}, "AdLo7vxg/fsYNJOFi4WU8h5FEzEwFQGHXZIeHGlsjXk=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-06-22T09:25:19.8616701+00:00"}, "mA4o5A1vGSDbe0/b4pMxVNwT4K9de0R2Gb1q+mVGjYY=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-06-22T09:25:19.8616701+00:00"}, "lj6iYuvSYg+NHzND68CuHO9RDjdqhY0WDBw9jJ/itDE=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-06-22T09:25:19.8616701+00:00"}, "cFVlQDdC8CLTtzPYbsJJJ2C+GggUy4ReOfk3rbvAEEc=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-06-22T09:25:19.8772994+00:00"}, "fWwkeVz5otlnIHDqLhfWTV3pNey1qwgjoU5oniN0NY8=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-06-22T09:25:19.8772994+00:00"}, "HKLltDYjRdC50QS/bcWUqW2ECiRcfnmJfMBROfiXIIw=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-06-22T09:25:19.8772994+00:00"}, "5L6zYojG59tcl+/8mAaa02BZH6y8shBCMBdBxFV98fo=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-06-22T09:25:19.9085561+00:00"}, "EfJ5ovSzqMQZBsh0VIxbC/ihElnJ3Z3ChL6Ic6Q/eWk=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-06-22T09:25:19.9241731+00:00"}, "lAxiuB4AzDhwSy5NIen+pbG9vYl1aWVwkmctLb9RaZ4=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-06-22T09:25:19.9398149+00:00"}, "4P1lIVIxUZq2qFeTifBbmwOUwxo8zhXl4I7QVGWymvA=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-06-22T09:25:19.0908649+00:00"}, "J+lXtdWqBjWzAB+eufPruZeQ4SpJP0u4DjdVVOXC4bU=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-06-22T09:25:19.1065499+00:00"}, "IF9Q/VOrQzdDm9ZGHKaGLx57GQJL7W58If6fae9SngI=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-06-22T09:25:19.1221179+00:00"}, "Po+7P3FX83Lb/7O+929h6w02wU/RrO4uRsaNTN7o19E=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-06-22T09:25:19.1221179+00:00"}, "pXZG2xjr9lKETn70aKYYkh8/JpY79bAejDE4vIduHII=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-06-22T09:25:19.153367+00:00"}, "1A6VTSOXSkxvKt2GkNDdzT/DV9A4VYaDIjK3DcrH6Rs=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-06-22T09:25:19.2107789+00:00"}, "VcgVHWqp+nwWXBWbaiuwEcEji+YZXYAcNX9kHj+MBBw=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-06-22T09:25:19.2576575+00:00"}, "E03mzWe8r43xCKblX+VNOOU/JWQtYeAhJjXD//WLyNg=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-06-22T09:25:19.2576575+00:00"}, "78CZZTkizTQyvMVlJhSMc+T24fH79HHP3Ly4UjG0kLY=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-06-22T09:25:19.3045112+00:00"}, "QYj4NSbFbgD++Uv4mq0mlgtIfjqDmfIgx2Wam445zo8=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-06-22T09:25:19.3201358+00:00"}, "CZXHB2Dykix4QkLC1f+kEqQeFgRqSJ6QXg5nBNwl5bM=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-06-22T09:25:19.351386+00:00"}, "EGWS1qNoA5lYkWKp/OdJ50YOjCHe7CgtbyzrE0fhTks=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-06-22T09:25:19.3670213+00:00"}, "EzsaGYKr4bEUvn9tYUIglAflNxOXcYeyxwA6Vq6MokA=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-06-22T09:25:19.4295158+00:00"}, "qEzmpQqQtbVU2JC3VIQNyG8G4xfYnHjHz4SamAAcAAo=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wm25hb1crz", "Integrity": "aJZC4u4JGCG1ui5iVuctFjsp9BtdAiZ1xlIzeuxYBMY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-06-22T09:25:19.4295158+00:00"}, "CG//svKmMcj4xHy5vr/cPZwQ8KJec4kuV0avP6wGknk=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9i1hoof2cg", "Integrity": "4QlN9gNvspRcUV0J9UX9fIxCFgSa12A3z6vgke2Gt0o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-06-22T09:25:19.4608104+00:00"}, "ATPRupIRk6agsdTg5t0ASR72cqe1DxS0uJiytPGEiuA=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-06-22T09:25:19.4773381+00:00"}, "dfr2dfjkuaJOSmsiL6TWhCFMDatbMu03RrRRTjQQ+i8=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0nj8p7ttgy", "Integrity": "lKXkSHymyS3WJ6Qg3ZyWvmQgTosjxGtbYcTPJhNRW0M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-06-22T09:25:19.5288875+00:00"}, "xc/LTSBTFeOitxpivDR7WsRlImGRBsSZDDlig+SumXc=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fy2gvia3cj", "Integrity": "99meunvs/idGiF/rdU9WRSpyB2XDK+bZ2vNohmENjb8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-06-22T09:25:19.5379317+00:00"}, "V4Q/kF9emLf9oxbsulc4uPlNpGQdSjjVt7hbxXtTZ/Q=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c97sm6mace", "Integrity": "QF6a3x6rTADjk4GHgutQ/wWdDA8mXYOMyxVXxgQ3dAQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-06-22T09:25:19.5450196+00:00"}, "8uavnojYx+VGv1UXmDQ7g2bklX+Q2YB+/LZk+hLkfk0=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-06-22T09:25:19.5762703+00:00"}, "/ORujYRouYFzpUG0F4DxpY05SewtvpGRuv4p40Dxtak=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s347mgww9m", "Integrity": "eNmRxCPn9gSlJckq4jUCSHZFocFdxxDjwAE3GH4kjz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-06-22T09:25:19.5762703+00:00"}, "PUTxYvyI6D3f7vR6Je5MHmFZQIieK06DZHZFHjSHQ1o=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5plt2zstor", "Integrity": "GcHDQZbHPNtpkZyRwP52+aw6uGh/9KNTRE25TyA55j8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-06-22T09:25:19.5930777+00:00"}, "tBMJHQMCAZBqQ9QMAooeTaIs1xs/jVs6Mf1pS9c9yTg=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6gi56h49eb", "Integrity": "phaZUOiFzHx9CX/HRq8ko/ekMAsnoaxK20hPcNlY2bk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-06-22T09:25:19.6116671+00:00"}, "qmbg7e5I7+3VW83UXdfvNIUE6ndP2oetWkzBSLPbQqU=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-06-22T09:25:19.6116671+00:00"}, "lkGqMwOkEwXTielxysPDOpup+zqnrq8X016Ak47Ubig=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "icnra8k7s3", "Integrity": "8hiiXxSV/0xjDJMs8k/TzNXgxBULY/BG53uLHNzwLKc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-06-22T09:25:19.6272933+00:00"}, "IkkkLzwhRBgi2ZZlRbNkRo8DZG46BNFvB9KcLEbRBIM=": {"Identity": "E:\\baytara\\Baytara.Client\\wwwroot\\sample-data\\weather.json", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\wwwroot\\", "BasePath": "/", "RelativePath": "sample-data/weather#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iag0ou56lh", "Integrity": "enKgCMkYmCpfEcmg6Annbmc40VZ/A6aYYSQjZfVn2cU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\sample-data\\weather.json", "FileLength": 453, "LastWriteTime": "2025-06-22T09:25:19.6429222+00:00"}}, "CachedCopyCandidates": {}}