using System.ComponentModel.DataAnnotations;

namespace Baytara.Shared.DTOs;

public class BreederDto
{
    public int Id { get; set; }
    
    [Required(ErrorMessage = "اسم المربي مطلوب")]
    [StringLength(100, ErrorMessage = "الاسم يجب أن يكون أقل من 100 حرف")]
    public string Name { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "رقم الهاتف مطلوب")]
    [Phone(ErrorMessage = "رقم الهاتف غير صحيح")]
    public string Phone { get; set; } = string.Empty;
    
    [StringLength(200, ErrorMessage = "العنوان يجب أن يكون أقل من 200 حرف")]
    public string? Address { get; set; }
    
    [StringLength(100, ErrorMessage = "المنطقة يجب أن تكون أقل من 100 حرف")]
    public string? Region { get; set; }
    
    [StringLength(50, ErrorMessage = "رقم الهوية يجب أن يكون أقل من 50 حرف")]
    public string? NationalId { get; set; }
    
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; }
    
    public int BranchId { get; set; }
    public string? BranchName { get; set; }
    
    public int AnimalsCount { get; set; }
}

public class CreateBreederDto
{
    [Required(ErrorMessage = "اسم المربي مطلوب")]
    [StringLength(100, ErrorMessage = "الاسم يجب أن يكون أقل من 100 حرف")]
    public string Name { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "رقم الهاتف مطلوب")]
    [Phone(ErrorMessage = "رقم الهاتف غير صحيح")]
    public string Phone { get; set; } = string.Empty;
    
    [StringLength(200, ErrorMessage = "العنوان يجب أن يكون أقل من 200 حرف")]
    public string? Address { get; set; }
    
    [StringLength(100, ErrorMessage = "المنطقة يجب أن تكون أقل من 100 حرف")]
    public string? Region { get; set; }
    
    [StringLength(50, ErrorMessage = "رقم الهوية يجب أن يكون أقل من 50 حرف")]
    public string? NationalId { get; set; }
    
    [Required(ErrorMessage = "الفرع مطلوب")]
    public int BranchId { get; set; }
}

public class UpdateBreederDto
{
    [Required(ErrorMessage = "اسم المربي مطلوب")]
    [StringLength(100, ErrorMessage = "الاسم يجب أن يكون أقل من 100 حرف")]
    public string Name { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "رقم الهاتف مطلوب")]
    [Phone(ErrorMessage = "رقم الهاتف غير صحيح")]
    public string Phone { get; set; } = string.Empty;
    
    [StringLength(200, ErrorMessage = "العنوان يجب أن يكون أقل من 200 حرف")]
    public string? Address { get; set; }
    
    [StringLength(100, ErrorMessage = "المنطقة يجب أن تكون أقل من 100 حرف")]
    public string? Region { get; set; }
    
    [StringLength(50, ErrorMessage = "رقم الهوية يجب أن يكون أقل من 50 حرف")]
    public string? NationalId { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    [Required(ErrorMessage = "الفرع مطلوب")]
    public int BranchId { get; set; }
}
