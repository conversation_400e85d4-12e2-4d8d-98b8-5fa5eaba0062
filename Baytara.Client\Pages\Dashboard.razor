@page "/dashboard"
@inject IReportService ReportService
@inject IBranchService BranchService
@inject ISnackbar Snackbar

<PageTitle>لوحة التحكم - نظام بيطره</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge">
    <!-- العنوان -->
    <MudPaper Class="pa-4 mb-4" Elevation="2">
        <MudGrid AlignItems="Center">
            <MudItem xs="12" md="8">
                <MudText Typo="Typo.h4" Color="Color.Primary">
                    <MudIcon Icon="@Icons.Material.Filled.Dashboard" Class="ml-2" />
                    لوحة التحكم الرئيسية
                </MudText>
                <MudText Typo="Typo.body1" Class="mt-2">
                    نظرة شاملة على أداء النظام والإحصائيات المهمة
                </MudText>
            </MudItem>
            <MudItem xs="12" md="4" Class="text-left">
                <MudSelect @bind-Value="selectedBranchId" Label="اختيار الفرع" OnSelectionChanged="OnBranchChanged">
                    <MudSelectItem Value="0">جميع الفروع</MudSelectItem>
                    @foreach (var branch in branches)
                    {
                        <MudSelectItem Value="@branch.Id">@branch.Name</MudSelectItem>
                    }
                </MudSelect>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- الإحصائيات السريعة -->
    <MudGrid Class="mb-4">
        <MudItem xs="12" sm="6" md="3">
            <MudPaper Class="pa-4 text-center" Elevation="3" Style="background: linear-gradient(45deg, #42A5F5 30%, #64B5F6 90%); color: white;">
                <MudIcon Icon="@Icons.Material.Filled.People" Size="Size.Large" Class="mb-2" />
                <MudText Typo="Typo.h3" Style="font-weight: bold;">@totalBreeders</MudText>
                <MudText Typo="Typo.h6">إجمالي المربين</MudText>
                <MudText Typo="Typo.caption">النشطين حالياً</MudText>
            </MudPaper>
        </MudItem>
        
        <MudItem xs="12" sm="6" md="3">
            <MudPaper Class="pa-4 text-center" Elevation="3" Style="background: linear-gradient(45deg, #66BB6A 30%, #81C784 90%); color: white;">
                <MudIcon Icon="@Icons.Material.Filled.Pets" Size="Size.Large" Class="mb-2" />
                <MudText Typo="Typo.h3" Style="font-weight: bold;">@totalAnimals</MudText>
                <MudText Typo="Typo.h6">إجمالي الحيوانات</MudText>
                <MudText Typo="Typo.caption">تحت الرعاية</MudText>
            </MudPaper>
        </MudItem>
        
        <MudItem xs="12" sm="6" md="3">
            <MudPaper Class="pa-4 text-center" Elevation="3" Style="background: linear-gradient(45deg, #FF7043 30%, #FF8A65 90%); color: white;">
                <MudIcon Icon="@Icons.Material.Filled.MedicalServices" Size="Size.Large" Class="mb-2" />
                <MudText Typo="Typo.h3" Style="font-weight: bold;">@totalTreatments</MudText>
                <MudText Typo="Typo.h6">إجمالي العلاجات</MudText>
                <MudText Typo="Typo.caption">منذ البداية</MudText>
            </MudPaper>
        </MudItem>
        
        <MudItem xs="12" sm="6" md="3">
            <MudPaper Class="pa-4 text-center" Elevation="3" Style="background: linear-gradient(45deg, #AB47BC 30%, #BA68C8 90%); color: white;">
                <MudIcon Icon="@Icons.Material.Filled.AttachMoney" Size="Size.Large" Class="mb-2" />
                <MudText Typo="Typo.h3" Style="font-weight: bold;">@totalRevenue.ToString("F0")</MudText>
                <MudText Typo="Typo.h6">إجمالي الإيرادات</MudText>
                <MudText Typo="Typo.caption">ريال عماني</MudText>
            </MudPaper>
        </MudItem>
    </MudGrid>

    <!-- الرسوم البيانية والتحليلات -->
    <MudGrid>
        <!-- نشاط هذا الشهر -->
        <MudItem xs="12" md="6">
            <MudPaper Class="pa-4" Elevation="2">
                <MudText Typo="Typo.h5" Class="mb-4">نشاط هذا الشهر</MudText>
                <MudGrid>
                    <MudItem xs="6">
                        <MudPaper Class="pa-3 text-center" Elevation="1" Style="background-color: #E3F2FD;">
                            <MudIcon Icon="@Icons.Material.Filled.MedicalServices" Color="Color.Primary" Size="Size.Medium" Class="mb-2" />
                            <MudText Typo="Typo.h4" Color="Color.Primary">@treatmentsThisMonth</MudText>
                            <MudText Typo="Typo.body2">علاجات جديدة</MudText>
                        </MudPaper>
                    </MudItem>
                    <MudItem xs="6">
                        <MudPaper Class="pa-3 text-center" Elevation="1" Style="background-color: #F3E5F5;">
                            <MudIcon Icon="@Icons.Material.Filled.Science" Color="Color.Secondary" Size="Size.Medium" Class="mb-2" />
                            <MudText Typo="Typo.h4" Color="Color.Secondary">@labTestsThisMonth</MudText>
                            <MudText Typo="Typo.body2">فحوصات مختبرية</MudText>
                        </MudPaper>
                    </MudItem>
                </MudGrid>
            </MudPaper>
        </MudItem>

        <!-- الوصول السريع -->
        <MudItem xs="12" md="6">
            <MudPaper Class="pa-4" Elevation="2">
                <MudText Typo="Typo.h5" Class="mb-4">الوصول السريع</MudText>
                <MudGrid>
                    <MudItem xs="6">
                        <MudButton Variant="Variant.Filled" 
                                  Color="Color.Primary" 
                                  FullWidth="true"
                                  StartIcon="@Icons.Material.Filled.PersonAdd"
                                  Href="/breeders/add">
                            إضافة مربي
                        </MudButton>
                    </MudItem>
                    <MudItem xs="6">
                        <MudButton Variant="Variant.Filled" 
                                  Color="Color.Secondary" 
                                  FullWidth="true"
                                  StartIcon="@Icons.Material.Filled.Add"
                                  Href="/animals/add">
                            إضافة حيوان
                        </MudButton>
                    </MudItem>
                    <MudItem xs="6">
                        <MudButton Variant="Variant.Outlined" 
                                  Color="Color.Success" 
                                  FullWidth="true"
                                  StartIcon="@Icons.Material.Filled.MedicalServices"
                                  Href="/treatments/add">
                            تسجيل علاج
                        </MudButton>
                    </MudItem>
                    <MudItem xs="6">
                        <MudButton Variant="Variant.Outlined" 
                                  Color="Color.Info" 
                                  FullWidth="true"
                                  StartIcon="@Icons.Material.Filled.Science"
                                  Href="/lab-tests/add">
                            طلب فحص
                        </MudButton>
                    </MudItem>
                </MudGrid>
            </MudPaper>
        </MudItem>
    </MudGrid>

    <!-- التنبيهات والإشعارات -->
    <MudPaper Class="pa-4 mt-4" Elevation="2">
        <MudText Typo="Typo.h5" Class="mb-4">التنبيهات والإشعارات</MudText>
        
        @if (alerts.Any())
        {
            @foreach (var alert in alerts)
            {
                <MudAlert Severity="@alert.Severity" Class="mb-2">
                    <MudText Typo="Typo.body1">@alert.Message</MudText>
                </MudAlert>
            }
        }
        else
        {
            <MudAlert Severity="Severity.Success">
                <MudText Typo="Typo.body1">
                    <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Class="ml-2" />
                    لا توجد تنبيهات في الوقت الحالي. النظام يعمل بشكل طبيعي.
                </MudText>
            </MudAlert>
        }
    </MudPaper>

    <!-- آخر النشاطات -->
    <MudPaper Class="pa-4 mt-4" Elevation="2">
        <MudText Typo="Typo.h5" Class="mb-4">آخر النشاطات</MudText>
        
        <MudTimeline TimelineOrientation="TimelineOrientation.Vertical">
            <MudTimelineItem Color="Color.Success" Size="Size.Small">
                <ItemContent>
                    <MudText Typo="Typo.body1" Style="font-weight: 500;">تم تسجيل مربي جديد</MudText>
                    <MudText Typo="Typo.caption">منذ ساعتين</MudText>
                </ItemContent>
            </MudTimelineItem>
            
            <MudTimelineItem Color="Color.Info" Size="Size.Small">
                <ItemContent>
                    <MudText Typo="Typo.body1" Style="font-weight: 500;">تم إضافة 5 حيوانات جديدة</MudText>
                    <MudText Typo="Typo.caption">منذ 4 ساعات</MudText>
                </ItemContent>
            </MudTimelineItem>
            
            <MudTimelineItem Color="Color.Warning" Size="Size.Small">
                <ItemContent>
                    <MudText Typo="Typo.body1" Style="font-weight: 500;">تم إجراء فحص مختبري</MudText>
                    <MudText Typo="Typo.caption">أمس</MudText>
                </ItemContent>
            </MudTimelineItem>
        </MudTimeline>
    </MudPaper>
</MudContainer>

@code {
    private List<Branch> branches = new();
    private int selectedBranchId = 0;
    
    // الإحصائيات
    private int totalBreeders = 0;
    private int totalAnimals = 0;
    private int totalTreatments = 0;
    private decimal totalRevenue = 0;
    private int treatmentsThisMonth = 0;
    private int labTestsThisMonth = 0;

    public class AlertItem
    {
        public string Message { get; set; } = "";
        public Severity Severity { get; set; }
    }

    private List<AlertItem> alerts = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadBranches();
        await LoadDashboardData();
        LoadAlerts();
    }

    private async Task LoadBranches()
    {
        try
        {
            branches = (await BranchService.GetBranchesAsync(isActive: true)).ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل الفروع: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadDashboardData()
    {
        try
        {
            // تحميل الإحصائيات الأساسية
            totalBreeders = 15; // مؤقت
            totalAnimals = 120; // مؤقت
            totalTreatments = 45; // مؤقت
            totalRevenue = 2500; // مؤقت
            treatmentsThisMonth = 12; // مؤقت
            labTestsThisMonth = 8; // مؤقت
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل البيانات: {ex.Message}", Severity.Error);
        }
    }

    private void LoadAlerts()
    {
        alerts = new List<AlertItem>
        {
            // يمكن إضافة تنبيهات حقيقية هنا
        };
    }

    private async Task OnBranchChanged()
    {
        await LoadDashboardData();
    }
}
