using System.ComponentModel.DataAnnotations;

namespace Baytara.Shared.Models;

// نموذج المرض
public class Disease
{
    public int Id { get; set; }
    
    [Required(ErrorMessage = "اسم المرض مطلوب")]
    [StringLength(100, ErrorMessage = "اسم المرض يجب أن يكون أقل من 100 حرف")]
    public string Name { get; set; } = string.Empty;
    
    [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
    public string? Description { get; set; }
    
    [StringLength(500, ErrorMessage = "الأعراض يجب أن تكون أقل من 500 حرف")]
    public string? Symptoms { get; set; }
    
    [StringLength(500, ErrorMessage = "طرق الوقاية يجب أن تكون أقل من 500 حرف")]
    public string? Prevention { get; set; }
    
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    // العلاقات
    public virtual ICollection<Treatment> Treatments { get; set; } = new List<Treatment>();
}
