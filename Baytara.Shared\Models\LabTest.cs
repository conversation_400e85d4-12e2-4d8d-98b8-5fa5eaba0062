using System.ComponentModel.DataAnnotations;

namespace Baytara.Shared.Models;

// نوع الفحص المختبري
public enum LabTestType
{
    BloodTest = 1,      // فحص دم
    UrineTest = 2,      // فحص بول
    StoolTest = 3,      // فحص براز
    SkinTest = 4,       // فحص جلد
    Biopsy = 5,         // خزعة
    Xray = 6,           // أشعة سينية
    Ultrasound = 7,     // موجات فوق صوتية
    Other = 8           // أخرى
}

// حالة الفحص
public enum LabTestStatus
{
    Pending = 1,        // في الانتظار
    InProgress = 2,     // جاري
    Completed = 3,      // مكتمل
    Cancelled = 4       // ملغي
}

// نموذج الفحص المختبري
public class LabTest
{
    public int Id { get; set; }
    
    [Required(ErrorMessage = "نوع الفحص مطلوب")]
    public LabTestType Type { get; set; }
    
    [Required(ErrorMessage = "اسم الفحص مطلوب")]
    [StringLength(100, ErrorMessage = "اسم الفحص يجب أن يكون أقل من 100 حرف")]
    public string TestName { get; set; } = string.Empty;
    
    [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
    public string? Description { get; set; }
    
    [Required(ErrorMessage = "تاريخ الطلب مطلوب")]
    public DateTime RequestDate { get; set; } = DateTime.UtcNow;
    
    public DateTime? SampleCollectionDate { get; set; }
    
    public DateTime? ResultDate { get; set; }
    
    [Required(ErrorMessage = "حالة الفحص مطلوبة")]
    public LabTestStatus Status { get; set; } = LabTestStatus.Pending;
    
    [StringLength(2000, ErrorMessage = "النتائج يجب أن تكون أقل من 2000 حرف")]
    public string? Results { get; set; }
    
    [StringLength(1000, ErrorMessage = "التفسير يجب أن يكون أقل من 1000 حرف")]
    public string? Interpretation { get; set; }
    
    [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
    public string? Notes { get; set; }
    
    [Range(0, double.MaxValue, ErrorMessage = "التكلفة يجب أن تكون أكبر من أو تساوي صفر")]
    public decimal Cost { get; set; } = 0;
    
    [StringLength(100, ErrorMessage = "اسم المختبر يجب أن يكون أقل من 100 حرف")]
    public string? LabName { get; set; }
    
    [StringLength(100, ErrorMessage = "اسم الفني يجب أن يكون أقل من 100 حرف")]
    public string? TechnicianName { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? LastUpdated { get; set; }
    
    // المفاتيح الخارجية
    [Required(ErrorMessage = "الحيوان مطلوب")]
    public int AnimalId { get; set; }
    public virtual Animal Animal { get; set; } = null!;
    
    public int? TreatmentId { get; set; }
    public virtual Treatment? Treatment { get; set; }
    
    public int BranchId { get; set; }
    public virtual Branch Branch { get; set; } = null!;
}
