using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Baytara.API.Data;
using Baytara.Shared.Models;

namespace Baytara.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class DiseasesController : ControllerBase
{
    private readonly BaytaraDbContext _context;

    public DiseasesController(BaytaraDbContext context)
    {
        _context = context;
    }

    // GET: api/Diseases
    [HttpGet]
    public async Task<ActionResult<IEnumerable<Disease>>> GetDiseases(
        [FromQuery] string? search = null,
        [FromQuery] bool? isActive = null)
    {
        var query = _context.Diseases.AsQueryable();

        if (!string.IsNullOrEmpty(search))
        {
            query = query.Where(d => d.Name.Contains(search) || 
                                   (d.Description != null && d.Description.Contains(search)) ||
                                   (d.Symptoms != null && d.Symptoms.Contains(search)));
        }

        if (isActive.HasValue)
        {
            query = query.Where(d => d.IsActive == isActive.Value);
        }

        var diseases = await query
            .OrderBy(d => d.Name)
            .ToListAsync();

        return Ok(diseases);
    }

    // GET: api/Diseases/5
    [HttpGet("{id}")]
    public async Task<ActionResult<Disease>> GetDisease(int id)
    {
        var disease = await _context.Diseases.FindAsync(id);

        if (disease == null)
        {
            return NotFound();
        }

        return Ok(disease);
    }

    // POST: api/Diseases
    [HttpPost]
    public async Task<ActionResult<Disease>> CreateDisease(Disease disease)
    {
        // التحقق من عدم تكرار اسم المرض
        var existingDisease = await _context.Diseases
            .FirstOrDefaultAsync(d => d.Name == disease.Name);

        if (existingDisease != null)
        {
            return BadRequest("اسم المرض موجود مسبقاً");
        }

        disease.CreatedAt = DateTime.UtcNow;
        _context.Diseases.Add(disease);
        await _context.SaveChangesAsync();

        return CreatedAtAction(nameof(GetDisease), new { id = disease.Id }, disease);
    }

    // PUT: api/Diseases/5
    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateDisease(int id, Disease disease)
    {
        if (id != disease.Id)
        {
            return BadRequest();
        }

        var existingDisease = await _context.Diseases.FindAsync(id);
        if (existingDisease == null)
        {
            return NotFound();
        }

        // التحقق من عدم تكرار اسم المرض
        var duplicateDisease = await _context.Diseases
            .FirstOrDefaultAsync(d => d.Name == disease.Name && d.Id != id);

        if (duplicateDisease != null)
        {
            return BadRequest("اسم المرض موجود مسبقاً");
        }

        existingDisease.Name = disease.Name;
        existingDisease.Description = disease.Description;
        existingDisease.Symptoms = disease.Symptoms;
        existingDisease.Prevention = disease.Prevention;
        existingDisease.IsActive = disease.IsActive;

        try
        {
            await _context.SaveChangesAsync();
        }
        catch (DbUpdateConcurrencyException)
        {
            if (!DiseaseExists(id))
            {
                return NotFound();
            }
            throw;
        }

        return NoContent();
    }

    // DELETE: api/Diseases/5
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteDisease(int id)
    {
        var disease = await _context.Diseases
            .Include(d => d.Treatments)
            .FirstOrDefaultAsync(d => d.Id == id);

        if (disease == null)
        {
            return NotFound();
        }

        // التحقق من وجود علاجات مرتبطة
        if (disease.Treatments.Any())
        {
            return BadRequest("لا يمكن حذف المرض لوجود علاجات مرتبطة به");
        }

        _context.Diseases.Remove(disease);
        await _context.SaveChangesAsync();

        return NoContent();
    }

    private bool DiseaseExists(int id)
    {
        return _context.Diseases.Any(e => e.Id == id);
    }
}
