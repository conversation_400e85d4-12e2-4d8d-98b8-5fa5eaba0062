@page "/treatments"
@inject ITreatmentService TreatmentService
@inject IBranchService BranchService
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<PageTitle>العلاجات البيطرية - نظام بيطره</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge">
    <!-- العنوان وشريط البحث -->
    <MudPaper Class="pa-4 mb-4" Elevation="2">
        <MudGrid AlignItems="Center">
            <MudItem xs="12" md="6">
                <MudText Typo="Typo.h4" Color="Color.Primary">
                    <MudIcon Icon="@Icons.Material.Filled.MedicalServices" Class="ml-2" />
                    💊 العلاجات البيطرية
                </MudText>
                <MudText Typo="Typo.body1" Class="mt-2">
                    إدارة العلاجات والخدمات البيطرية المقدمة للحيوانات
                </MudText>
            </MudItem>
            <MudItem xs="12" md="6" Class="text-left">
                <MudButton Variant="Variant.Filled" 
                          Color="Color.Primary" 
                          StartIcon="@Icons.Material.Filled.Add"
                          Href="/treatments/add">
                    تسجيل علاج جديد
                </MudButton>
            </MudItem>
        </MudGrid>
        
        <!-- شريط البحث والفلاتر -->
        <MudGrid Class="mt-4">
            <MudItem xs="12" md="4">
                <MudTextField @bind-Value="searchText" 
                             Label="البحث" 
                             Placeholder="البحث بالأعراض أو التشخيص أو اسم الطبيب"
                             Adornment="Adornment.Start" 
                             AdornmentIcon="@Icons.Material.Filled.Search"
                             OnKeyUp="@(async (e) => { if (e.Key == "Enter") await SearchTreatments(); })" />
            </MudItem>
            <MudItem xs="12" md="3">
                <MudSelect @bind-Value="selectedBranchId" Label="الفرع" Clearable="true">
                    @foreach (var branch in branches)
                    {
                        <MudSelectItem Value="@branch.Id">@branch.Name</MudSelectItem>
                    }
                </MudSelect>
            </MudItem>
            <MudItem xs="12" md="3">
                <MudSelect @bind-Value="selectedStatus" Label="حالة العلاج" Clearable="true">
                    <MudSelectItem Value="@TreatmentStatus.Planned">مخطط</MudSelectItem>
                    <MudSelectItem Value="@TreatmentStatus.InProgress">جاري</MudSelectItem>
                    <MudSelectItem Value="@TreatmentStatus.Completed">مكتمل</MudSelectItem>
                    <MudSelectItem Value="@TreatmentStatus.Cancelled">ملغي</MudSelectItem>
                </MudSelect>
            </MudItem>
            <MudItem xs="12" md="2">
                <MudButton Variant="Variant.Filled" 
                          Color="Color.Secondary" 
                          FullWidth="true"
                          OnClick="SearchTreatments">
                    بحث
                </MudButton>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- جدول العلاجات -->
    <MudPaper Class="pa-4" Elevation="2">
        @if (isLoading)
        {
            <div class="text-center pa-8">
                <MudProgressCircular Indeterminate="true" />
                <MudText Class="mt-4">جاري تحميل البيانات...</MudText>
            </div>
        }
        else if (!treatments.Any())
        {
            <div class="text-center pa-8">
                <MudIcon Icon="@Icons.Material.Filled.SearchOff" Size="Size.Large" Color="Color.Secondary" />
                <MudText Typo="Typo.h6" Class="mt-4">لا توجد نتائج</MudText>
                <MudText>لم يتم العثور على علاجات مطابقة لمعايير البحث</MudText>
            </div>
        }
        else
        {
            <MudTable Items="@treatments" Hover="true" Striped="true" Dense="true">
                <HeaderContent>
                    <MudTh>تاريخ العلاج</MudTh>
                    <MudTh>الحيوان</MudTh>
                    <MudTh>المربي</MudTh>
                    <MudTh>التشخيص</MudTh>
                    <MudTh>الطبيب البيطري</MudTh>
                    <MudTh>الحالة</MudTh>
                    <MudTh>التكلفة (ر.ع)</MudTh>
                    <MudTh>الإجراءات</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd DataLabel="تاريخ العلاج">
                        -
                    </MudTd>
                    <MudTd DataLabel="الحيوان">
                        <div>
                            <MudText Typo="Typo.body2" Style="font-weight: 500;">
                                -
                            </MudText>
                            <MudText Typo="Typo.caption">-</MudText>
                        </div>
                    </MudTd>
                    <MudTd DataLabel="المربي">
                        <div>
                            <MudText Typo="Typo.body2">-</MudText>
                            <MudText Typo="Typo.caption">-</MudText>
                        </div>
                    </MudTd>
                    <MudTd DataLabel="التشخيص">
                        <span>-</span>
                    </MudTd>
                    <MudTd DataLabel="الطبيب البيطري">
                        -
                    </MudTd>
                    <MudTd DataLabel="الحالة">
                        <MudChip T="string" Size="Size.Small" Color="Color.Default">
                            -
                        </MudChip>
                    </MudTd>
                    <MudTd DataLabel="التكلفة">
                        0.00
                    </MudTd>
                    <MudTd DataLabel="الإجراءات">
                        <MudButtonGroup Variant="Variant.Text" Size="Size.Small">
                            <MudIconButton Icon="@Icons.Material.Filled.Visibility" 
                                          Color="Color.Info" 
                                          Size="Size.Small"
                                          OnClick="@(() => ViewTreatment(context))"
                                          Title="عرض التفاصيل" />
                            <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                          Color="Color.Primary"
                                          Size="Size.Small"
                                          Href="/treatments/edit/1"
                                          Title="تعديل" />
                            <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                          Color="Color.Error"
                                          Size="Size.Small"
                                          OnClick="@(() => DeleteTreatment(1))"
                                          Title="حذف" />
                        </MudButtonGroup>
                    </MudTd>
                </RowTemplate>
            </MudTable>

            <!-- التصفح -->
            <div class="d-flex justify-center mt-4">
                <MudPagination Count="@totalPages" 
                              Selected="@currentPage" 
                              SelectedChanged="@OnPageChanged" 
                              ShowFirstButton="true" 
                              ShowLastButton="true" />
            </div>
        }
    </MudPaper>
</MudContainer>

<!-- حوار عرض تفاصيل العلاج -->
<MudDialog @bind-IsVisible="showDetailsDialog" Options="dialogOptions">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.MedicalServices" Class="ml-2" />
            تفاصيل العلاج
        </MudText>
    </TitleContent>
    <DialogContent>
        <MudGrid>
            <MudItem xs="12" md="6">
                <MudText Typo="Typo.subtitle2" Style="font-weight: 500;">تاريخ العلاج:</MudText>
                <MudText Typo="Typo.body2">-</MudText>
            </MudItem>

            <MudItem xs="12" md="6">
                <MudText Typo="Typo.subtitle2" Style="font-weight: 500;">الحالة:</MudText>
                <MudChip T="string" Size="Size.Small" Color="Color.Default">
                    -
                </MudChip>
            </MudItem>

            <MudItem xs="12">
                <MudText Typo="Typo.subtitle2" Style="font-weight: 500;">الأعراض:</MudText>
                <MudText Typo="Typo.body2">-</MudText>
            </MudItem>

            <MudItem xs="12">
                <MudText Typo="Typo.subtitle2" Style="font-weight: 500;">التشخيص:</MudText>
                <MudText Typo="Typo.body2">-</MudText>
            </MudItem>

            <MudItem xs="12">
                <MudText Typo="Typo.subtitle2" Style="font-weight: 500;">خطة العلاج:</MudText>
                <MudText Typo="Typo.body2">-</MudText>
            </MudItem>

            <MudItem xs="12" md="6">
                <MudText Typo="Typo.subtitle2" Style="font-weight: 500;">التكلفة:</MudText>
                <MudText Typo="Typo.body2">0.00 ر.ع</MudText>
            </MudItem>

            <MudItem xs="12" md="6">
                <MudText Typo="Typo.subtitle2" Style="font-weight: 500;">الطبيب البيطري:</MudText>
                <MudText Typo="Typo.body2">-</MudText>
            </MudItem>
        </MudGrid>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="CloseDetailsDialog">إغلاق</MudButton>
    </DialogActions>
</MudDialog>

@code {
    private List<object> treatments = new();
    private List<Branch> branches = new();
    private bool isLoading = true;
    private string searchText = "";
    private int? selectedBranchId = null;
    private TreatmentStatus? selectedStatus = null;
    private int currentPage = 1;
    private int pageSize = 10;
    private int totalPages = 1;
    private bool showDetailsDialog = false;
    private object? selectedTreatment = null;
    
    private DialogOptions dialogOptions = new() { MaxWidth = MaxWidth.Large, FullWidth = true };

    protected override async Task OnInitializedAsync()
    {
        await LoadBranches();
        await LoadTreatments();
    }

    private async Task LoadBranches()
    {
        try
        {
            branches = (await BranchService.GetBranchesAsync(isActive: true)).ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل الفروع: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadTreatments()
    {
        isLoading = true;
        try
        {
            var result = await TreatmentService.GetTreatmentsAsync(
                search: string.IsNullOrEmpty(searchText) ? null : searchText,
                branchId: selectedBranchId,
                page: currentPage,
                pageSize: pageSize
            );
            
            treatments = result.ToList();
            totalPages = Math.Max(1, (int)Math.Ceiling(treatments.Count / (double)pageSize));
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل العلاجات: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task SearchTreatments()
    {
        currentPage = 1;
        await LoadTreatments();
    }

    private async Task OnPageChanged(int page)
    {
        currentPage = page;
        await LoadTreatments();
    }

    private void ViewTreatment(dynamic treatment)
    {
        selectedTreatment = treatment;
        showDetailsDialog = true;
    }

    private void CloseDetailsDialog()
    {
        showDetailsDialog = false;
        selectedTreatment = null;
    }

    private async Task DeleteTreatment(int treatmentId)
    {
        try
        {
            await TreatmentService.DeleteTreatmentAsync(treatmentId);
            await LoadTreatments();
            Snackbar.Add("تم حذف العلاج بنجاح", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في حذف العلاج: {ex.Message}", Severity.Error);
        }
    }

    private Color GetStatusColor(TreatmentStatus status)
    {
        return status switch
        {
            TreatmentStatus.Planned => Color.Info,
            TreatmentStatus.InProgress => Color.Warning,
            TreatmentStatus.Completed => Color.Success,
            TreatmentStatus.Cancelled => Color.Error,
            _ => Color.Default
        };
    }

    private string GetStatusText(TreatmentStatus status)
    {
        return status switch
        {
            TreatmentStatus.Planned => "مخطط",
            TreatmentStatus.InProgress => "جاري",
            TreatmentStatus.Completed => "مكتمل",
            TreatmentStatus.Cancelled => "ملغي",
            _ => "غير محدد"
        };
    }
}
