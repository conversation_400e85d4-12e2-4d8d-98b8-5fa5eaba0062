@page "/breeders"
@inject IBreederService BreederService
@inject IBranchService BranchService
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<PageTitle>المربين - نظام بيطره</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge">
    <!-- العنوان وشريط البحث -->
    <MudPaper Class="pa-4 mb-4" Elevation="2">
        <MudGrid AlignItems="Center">
            <MudItem xs="12" md="6">
                <MudText Typo="Typo.h4" Color="Color.Primary">
                    <MudIcon Icon="@Icons.Material.Filled.People" Class="ml-2" />
                    المربين
                </MudText>
                <MudText Typo="Typo.body1" Class="mt-2">
                    إدارة المربين المسجلين في النظام
                </MudText>
            </MudItem>
            <MudItem xs="12" md="6" Class="text-left">
                <MudButton Variant="Variant.Filled"
                          Color="Color.Primary"
                          StartIcon="@Icons.Material.Filled.Add"
                          Href="/breeders/add">
                    إضافة مربي جديد
                </MudButton>
            </MudItem>
        </MudGrid>
        
        <!-- شريط البحث والفلاتر -->
        <MudGrid Class="mt-4">
            <MudItem xs="12" md="4">
                <MudTextField @bind-Value="searchText" 
                             Label="البحث" 
                             Placeholder="البحث بالاسم، الهاتف، أو رقم الهوية"
                             Adornment="Adornment.Start" 
                             AdornmentIcon="@Icons.Material.Filled.Search"
                             OnKeyUp="@(async (e) => { if (e.Key == "Enter") await SearchBreeders(); })" />
            </MudItem>
            <MudItem xs="12" md="3">
                <MudSelect T="int?" @bind-Value="selectedBranchId" Label="الفرع" Clearable="true">
                    @foreach (var branch in branches)
                    {
                        <MudSelectItem T="int?" Value="@branch.Id">@branch.Name</MudSelectItem>
                    }
                </MudSelect>
            </MudItem>
            <MudItem xs="12" md="3">
                <MudTextField @bind-Value="regionFilter" 
                             Label="المنطقة" 
                             Placeholder="فلترة حسب المنطقة" />
            </MudItem>
            <MudItem xs="12" md="2">
                <MudButton Variant="Variant.Filled" 
                          Color="Color.Secondary" 
                          FullWidth="true"
                          OnClick="SearchBreeders">
                    بحث
                </MudButton>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- جدول المربين -->
    <MudPaper Class="pa-4" Elevation="2">
        @if (isLoading)
        {
            <div class="text-center pa-8">
                <MudProgressCircular Indeterminate="true" />
                <MudText Class="mt-4">جاري تحميل البيانات...</MudText>
            </div>
        }
        else if (!breeders.Any())
        {
            <div class="text-center pa-8">
                <MudIcon Icon="@Icons.Material.Filled.SearchOff" Size="Size.Large" Color="Color.Secondary" />
                <MudText Typo="Typo.h6" Class="mt-4">لا توجد نتائج</MudText>
                <MudText>لم يتم العثور على مربين مطابقين لمعايير البحث</MudText>
            </div>
        }
        else
        {
            <MudTable Items="@breeders" Hover="true" Striped="true" Dense="true">
                <HeaderContent>
                    <MudTh>الاسم</MudTh>
                    <MudTh>رقم الهاتف</MudTh>
                    <MudTh>المنطقة</MudTh>
                    <MudTh>الفرع</MudTh>
                    <MudTh>عدد الحيوانات</MudTh>
                    <MudTh>الإجراءات</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd DataLabel="الاسم">
                        <MudText Typo="Typo.body2" Style="font-weight: 500;">@context.Name</MudText>
                    </MudTd>
                    <MudTd DataLabel="رقم الهاتف">@context.Phone</MudTd>
                    <MudTd DataLabel="المنطقة">@(context.Region ?? "-")</MudTd>
                    <MudTd DataLabel="الفرع">@context.BranchName</MudTd>
                    <MudTd DataLabel="عدد الحيوانات">
                        <MudChip T="string" Size="Size.Small" Color="Color.Info">@context.AnimalsCount</MudChip>
                    </MudTd>
                    <MudTd DataLabel="الإجراءات">
                        <MudButtonGroup Variant="Variant.Text" Size="Size.Small">
                            <MudIconButton Icon="@Icons.Material.Filled.Visibility"
                                          Color="Color.Success"
                                          Size="Size.Small"
                                          OnClick="@(() => ViewBreederAnimals(context))"
                                          Title="عرض التفاصيل" />
                            <MudIconButton Icon="@Icons.Material.Filled.Pets"
                                          Color="Color.Info"
                                          Size="Size.Small"
                                          OnClick="@(() => AddAnimalToBreeder(context))"
                                          Title="إضافة حيوان" />
                            <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                          Color="Color.Primary"
                                          Size="Size.Small"
                                          Href="@($"/breeders/edit/{context.Id}")"
                                          Title="تعديل المربي" />
                            <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                          Color="Color.Error"
                                          Size="Size.Small"
                                          OnClick="@(() => ShowDeleteConfirmation(context))"
                                          Title="حذف المربي" />
                        </MudButtonGroup>
                    </MudTd>
                </RowTemplate>
            </MudTable>

            <!-- التصفح -->
            <div class="d-flex justify-center mt-4">
                <MudPagination Count="@totalPages" 
                              Selected="@currentPage" 
                              SelectedChanged="@OnPageChanged" 
                              ShowFirstButton="true" 
                              ShowLastButton="true" />
            </div>
        }
    </MudPaper>
</MudContainer>

<!-- حوار عرض حيوانات المربي -->
<MudDialog @bind-IsVisible="showAnimalsDialog" Options="animalsDialogOptions">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.Pets" Class="ml-2" />
            حيوانات المربي: @selectedBreeder?.Name
        </MudText>
    </TitleContent>
    <DialogContent>
        @if (breederAnimals.Any())
        {
            <MudTable Items="@breederAnimals" Dense="true" Hover="true">
                <HeaderContent>
                    <MudTh>النوع</MudTh>
                    <MudTh>الاسم</MudTh>
                    <MudTh>العدد</MudTh>
                    <MudTh>الحالة</MudTh>
                    <MudTh>الإجراءات</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd>-</MudTd>
                    <MudTd>-</MudTd>
                    <MudTd>0</MudTd>
                    <MudTd>
                        <MudChip T="string" Size="Size.Small" Color="Color.Default">
                            -
                        </MudChip>
                    </MudTd>
                    <MudTd>
                        <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                      Size="Size.Small"
                                      Color="Color.Primary"
                                      OnClick="@(() => EditAnimal(context))" />
                        <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                      Size="Size.Small"
                                      Color="Color.Error"
                                      OnClick="@(() => DeleteAnimal(1))" />
                    </MudTd>
                </RowTemplate>
            </MudTable>
        }
        else
        {
            <div class="text-center pa-4">
                <MudIcon Icon="@Icons.Material.Filled.Pets" Size="Size.Large" Color="Color.Secondary" />
                <MudText Typo="Typo.h6" Class="mt-2">لا توجد حيوانات</MudText>
                <MudText>لم يتم تسجيل أي حيوانات لهذا المربي بعد</MudText>
            </div>
        }
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="CloseAnimalsDialog">إغلاق</MudButton>
        <MudButton Color="Color.Primary"
                  Variant="Variant.Filled"
                  StartIcon="@Icons.Material.Filled.Pets"
                  OnClick="@(() => AddAnimalToBreeder(selectedBreeder!))">
            إضافة حيوان جديد
        </MudButton>
    </DialogActions>
</MudDialog>

<!-- حوار إضافة حيوانات جديدة -->
<MudDialog @bind-IsVisible="showAddAnimalDialog" Options="addAnimalDialogOptions">
    <TitleContent>
        <div class="d-flex align-center">
            <div class="d-flex align-center justify-center"
                 style="width: 40px; height: 40px; background: linear-gradient(135deg, #4CAF50, #45a049); border-radius: 50%; margin-left: 12px;">
                <MudIcon Icon="@Icons.Material.Filled.Pets" Style="color: white; font-size: 20px;" />
            </div>
            <div>
                <MudText Typo="Typo.h6" Style="color: #2c3e50; font-weight: 600;">
                    إضافة حيوانات جديدة
                </MudText>
                <MudText Typo="Typo.caption" Style="color: #7f8c8d;">
                    للمربي: @selectedBreeder?.Name
                </MudText>
            </div>
        </div>
    </TitleContent>
    <DialogContent>
        <div style="max-height: 600px; overflow-y: auto;">
            <!-- قائمة الحيوانات المضافة -->
            @if (animalsToAdd.Any())
            {
                <MudPaper Class="pa-3 mb-4" Style="background: linear-gradient(135deg, #f8f9fa, #e9ecef); border-radius: 12px;">
                    <MudText Typo="Typo.subtitle1" Style="color: #495057; font-weight: 600; margin-bottom: 12px;">
                        <MudIcon Icon="@Icons.Material.Filled.List" Class="ml-1" />
                        الحيوانات المضافة (@animalsToAdd.Count)
                    </MudText>

                    @foreach (var animal in animalsToAdd.Select((value, index) => new { value, index }))
                    {
                        <MudCard Class="mb-2" Style="border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                            <MudCardContent Class="pa-3">
                                <div class="d-flex justify-space-between align-center">
                                    <div class="d-flex align-center">
                                        <MudIcon Icon="@GetAnimalTypeIcon(animal.value.AnimalTypeId)"
                                                Style="color: #4CAF50; margin-left: 8px; font-size: 20px;" />
                                        <div>
                                            <MudText Typo="Typo.body1" Style="font-weight: 500;">
                                                @GetAnimalTypeName(animal.value.AnimalTypeId)
                                                @if (!string.IsNullOrEmpty(animal.value.Name))
                                                {
                                                    <span style="color: #6c757d;"> - @animal.value.Name</span>
                                                }
                                            </MudText>
                                            <MudText Typo="Typo.caption" Style="color: #6c757d;">
                                                العدد: @animal.value.Count | التصنيف: @GetCategoryName(animal.value.Category)
                                            </MudText>
                                        </div>
                                    </div>
                                    <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                  Color="Color.Error"
                                                  Size="Size.Small"
                                                  OnClick="@(() => RemoveAnimalFromList(animal.index))"
                                                  Style="background: rgba(244, 67, 54, 0.1); border-radius: 50%;" />
                                </div>
                            </MudCardContent>
                        </MudCard>
                    }
                </MudPaper>
            }

            <!-- نموذج إضافة حيوان جديد -->
            <MudPaper Class="pa-4" Style="border-radius: 12px; border: 2px dashed #dee2e6;">
                <MudText Typo="Typo.subtitle1" Style="color: #495057; font-weight: 600; margin-bottom: 16px;">
                    <MudIcon Icon="@Icons.Material.Filled.Add" Class="ml-1" />
                    إضافة حيوان جديد
                </MudText>

                <MudGrid>
                    <MudItem xs="12" md="6">
                        <MudSelect @bind-Value="newAnimal.AnimalTypeId"
                                  Label="نوع الحيوان"
                                  Required="true"
                                  OnSelectionChanged="OnAnimalTypeChanged"
                                  Style="background: white;">
                            @foreach (var type in animalTypes)
                            {
                                <MudSelectItem Value="@type.Id">
                                    <div class="d-flex align-center">
                                        <MudIcon Icon="@GetAnimalTypeIcon(type.Id)" Class="ml-2" />
                                        @type.Name
                                    </div>
                                </MudSelectItem>
                            }
                        </MudSelect>
                    </MudItem>

                    <MudItem xs="12" md="6">
                        <MudTextField @bind-Value="newAnimal.Name"
                                     Label="اسم الحيوان (اختياري)"
                                     Style="background: white;" />
                    </MudItem>

                    <MudItem xs="12" md="6">
                        <MudNumericField @bind-Value="newAnimal.Count"
                                        Label="العدد"
                                        Min="1"
                                        Required="true"
                                        Style="background: white;" />
                    </MudItem>

                    <MudItem xs="12" md="6">
                        <MudSelect @bind-Value="newAnimal.Category"
                                  Label="التصنيف"
                                  Style="background: white;">
                            @foreach (var category in GetAvailableCategories())
                            {
                                <MudSelectItem Value="@category.Value">
                                    <div class="d-flex align-center">
                                        <MudIcon Icon="@category.Icon" Class="ml-2" style="@($"color: {category.Color};")" />
                                        @category.Name
                                    </div>
                                </MudSelectItem>
                            }
                        </MudSelect>
                    </MudItem>

                    <MudItem xs="12">
                        <MudTextField @bind-Value="newAnimal.Notes"
                                     Label="ملاحظات"
                                     Lines="2"
                                     Style="background: white;" />
                    </MudItem>

                    <MudItem xs="12">
                        <MudButton Color="Color.Success"
                                  Variant="Variant.Filled"
                                  StartIcon="@Icons.Material.Filled.Add"
                                  OnClick="AddAnimalToList"
                                  Disabled="@(newAnimal.AnimalTypeId == 0)"
                                  Style="border-radius: 8px;">
                            إضافة إلى القائمة
                        </MudButton>
                    </MudItem>
                </MudGrid>
            </MudPaper>
        </div>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="CloseAddAnimalDialog"
                  Style="border-radius: 8px;">
            إلغاء
        </MudButton>
        <MudButton Color="Color.Primary"
                  Variant="Variant.Filled"
                  OnClick="SaveAllAnimals"
                  Disabled="@(isSavingAnimal || !animalsToAdd.Any())"
                  Style="border-radius: 8px;">
            @if (isSavingAnimal)
            {
                <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                <span class="ml-2">جاري الحفظ...</span>
            }
            else
            {
                <MudIcon Icon="@Icons.Material.Filled.Save" Class="ml-1" />
                <span>حفظ جميع الحيوانات (@animalsToAdd.Count)</span>
            }
        </MudButton>
    </DialogActions>
</MudDialog>

<!-- حوار تأكيد الحذف العصري -->
<MudDialog @bind-IsVisible="showDeleteDialog" Options="deleteDialogOptions">
    <TitleContent>
        <div class="d-flex align-center">
            <div class="d-flex align-center justify-center"
                 style="width: 48px; height: 48px; background: linear-gradient(135deg, #ff6b6b, #ee5a52); border-radius: 50%; margin-left: 12px;">
                <MudIcon Icon="@Icons.Material.Filled.DeleteForever"
                         Style="color: white; font-size: 24px;" />
            </div>
            <div>
                <MudText Typo="Typo.h6" Style="color: #2c3e50; font-weight: 600;">
                    حذف المربي
                </MudText>
                <MudText Typo="Typo.caption" Style="color: #7f8c8d;">
                    تأكيد عملية الحذف
                </MudText>
            </div>
        </div>
    </TitleContent>
    <DialogContent>
        <div class="pa-4">
            <div class="text-center mb-4">
                <div class="d-flex align-center justify-center mb-3"
                     style="width: 80px; height: 80px; background: linear-gradient(135deg, #ff9ff3, #f368e0); border-radius: 50%; margin: 0 auto;">
                    <MudIcon Icon="@Icons.Material.Filled.Person"
                             Style="color: white; font-size: 40px;" />
                </div>
                <MudText Typo="Typo.h6" Style="color: #2c3e50; margin-bottom: 8px;">
                    @breederToDelete?.Name
                </MudText>
                <MudText Typo="Typo.body2" Style="color: #7f8c8d;">
                    هل تريد حذف هذا المربي نهائياً؟
                </MudText>
            </div>

            <MudAlert Severity="Severity.Warning"
                      Style="background: linear-gradient(135deg, #fff3cd, #ffeaa7); border: none; border-radius: 12px;">
                <div class="d-flex align-center">
                    <MudIcon Icon="@Icons.Material.Filled.Warning"
                             Style="color: #f39c12; margin-left: 8px;" />
                    <div>
                        <MudText Typo="Typo.body2" Style="color: #8b4513; font-weight: 500;">
                            تحذير مهم
                        </MudText>
                        <MudText Typo="Typo.caption" Style="color: #8b4513;">
                            سيتم حذف جميع البيانات والحيوانات المرتبطة بهذا المربي ولا يمكن التراجع عن هذا الإجراء
                        </MudText>
                    </div>
                </div>
            </MudAlert>
        </div>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="CloseDeleteDialog"
                   Variant="Variant.Text"
                   Style="color: #7f8c8d; border-radius: 8px; padding: 8px 24px;">
            إلغاء
        </MudButton>
        <MudButton OnClick="ConfirmDelete"
                   Variant="Variant.Filled"
                   Disabled="@isDeletingBreeder"
                   Style="background: linear-gradient(135deg, #ff6b6b, #ee5a52); color: white; border-radius: 8px; padding: 8px 24px; box-shadow: 0 4px 12px rgba(238, 90, 82, 0.3);">
            @if (isDeletingBreeder)
            {
                <MudProgressCircular Class="ml-2" Size="Size.Small" Indeterminate="true" />
                <span>جاري الحذف...</span>
            }
            else
            {
                <MudIcon Icon="@Icons.Material.Filled.Delete" Class="ml-1" />
                <span>حذف نهائي</span>
            }
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    private List<BreederDto> breeders = new();
    private List<Branch> branches = new();
    private List<object> breederAnimals = new();
    private List<AnimalType> animalTypes = new();
    private bool isLoading = true;
    private string searchText = "";
    private string regionFilter = "";
    private int? selectedBranchId = null;
    private int currentPage = 1;
    private int pageSize = 10;
    private int totalPages = 1;

    // حوارات
    private bool showAnimalsDialog = false;
    private bool showAddAnimalDialog = false;
    private bool showDeleteDialog = false;
    private bool isSavingAnimal = false;
    private bool isDeletingBreeder = false;
    private BreederDto? selectedBreeder = null;
    private BreederDto? breederToDelete = null;
    private Animal newAnimal = new();
    private List<Animal> animalsToAdd = new();

    private DialogOptions animalsDialogOptions = new() { MaxWidth = MaxWidth.Large, FullWidth = true };
    private DialogOptions addAnimalDialogOptions = new() { MaxWidth = MaxWidth.Large, FullWidth = true };
    private DialogOptions deleteDialogOptions = new() { MaxWidth = MaxWidth.Small, FullWidth = true };

    protected override async Task OnInitializedAsync()
    {
        await LoadBranches();
        await LoadAnimalTypes();
        await LoadBreeders();
    }

    private async Task LoadBranches()
    {
        try
        {
            branches = (await BranchService.GetBranchesAsync(isActive: true)).ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل الفروع: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadBreeders()
    {
        isLoading = true;
        try
        {
            var result = await BreederService.GetBreedersAsync(
                search: string.IsNullOrEmpty(searchText) ? null : searchText,
                branchId: selectedBranchId,
                region: string.IsNullOrEmpty(regionFilter) ? null : regionFilter,
                page: currentPage,
                pageSize: pageSize
            );
            
            breeders = result.ToList();
            // حساب عدد الصفحات (يجب إضافة header للعدد الكلي من الـ API)
            totalPages = Math.Max(1, (int)Math.Ceiling(breeders.Count / (double)pageSize));
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل المربين: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task SearchBreeders()
    {
        currentPage = 1;
        await LoadBreeders();
    }

    private async Task OnPageChanged(int page)
    {
        currentPage = page;
        await LoadBreeders();
    }



    private async Task LoadAnimalTypes()
    {
        try
        {
            // يجب إضافة خدمة أنواع الحيوانات
            animalTypes = new List<AnimalType>
            {
                new AnimalType { Id = 1, Name = "إبل" },
                new AnimalType { Id = 2, Name = "أبقار" },
                new AnimalType { Id = 3, Name = "أغنام" },
                new AnimalType { Id = 4, Name = "ماعز" },
                new AnimalType { Id = 5, Name = "دواجن" }
            };
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل أنواع الحيوانات: {ex.Message}", Severity.Error);
        }
    }

    private async Task ViewBreederAnimals(BreederDto breeder)
    {
        selectedBreeder = breeder;
        try
        {
            // تحميل حيوانات المربي
            breederAnimals = new List<object>(); // مؤقت
            showAnimalsDialog = true;
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل حيوانات المربي: {ex.Message}", Severity.Error);
        }
    }

    private void AddAnimalToBreeder(BreederDto breeder)
    {
        try
        {
            selectedBreeder = breeder;
            newAnimal = new Animal
            {
                BreederId = breeder.Id,
                BranchId = breeder.BranchId,
                Category = AnimalCategory.Free,
                Status = AnimalStatus.Healthy,
                Count = 1
            };
            animalsToAdd.Clear();
            showAddAnimalDialog = true;
            StateHasChanged();
            // تم فتح حوار إضافة الحيوان بنجاح
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في فتح حوار إضافة الحيوان: {ex.Message}", Severity.Error);
        }
    }

    private void CloseAnimalsDialog()
    {
        showAnimalsDialog = false;
        selectedBreeder = null;
    }

    private void CloseAddAnimalDialog()
    {
        showAddAnimalDialog = false;
        selectedBreeder = null;
        newAnimal = new Animal();
        animalsToAdd.Clear();
    }

    private async Task SaveNewAnimal()
    {
        isSavingAnimal = true;
        try
        {
            // حفظ الحيوان الجديد
            Snackbar.Add("تم إضافة الحيوان بنجاح", Severity.Success);
            CloseAddAnimalDialog();
            await LoadBreeders(); // إعادة تحميل لتحديث عدد الحيوانات
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في إضافة الحيوان: {ex.Message}", Severity.Error);
        }
        finally
        {
            isSavingAnimal = false;
        }
    }

    // إضافة حيوان إلى القائمة
    private void AddAnimalToList()
    {
        if (newAnimal.AnimalTypeId == 0) return;

        var animalToAdd = new Animal
        {
            AnimalTypeId = newAnimal.AnimalTypeId,
            Name = newAnimal.Name,
            Count = newAnimal.Count,
            Category = newAnimal.Category,
            Status = AnimalStatus.Healthy,
            Notes = newAnimal.Notes,
            BreederId = selectedBreeder!.Id,
            BranchId = selectedBreeder!.BranchId
        };

        animalsToAdd.Add(animalToAdd);

        // إعادة تعيين النموذج
        newAnimal = new Animal
        {
            BreederId = selectedBreeder!.Id,
            BranchId = selectedBreeder!.BranchId,
            Category = AnimalCategory.Free,
            Status = AnimalStatus.Healthy,
            Count = 1
        };

        StateHasChanged();
    }

    // إزالة حيوان من القائمة
    private void RemoveAnimalFromList(int index)
    {
        if (index >= 0 && index < animalsToAdd.Count)
        {
            animalsToAdd.RemoveAt(index);
            StateHasChanged();
        }
    }

    // حفظ جميع الحيوانات
    private async Task SaveAllAnimals()
    {
        if (!animalsToAdd.Any()) return;

        isSavingAnimal = true;
        try
        {
            foreach (var animal in animalsToAdd)
            {
                // هنا سيتم استدعاء API لحفظ كل حيوان
                // await AnimalService.CreateAnimalAsync(animal);
            }

            Snackbar.Add($"تم إضافة {animalsToAdd.Count} حيوان بنجاح", Severity.Success);
            CloseAddAnimalDialog();
            await LoadBreeders(); // إعادة تحميل لتحديث عدد الحيوانات
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في إضافة الحيوانات: {ex.Message}", Severity.Error);
        }
        finally
        {
            isSavingAnimal = false;
        }
    }

    private void EditAnimal(object animal)
    {
        // تنفيذ تعديل الحيوان
        Snackbar.Add("سيتم تنفيذ تعديل الحيوان قريباً", Severity.Info);
    }

    private async Task DeleteAnimal(int animalId)
    {
        try
        {
            // حذف الحيوان
            Snackbar.Add("تم حذف الحيوان بنجاح", Severity.Success);
            await ViewBreederAnimals(selectedBreeder!); // إعادة تحميل القائمة
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في حذف الحيوان: {ex.Message}", Severity.Error);
        }
    }

    private Color GetAnimalStatusColor(AnimalStatus status)
    {
        return status switch
        {
            AnimalStatus.Healthy => Color.Success,
            AnimalStatus.Sick => Color.Warning,
            AnimalStatus.UnderTreatment => Color.Info,
            AnimalStatus.Dead => Color.Dark,
            _ => Color.Default
        };
    }

    private string GetAnimalStatusText(AnimalStatus status)
    {
        return status switch
        {
            AnimalStatus.Healthy => "سليم",
            AnimalStatus.Sick => "مريض",
            AnimalStatus.UnderTreatment => "تحت العلاج",
            AnimalStatus.Dead => "نافق",
            _ => "غير محدد"
        };
    }

    private void ShowDeleteConfirmation(BreederDto breeder)
    {
        breederToDelete = breeder;
        showDeleteDialog = true;
    }

    private void CloseDeleteDialog()
    {
        showDeleteDialog = false;
        breederToDelete = null;
    }

    private async Task ConfirmDelete()
    {
        if (breederToDelete == null) return;

        isDeletingBreeder = true;
        try
        {
            await BreederService.DeleteBreederAsync(breederToDelete.Id);
            await LoadBreeders();
            Snackbar.Add("تم حذف المربي بنجاح", Severity.Success);
            CloseDeleteDialog();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في حذف المربي: {ex.Message}", Severity.Error);
        }
        finally
        {
            isDeletingBreeder = false;
        }
    }

    private async Task DeleteBreeder(int breederId)
    {
        try
        {
            await BreederService.DeleteBreederAsync(breederId);
            await LoadBreeders();
            Snackbar.Add("تم حذف المربي بنجاح", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في حذف المربي: {ex.Message}", Severity.Error);
        }
    }

    // دوال مساعدة للواجهة
    private string GetAnimalTypeName(int animalTypeId)
    {
        return animalTypes.FirstOrDefault(t => t.Id == animalTypeId)?.Name ?? "غير محدد";
    }

    private string GetAnimalTypeIcon(int animalTypeId)
    {
        var typeName = GetAnimalTypeName(animalTypeId).ToLower();
        return typeName switch
        {
            "جمل" => Icons.Material.Filled.Pets,
            "ماعز" => Icons.Material.Filled.Pets,
            "غنم" => Icons.Material.Filled.Pets,
            "بقر" => Icons.Material.Filled.Pets,
            "دجاج" => Icons.Material.Filled.Pets,
            _ => Icons.Material.Filled.Pets
        };
    }

    private string GetCategoryName(AnimalCategory category)
    {
        return category switch
        {
            AnimalCategory.Free => "مجاني",
            AnimalCategory.Economic => "اقتصادي",
            AnimalCategory.NonEconomic => "غير اقتصادي",
            _ => "غير محدد"
        };
    }

    private List<(AnimalCategory Value, string Name, string Icon, string Color)> GetAvailableCategories()
    {
        return new List<(AnimalCategory, string, string, string)>
        {
            (AnimalCategory.Free, "مجاني", Icons.Material.Filled.VolunteerActivism, "#4CAF50"),
            (AnimalCategory.Economic, "اقتصادي", Icons.Material.Filled.TrendingUp, "#2196F3"),
            (AnimalCategory.NonEconomic, "غير اقتصادي", Icons.Material.Filled.TrendingDown, "#FF9800")
        };
    }

    private void OnAnimalTypeChanged()
    {
        // يمكن إضافة منطق خاص حسب نوع الحيوان
        StateHasChanged();
    }
}
