@page "/animals"
@inject IAnimalService AnimalService
@inject IBranchService BranchService
@inject IBreederService BreederService
@inject IAnimalTypeService AnimalTypeService
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<PageTitle>إدارة الحيوانات - نظام بيطره</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge">
    <!-- العنوان وشريط البحث -->
    <MudPaper Class="pa-4 mb-4" Elevation="2">
        <MudGrid AlignItems="Center">
            <MudItem xs="12" md="6">
                <MudText Typo="Typo.h4" Color="Color.Primary">
                    <MudIcon Icon="@Icons.Material.Filled.Pets" Class="ml-2" />
                    إدارة الحيوانات
                </MudText>
            </MudItem>
            <MudItem xs="12" md="6" Class="text-left">
                <MudButton Variant="Variant.Filled" 
                          Color="Color.Primary" 
                          StartIcon="@Icons.Material.Filled.Add"
                          Href="/animals/add">
                    إضافة حيوان جديد
                </MudButton>
            </MudItem>
        </MudGrid>
        
        <!-- شريط البحث والفلاتر -->
        <MudGrid Class="mt-4">
            <MudItem xs="12" md="3">
                <MudTextField @bind-Value="searchText" 
                             Label="البحث" 
                             Placeholder="البحث بالاسم أو نوع الحيوان"
                             Adornment="Adornment.Start" 
                             AdornmentIcon="@Icons.Material.Filled.Search"
                             OnKeyUp="@(async (e) => { if (e.Key == "Enter") await SearchAnimals(); })" />
            </MudItem>
            <MudItem xs="12" md="2">
                <MudSelect @bind-Value="selectedBranchId" Label="الفرع" Clearable="true">
                    @foreach (var branch in branches)
                    {
                        <MudSelectItem Value="@branch.Id">@branch.Name</MudSelectItem>
                    }
                </MudSelect>
            </MudItem>
            <MudItem xs="12" md="2">
                <MudSelect @bind-Value="selectedAnimalTypeId" Label="نوع الحيوان" Clearable="true">
                    @foreach (var animalType in animalTypes)
                    {
                        <MudSelectItem Value="@animalType.Id">@animalType.Name</MudSelectItem>
                    }
                </MudSelect>
            </MudItem>
            <MudItem xs="12" md="2">
                <MudSelect @bind-Value="selectedCategory" Label="التصنيف" Clearable="true">
                    <MudSelectItem Value="@AnimalCategory.Free">مجاني</MudSelectItem>
                    <MudSelectItem Value="@AnimalCategory.Economic">اقتصادي</MudSelectItem>
                    <MudSelectItem Value="@AnimalCategory.NonEconomic">غير اقتصادي</MudSelectItem>
                </MudSelect>
            </MudItem>
            <MudItem xs="12" md="2">
                <MudSelect @bind-Value="selectedStatus" Label="الحالة" Clearable="true">
                    <MudSelectItem Value="@AnimalStatus.Healthy">سليم</MudSelectItem>
                    <MudSelectItem Value="@AnimalStatus.Sick">مريض</MudSelectItem>
                    <MudSelectItem Value="@AnimalStatus.UnderTreatment">تحت العلاج</MudSelectItem>
                    <MudSelectItem Value="@AnimalStatus.Recovered">متعافي</MudSelectItem>
                    <MudSelectItem Value="@AnimalStatus.Dead">نافق</MudSelectItem>
                </MudSelect>
            </MudItem>
            <MudItem xs="12" md="1">
                <MudButton Variant="Variant.Filled" 
                          Color="Color.Secondary" 
                          FullWidth="true"
                          OnClick="SearchAnimals">
                    بحث
                </MudButton>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- جدول الحيوانات -->
    <MudPaper Class="pa-4" Elevation="2">
        @if (isLoading)
        {
            <div class="text-center pa-8">
                <MudProgressCircular Indeterminate="true" />
                <MudText Class="mt-4">جاري تحميل البيانات...</MudText>
            </div>
        }
        else if (!animals.Any())
        {
            <div class="text-center pa-8">
                <MudIcon Icon="@Icons.Material.Filled.SearchOff" Size="Size.Large" Color="Color.Secondary" />
                <MudText Typo="Typo.h6" Class="mt-4">لا توجد نتائج</MudText>
                <MudText>لم يتم العثور على حيوانات مطابقة لمعايير البحث</MudText>
            </div>
        }
        else
        {
            <MudTable Items="@animals" Hover="true" Striped="true" Dense="true">
                <HeaderContent>
                    <MudTh>الاسم</MudTh>
                    <MudTh>النوع</MudTh>
                    <MudTh>العدد</MudTh>
                    <MudTh>التصنيف</MudTh>
                    <MudTh>الحالة</MudTh>
                    <MudTh>المربي</MudTh>
                    <MudTh>الفرع</MudTh>
                    <MudTh>الإجراءات</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd DataLabel="الاسم">
                        <MudText Typo="Typo.body2" Style="font-weight: 500;">-</MudText>
                    </MudTd>
                    <MudTd DataLabel="النوع">-</MudTd>
                    <MudTd DataLabel="العدد">
                        <MudChip T="string" Size="Size.Small" Color="Color.Info">0</MudChip>
                    </MudTd>
                    <MudTd DataLabel="التصنيف">
                        <MudChip T="string" Size="Size.Small" Color="Color.Default">
                            -
                        </MudChip>
                    </MudTd>
                    <MudTd DataLabel="الحالة">
                        <MudChip T="string" Size="Size.Small" Color="Color.Default">
                            -
                        </MudChip>
                    </MudTd>
                    <MudTd DataLabel="المربي">-</MudTd>
                    <MudTd DataLabel="الفرع">-</MudTd>
                    <MudTd DataLabel="الإجراءات">
                        <MudButtonGroup Variant="Variant.Text" Size="Size.Small">
                            <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                          Color="Color.Primary"
                                          Size="Size.Small"
                                          Href="/animals/edit/1" />
                            <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                          Color="Color.Error"
                                          Size="Size.Small"
                                          OnClick="@(() => DeleteAnimal(1))" />
                        </MudButtonGroup>
                    </MudTd>
                </RowTemplate>
            </MudTable>

            <!-- التصفح -->
            <div class="d-flex justify-center mt-4">
                <MudPagination Count="@totalPages" 
                              Selected="@currentPage" 
                              SelectedChanged="@OnPageChanged" 
                              ShowFirstButton="true" 
                              ShowLastButton="true" />
            </div>
        }
    </MudPaper>
</MudContainer>

@code {
    private List<object> animals = new();
    private List<Branch> branches = new();
    private List<AnimalType> animalTypes = new();
    private bool isLoading = true;
    private string searchText = "";
    private int? selectedBranchId = null;
    private int? selectedAnimalTypeId = null;
    private AnimalCategory? selectedCategory = null;
    private AnimalStatus? selectedStatus = null;
    private int currentPage = 1;
    private int pageSize = 10;
    private int totalPages = 1;

    protected override async Task OnInitializedAsync()
    {
        await LoadBranches();
        await LoadAnimalTypes();
        await LoadAnimals();
    }

    private async Task LoadBranches()
    {
        try
        {
            branches = (await BranchService.GetBranchesAsync(isActive: true)).ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل الفروع: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadAnimalTypes()
    {
        try
        {
            animalTypes = (await AnimalTypeService.GetAnimalTypesAsync(isActive: true)).ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل أنواع الحيوانات: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadAnimals()
    {
        isLoading = true;
        try
        {
            var result = await AnimalService.GetAnimalsAsync(
                search: string.IsNullOrEmpty(searchText) ? null : searchText,
                branchId: selectedBranchId,
                animalTypeId: selectedAnimalTypeId,
                page: currentPage,
                pageSize: pageSize
            );
            
            animals = result.ToList();
            totalPages = Math.Max(1, (int)Math.Ceiling(animals.Count / (double)pageSize));
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل الحيوانات: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task SearchAnimals()
    {
        currentPage = 1;
        await LoadAnimals();
    }

    private async Task OnPageChanged(int page)
    {
        currentPage = page;
        await LoadAnimals();
    }

    private async Task DeleteAnimal(int animalId)
    {
        try
        {
            await AnimalService.DeleteAnimalAsync(animalId);
            await LoadAnimals();
            Snackbar.Add("تم حذف الحيوان بنجاح", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في حذف الحيوان: {ex.Message}", Severity.Error);
        }
    }

    private Color GetCategoryColor(AnimalCategory category)
    {
        return category switch
        {
            AnimalCategory.Free => Color.Success,
            AnimalCategory.Economic => Color.Primary,
            AnimalCategory.NonEconomic => Color.Secondary,
            _ => Color.Default
        };
    }

    private string GetCategoryText(AnimalCategory category)
    {
        return category switch
        {
            AnimalCategory.Free => "مجاني",
            AnimalCategory.Economic => "اقتصادي",
            AnimalCategory.NonEconomic => "غير اقتصادي",
            _ => "غير محدد"
        };
    }

    private Color GetStatusColor(AnimalStatus status)
    {
        return status switch
        {
            AnimalStatus.Healthy => Color.Success,
            AnimalStatus.Sick => Color.Error,
            AnimalStatus.UnderTreatment => Color.Warning,
            AnimalStatus.Recovered => Color.Info,
            AnimalStatus.Dead => Color.Dark,
            _ => Color.Default
        };
    }

    private string GetStatusText(AnimalStatus status)
    {
        return status switch
        {
            AnimalStatus.Healthy => "سليم",
            AnimalStatus.Sick => "مريض",
            AnimalStatus.UnderTreatment => "تحت العلاج",
            AnimalStatus.Recovered => "متعافي",
            AnimalStatus.Dead => "نافق",
            _ => "غير محدد"
        };
    }
}
