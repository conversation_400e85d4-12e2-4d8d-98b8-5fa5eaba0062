@page "/reports"
@inject IReportService ReportService
@inject IBranchService BranchService
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<PageTitle>التقارير - نظام بيطره</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge">
    <!-- العنوان -->
    <MudPaper Class="pa-4 mb-4" Elevation="2">
        <MudText Typo="Typo.h4" Color="Color.Primary">
            <MudIcon Icon="@Icons.Material.Filled.Assessment" Class="ml-2" />
            التقارير والإحصائيات
        </MudText>
        <MudText Typo="Typo.body1" Class="mt-2">
            عرض شامل للتقارير والإحصائيات الخاصة بالنظام
        </MudText>
    </MudPaper>

    <!-- فلتر الفرع -->
    <MudPaper Class="pa-4 mb-4" Elevation="2">
        <MudGrid AlignItems="Center">
            <MudItem xs="12" md="4">
                <MudSelect @bind-Value="selectedBranchId" Label="اختيار الفرع" Clearable="true" OnSelectionChanged="OnBranchChanged">
                    <MudSelectItem Value="0">جميع الفروع</MudSelectItem>
                    @foreach (var branch in branches)
                    {
                        <MudSelectItem Value="@branch.Id">@branch.Name</MudSelectItem>
                    }
                </MudSelect>
            </MudItem>
            <MudItem xs="12" md="3">
                <MudDatePicker @bind-Date="fromDate" Label="من تاريخ" />
            </MudItem>
            <MudItem xs="12" md="3">
                <MudDatePicker @bind-Date="toDate" Label="إلى تاريخ" />
            </MudItem>
            <MudItem xs="12" md="2">
                <MudButton Variant="Variant.Filled" 
                          Color="Color.Primary" 
                          FullWidth="true"
                          OnClick="RefreshReports">
                    تحديث التقارير
                </MudButton>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- الإحصائيات السريعة -->
    @if (dashboardStats != null)
    {
        <MudPaper Class="pa-4 mb-4" Elevation="2">
            <MudText Typo="Typo.h5" Class="mb-4">الإحصائيات العامة</MudText>
            <MudGrid>
                <MudItem xs="12" sm="6" md="3">
                    <MudPaper Class="pa-4 text-center" Elevation="1" Style="background: linear-gradient(45deg, #2196F3 30%, #21CBF3 90%);">
                        <MudIcon Icon="@Icons.Material.Filled.People" Size="Size.Large" Style="color: white;" Class="mb-2" />
                        <MudText Typo="Typo.h4" Style="color: white;">0</MudText>
                        <MudText Typo="Typo.body1" Style="color: white;">إجمالي المربين</MudText>
                    </MudPaper>
                </MudItem>
                
                <MudItem xs="12" sm="6" md="3">
                    <MudPaper Class="pa-4 text-center" Elevation="1" Style="background: linear-gradient(45deg, #FF9800 30%, #FFB74D 90%);">
                        <MudIcon Icon="@Icons.Material.Filled.Pets" Size="Size.Large" Style="color: white;" Class="mb-2" />
                        <MudText Typo="Typo.h4" Style="color: white;">0</MudText>
                        <MudText Typo="Typo.body1" Style="color: white;">إجمالي الحيوانات</MudText>
                    </MudPaper>
                </MudItem>

                <MudItem xs="12" sm="6" md="3">
                    <MudPaper Class="pa-4 text-center" Elevation="1" Style="background: linear-gradient(45deg, #4CAF50 30%, #81C784 90%);">
                        <MudIcon Icon="@Icons.Material.Filled.MedicalServices" Size="Size.Large" Style="color: white;" Class="mb-2" />
                        <MudText Typo="Typo.h4" Style="color: white;">0</MudText>
                        <MudText Typo="Typo.body1" Style="color: white;">إجمالي العلاجات</MudText>
                    </MudPaper>
                </MudItem>

                <MudItem xs="12" sm="6" md="3">
                    <MudPaper Class="pa-4 text-center" Elevation="1" Style="background: linear-gradient(45deg, #9C27B0 30%, #BA68C8 90%);">
                        <MudIcon Icon="@Icons.Material.Filled.AttachMoney" Size="Size.Large" Style="color: white;" Class="mb-2" />
                        <MudText Typo="Typo.h4" Style="color: white;">0.00</MudText>
                        <MudText Typo="Typo.body1" Style="color: white;">إجمالي الإيرادات (ر.ع)</MudText>
                    </MudPaper>
                </MudItem>
            </MudGrid>
        </MudPaper>
    }

    <!-- بطاقات التقارير -->
    <MudGrid>
        <!-- تقرير المربين -->
        <MudItem xs="12" md="6" lg="3">
            <MudCard Class="pa-4 h-100" Elevation="3">
                <MudCardContent Class="text-center">
                    <MudIcon Icon="@Icons.Material.Filled.PeopleAlt" 
                            Size="Size.Large" 
                            Color="Color.Primary" 
                            Class="mb-4" />
                    <MudText Typo="Typo.h6" Class="mb-2">تقرير المربين</MudText>
                    <MudText Typo="Typo.body2" Class="mb-4">
                        تقرير شامل عن المربين وأنشطتهم
                    </MudText>
                </MudCardContent>
                <MudCardActions Class="justify-center">
                    <MudButton Variant="Variant.Filled" 
                              Color="Color.Primary" 
                              FullWidth="true"
                              Href="/reports/breeders">
                        عرض التقرير
                    </MudButton>
                </MudCardActions>
            </MudCard>
        </MudItem>

        <!-- تقرير الحيوانات -->
        <MudItem xs="12" md="6" lg="3">
            <MudCard Class="pa-4 h-100" Elevation="3">
                <MudCardContent Class="text-center">
                    <MudIcon Icon="@Icons.Material.Filled.Pets" 
                            Size="Size.Large" 
                            Color="Color.Secondary" 
                            Class="mb-4" />
                    <MudText Typo="Typo.h6" Class="mb-2">تقرير الحيوانات</MudText>
                    <MudText Typo="Typo.body2" Class="mb-4">
                        إحصائيات الحيوانات وحالتها الصحية
                    </MudText>
                </MudCardContent>
                <MudCardActions Class="justify-center">
                    <MudButton Variant="Variant.Filled" 
                              Color="Color.Secondary" 
                              FullWidth="true"
                              Href="/reports/animals">
                        عرض التقرير
                    </MudButton>
                </MudCardActions>
            </MudCard>
        </MudItem>

        <!-- تقرير العلاجات -->
        <MudItem xs="12" md="6" lg="3">
            <MudCard Class="pa-4 h-100" Elevation="3">
                <MudCardContent Class="text-center">
                    <MudIcon Icon="@Icons.Material.Filled.MedicalServices" 
                            Size="Size.Large" 
                            Color="Color.Success" 
                            Class="mb-4" />
                    <MudText Typo="Typo.h6" Class="mb-2">تقرير العلاجات</MudText>
                    <MudText Typo="Typo.body2" Class="mb-4">
                        تفاصيل العلاجات والخدمات البيطرية
                    </MudText>
                </MudCardContent>
                <MudCardActions Class="justify-center">
                    <MudButton Variant="Variant.Filled" 
                              Color="Color.Success" 
                              FullWidth="true"
                              Href="/reports/treatments">
                        عرض التقرير
                    </MudButton>
                </MudCardActions>
            </MudCard>
        </MudItem>

        <!-- التقرير المالي -->
        <MudItem xs="12" md="6" lg="3">
            <MudCard Class="pa-4 h-100" Elevation="3">
                <MudCardContent Class="text-center">
                    <MudIcon Icon="@Icons.Material.Filled.AttachMoney" 
                            Size="Size.Large" 
                            Color="Color.Warning" 
                            Class="mb-4" />
                    <MudText Typo="Typo.h6" Class="mb-2">التقرير المالي</MudText>
                    <MudText Typo="Typo.body2" Class="mb-4">
                        الإيرادات والمصروفات والأرباح
                    </MudText>
                </MudCardContent>
                <MudCardActions Class="justify-center">
                    <MudButton Variant="Variant.Filled" 
                              Color="Color.Warning" 
                              FullWidth="true"
                              Href="/reports/financial">
                        عرض التقرير
                    </MudButton>
                </MudCardActions>
            </MudCard>
        </MudItem>
    </MudGrid>

    <!-- إحصائيات هذا الشهر -->
    @if (dashboardStats != null)
    {
        <MudPaper Class="pa-4 mt-4" Elevation="2">
            <MudText Typo="Typo.h5" Class="mb-4">إحصائيات هذا الشهر</MudText>
            <MudGrid>
                <MudItem xs="12" md="6">
                    <MudPaper Class="pa-4 text-center" Elevation="1">
                        <MudIcon Icon="@Icons.Material.Filled.MedicalServices" Size="Size.Large" Color="Color.Success" Class="mb-2" />
                        <MudText Typo="Typo.h4" Color="Color.Success">0</MudText>
                        <MudText Typo="Typo.body1">العلاجات هذا الشهر</MudText>
                    </MudPaper>
                </MudItem>

                <MudItem xs="12" md="6">
                    <MudPaper Class="pa-4 text-center" Elevation="1">
                        <MudIcon Icon="@Icons.Material.Filled.Science" Size="Size.Large" Color="Color.Info" Class="mb-2" />
                        <MudText Typo="Typo.h4" Color="Color.Info">0</MudText>
                        <MudText Typo="Typo.body1">الفحوصات هذا الشهر</MudText>
                    </MudPaper>
                </MudItem>
            </MudGrid>
        </MudPaper>
    }
</MudContainer>

@code {
    private List<Branch> branches = new();
    private int selectedBranchId = 0;
    private DateTime? fromDate = DateTime.UtcNow.AddMonths(-1);
    private DateTime? toDate = DateTime.UtcNow;
    private object? dashboardStats = null;

    protected override async Task OnInitializedAsync()
    {
        await LoadBranches();
        await LoadDashboardStats();
    }

    private async Task LoadBranches()
    {
        try
        {
            branches = (await BranchService.GetBranchesAsync(isActive: true)).ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل الفروع: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadDashboardStats()
    {
        try
        {
            var branchId = selectedBranchId == 0 ? (int?)null : selectedBranchId;
            dashboardStats = await ReportService.GetDashboardStatsAsync(branchId);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل الإحصائيات: {ex.Message}", Severity.Error);
        }
    }

    private async Task OnBranchChanged()
    {
        await LoadDashboardStats();
    }

    private async Task RefreshReports()
    {
        await LoadDashboardStats();
        Snackbar.Add("تم تحديث التقارير بنجاح", Severity.Success);
    }
}
