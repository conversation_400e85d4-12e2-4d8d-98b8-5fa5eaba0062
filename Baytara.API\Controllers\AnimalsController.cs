using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Baytara.API.Data;
using Baytara.Shared.Models;

namespace Baytara.API.Controllers;

public class AnimalDto
{
    public int Id { get; set; }
    public string? Name { get; set; }
    public int Count { get; set; }
    public AnimalCategory Category { get; set; }
    public AnimalStatus Status { get; set; }
    public string? Notes { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? LastUpdated { get; set; }
    
    public int AnimalTypeId { get; set; }
    public string AnimalTypeName { get; set; } = string.Empty;
    
    public int BreederId { get; set; }
    public string BreederName { get; set; } = string.Empty;
    public string BreederPhone { get; set; } = string.Empty;
    
    public int BranchId { get; set; }
    public string BranchName { get; set; } = string.Empty;
    
    public int TreatmentsCount { get; set; }
    public int LabTestsCount { get; set; }
}

public class CreateAnimalDto
{
    public string? Name { get; set; }
    public int Count { get; set; } = 1;
    public AnimalCategory Category { get; set; }
    public AnimalStatus Status { get; set; } = AnimalStatus.Healthy;
    public string? Notes { get; set; }
    public int AnimalTypeId { get; set; }
    public int BreederId { get; set; }
    public int BranchId { get; set; }
}

[ApiController]
[Route("api/[controller]")]
public class AnimalsController : ControllerBase
{
    private readonly BaytaraDbContext _context;

    public AnimalsController(BaytaraDbContext context)
    {
        _context = context;
    }

    // GET: api/Animals
    [HttpGet]
    public async Task<ActionResult<IEnumerable<AnimalDto>>> GetAnimals(
        [FromQuery] string? search = null,
        [FromQuery] int? branchId = null,
        [FromQuery] int? breederId = null,
        [FromQuery] int? animalTypeId = null,
        [FromQuery] AnimalCategory? category = null,
        [FromQuery] AnimalStatus? status = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10)
    {
        var query = _context.Animals
            .Include(a => a.AnimalType)
            .Include(a => a.Breeder)
            .Include(a => a.Branch)
            .Include(a => a.Treatments)
            .Include(a => a.LabTests)
            .AsQueryable();

        // تطبيق الفلاتر
        if (!string.IsNullOrEmpty(search))
        {
            query = query.Where(a => (a.Name != null && a.Name.Contains(search)) ||
                                   a.AnimalType.Name.Contains(search) ||
                                   a.Breeder.Name.Contains(search));
        }

        if (branchId.HasValue)
        {
            query = query.Where(a => a.BranchId == branchId.Value);
        }

        if (breederId.HasValue)
        {
            query = query.Where(a => a.BreederId == breederId.Value);
        }

        if (animalTypeId.HasValue)
        {
            query = query.Where(a => a.AnimalTypeId == animalTypeId.Value);
        }

        if (category.HasValue)
        {
            query = query.Where(a => a.Category == category.Value);
        }

        if (status.HasValue)
        {
            query = query.Where(a => a.Status == status.Value);
        }

        // ترتيب النتائج
        query = query.OrderByDescending(a => a.CreatedAt);

        // تطبيق التصفح
        var totalCount = await query.CountAsync();
        var animals = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .Select(a => new AnimalDto
            {
                Id = a.Id,
                Name = a.Name,
                Count = a.Count,
                Category = a.Category,
                Status = a.Status,
                Notes = a.Notes,
                CreatedAt = a.CreatedAt,
                LastUpdated = a.LastUpdated,
                AnimalTypeId = a.AnimalTypeId,
                AnimalTypeName = a.AnimalType.Name,
                BreederId = a.BreederId,
                BreederName = a.Breeder.Name,
                BreederPhone = a.Breeder.Phone,
                BranchId = a.BranchId,
                BranchName = a.Branch.Name,
                TreatmentsCount = a.Treatments.Count,
                LabTestsCount = a.LabTests.Count
            })
            .ToListAsync();

        Response.Headers["X-Total-Count"] = totalCount.ToString();
        Response.Headers["X-Page"] = page.ToString();
        Response.Headers["X-Page-Size"] = pageSize.ToString();

        return Ok(animals);
    }

    // GET: api/Animals/5
    [HttpGet("{id}")]
    public async Task<ActionResult<AnimalDto>> GetAnimal(int id)
    {
        var animal = await _context.Animals
            .Include(a => a.AnimalType)
            .Include(a => a.Breeder)
            .Include(a => a.Branch)
            .Include(a => a.Treatments)
            .Include(a => a.LabTests)
            .FirstOrDefaultAsync(a => a.Id == id);

        if (animal == null)
        {
            return NotFound();
        }

        var animalDto = new AnimalDto
        {
            Id = animal.Id,
            Name = animal.Name,
            Count = animal.Count,
            Category = animal.Category,
            Status = animal.Status,
            Notes = animal.Notes,
            CreatedAt = animal.CreatedAt,
            LastUpdated = animal.LastUpdated,
            AnimalTypeId = animal.AnimalTypeId,
            AnimalTypeName = animal.AnimalType.Name,
            BreederId = animal.BreederId,
            BreederName = animal.Breeder.Name,
            BreederPhone = animal.Breeder.Phone,
            BranchId = animal.BranchId,
            BranchName = animal.Branch.Name,
            TreatmentsCount = animal.Treatments.Count,
            LabTestsCount = animal.LabTests.Count
        };

        return Ok(animalDto);
    }

    // POST: api/Animals
    [HttpPost]
    public async Task<ActionResult<AnimalDto>> CreateAnimal(CreateAnimalDto createAnimalDto)
    {
        // التحقق من وجود نوع الحيوان
        var animalType = await _context.AnimalTypes.FindAsync(createAnimalDto.AnimalTypeId);
        if (animalType == null)
        {
            return BadRequest("نوع الحيوان المحدد غير موجود");
        }

        // التحقق من وجود المربي
        var breeder = await _context.Breeders.FindAsync(createAnimalDto.BreederId);
        if (breeder == null)
        {
            return BadRequest("المربي المحدد غير موجود");
        }

        // التحقق من وجود الفرع
        var branch = await _context.Branches.FindAsync(createAnimalDto.BranchId);
        if (branch == null)
        {
            return BadRequest("الفرع المحدد غير موجود");
        }

        var animal = new Animal
        {
            Name = createAnimalDto.Name,
            Count = createAnimalDto.Count,
            Category = createAnimalDto.Category,
            Status = createAnimalDto.Status,
            Notes = createAnimalDto.Notes,
            AnimalTypeId = createAnimalDto.AnimalTypeId,
            BreederId = createAnimalDto.BreederId,
            BranchId = createAnimalDto.BranchId,
            CreatedAt = DateTime.UtcNow
        };

        _context.Animals.Add(animal);
        await _context.SaveChangesAsync();

        // إعادة تحميل البيانات مع العلاقات
        await _context.Entry(animal)
            .Reference(a => a.AnimalType)
            .LoadAsync();
        await _context.Entry(animal)
            .Reference(a => a.Breeder)
            .LoadAsync();
        await _context.Entry(animal)
            .Reference(a => a.Branch)
            .LoadAsync();

        var animalDto = new AnimalDto
        {
            Id = animal.Id,
            Name = animal.Name,
            Count = animal.Count,
            Category = animal.Category,
            Status = animal.Status,
            Notes = animal.Notes,
            CreatedAt = animal.CreatedAt,
            LastUpdated = animal.LastUpdated,
            AnimalTypeId = animal.AnimalTypeId,
            AnimalTypeName = animal.AnimalType.Name,
            BreederId = animal.BreederId,
            BreederName = animal.Breeder.Name,
            BreederPhone = animal.Breeder.Phone,
            BranchId = animal.BranchId,
            BranchName = animal.Branch.Name,
            TreatmentsCount = 0,
            LabTestsCount = 0
        };

        return CreatedAtAction(nameof(GetAnimal), new { id = animal.Id }, animalDto);
    }

    // PUT: api/Animals/5
    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateAnimal(int id, CreateAnimalDto updateAnimalDto)
    {
        var animal = await _context.Animals.FindAsync(id);
        if (animal == null)
        {
            return NotFound();
        }

        // التحقق من وجود نوع الحيوان
        var animalType = await _context.AnimalTypes.FindAsync(updateAnimalDto.AnimalTypeId);
        if (animalType == null)
        {
            return BadRequest("نوع الحيوان المحدد غير موجود");
        }

        // التحقق من وجود المربي
        var breeder = await _context.Breeders.FindAsync(updateAnimalDto.BreederId);
        if (breeder == null)
        {
            return BadRequest("المربي المحدد غير موجود");
        }

        // التحقق من وجود الفرع
        var branch = await _context.Branches.FindAsync(updateAnimalDto.BranchId);
        if (branch == null)
        {
            return BadRequest("الفرع المحدد غير موجود");
        }

        animal.Name = updateAnimalDto.Name;
        animal.Count = updateAnimalDto.Count;
        animal.Category = updateAnimalDto.Category;
        animal.Status = updateAnimalDto.Status;
        animal.Notes = updateAnimalDto.Notes;
        animal.AnimalTypeId = updateAnimalDto.AnimalTypeId;
        animal.BreederId = updateAnimalDto.BreederId;
        animal.BranchId = updateAnimalDto.BranchId;
        animal.LastUpdated = DateTime.UtcNow;

        try
        {
            await _context.SaveChangesAsync();
        }
        catch (DbUpdateConcurrencyException)
        {
            if (!AnimalExists(id))
            {
                return NotFound();
            }
            throw;
        }

        return NoContent();
    }

    // DELETE: api/Animals/5
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteAnimal(int id)
    {
        var animal = await _context.Animals
            .Include(a => a.Treatments)
            .Include(a => a.LabTests)
            .FirstOrDefaultAsync(a => a.Id == id);

        if (animal == null)
        {
            return NotFound();
        }

        // التحقق من وجود علاجات أو فحوصات مرتبطة
        if (animal.Treatments.Any() || animal.LabTests.Any())
        {
            return BadRequest("لا يمكن حذف الحيوان لوجود علاجات أو فحوصات مرتبطة به");
        }

        _context.Animals.Remove(animal);
        await _context.SaveChangesAsync();

        return NoContent();
    }

    private bool AnimalExists(int id)
    {
        return _context.Animals.Any(e => e.Id == id);
    }
}
