using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Baytara.API.Data;
using Baytara.Shared.Models;

namespace Baytara.API.Controllers;

public class DashboardStatsDto
{
    public int TotalBreeders { get; set; }
    public int TotalAnimals { get; set; }
    public int TotalTreatments { get; set; }
    public int TotalLabTests { get; set; }
    public int ActiveBranches { get; set; }
    public decimal TotalRevenue { get; set; }
    public int TreatmentsThisMonth { get; set; }
    public int LabTestsThisMonth { get; set; }
}

public class BreederReportDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string? Region { get; set; }
    public string BranchName { get; set; } = string.Empty;
    public int AnimalsCount { get; set; }
    public int TreatmentsCount { get; set; }
    public decimal TotalSpent { get; set; }
    public DateTime LastVisit { get; set; }
}

public class AnimalReportDto
{
    public int Id { get; set; }
    public string? Name { get; set; }
    public string AnimalTypeName { get; set; } = string.Empty;
    public int Count { get; set; }
    public AnimalCategory Category { get; set; }
    public AnimalStatus Status { get; set; }
    public string BreederName { get; set; } = string.Empty;
    public string BranchName { get; set; } = string.Empty;
    public int TreatmentsCount { get; set; }
    public int LabTestsCount { get; set; }
    public decimal TotalTreatmentCost { get; set; }
}

public class TreatmentReportDto
{
    public int Id { get; set; }
    public DateTime TreatmentDate { get; set; }
    public string AnimalInfo { get; set; } = string.Empty;
    public string BreederName { get; set; } = string.Empty;
    public string? DiseaseName { get; set; }
    public TreatmentStatus Status { get; set; }
    public decimal Cost { get; set; }
    public string? VeterinarianName { get; set; }
    public string BranchName { get; set; } = string.Empty;
    public int MedicinesCount { get; set; }
}

public class FinancialReportDto
{
    public DateTime Date { get; set; }
    public decimal TreatmentRevenue { get; set; }
    public decimal LabTestRevenue { get; set; }
    public decimal TotalRevenue { get; set; }
    public int TreatmentsCount { get; set; }
    public int LabTestsCount { get; set; }
    public string BranchName { get; set; } = string.Empty;
}

[ApiController]
[Route("api/[controller]")]
public class ReportsController : ControllerBase
{
    private readonly BaytaraDbContext _context;

    public ReportsController(BaytaraDbContext context)
    {
        _context = context;
    }

    // GET: api/Reports/Dashboard
    [HttpGet("Dashboard")]
    public async Task<ActionResult<DashboardStatsDto>> GetDashboardStats([FromQuery] int? branchId = null)
    {
        var breedersQuery = _context.Breeders.AsQueryable();
        var animalsQuery = _context.Animals.AsQueryable();
        var treatmentsQuery = _context.Treatments.AsQueryable();
        var labTestsQuery = _context.LabTests.AsQueryable();

        if (branchId.HasValue)
        {
            breedersQuery = breedersQuery.Where(b => b.BranchId == branchId.Value);
            animalsQuery = animalsQuery.Where(a => a.BranchId == branchId.Value);
            treatmentsQuery = treatmentsQuery.Where(t => t.BranchId == branchId.Value);
            labTestsQuery = labTestsQuery.Where(lt => lt.BranchId == branchId.Value);
        }

        var currentMonth = DateTime.UtcNow.Month;
        var currentYear = DateTime.UtcNow.Year;

        var stats = new DashboardStatsDto
        {
            TotalBreeders = await breedersQuery.CountAsync(b => b.IsActive),
            TotalAnimals = await animalsQuery.SumAsync(a => a.Count),
            TotalTreatments = await treatmentsQuery.CountAsync(),
            TotalLabTests = await labTestsQuery.CountAsync(),
            ActiveBranches = await _context.Branches.CountAsync(b => b.IsActive),
            TotalRevenue = await treatmentsQuery.SumAsync(t => t.Cost) + await labTestsQuery.SumAsync(lt => lt.Cost),
            TreatmentsThisMonth = await treatmentsQuery.CountAsync(t => t.TreatmentDate.Month == currentMonth && t.TreatmentDate.Year == currentYear),
            LabTestsThisMonth = await labTestsQuery.CountAsync(lt => lt.RequestDate.Month == currentMonth && lt.RequestDate.Year == currentYear)
        };

        return Ok(stats);
    }

    // GET: api/Reports/Breeders
    [HttpGet("Breeders")]
    public async Task<ActionResult<IEnumerable<BreederReportDto>>> GetBreedersReport(
        [FromQuery] int? branchId = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        var query = _context.Breeders
            .Include(b => b.Branch)
            .Include(b => b.Animals)
                .ThenInclude(a => a.Treatments)
            .AsQueryable();

        if (branchId.HasValue)
        {
            query = query.Where(b => b.BranchId == branchId.Value);
        }

        var breeders = await query
            .Where(b => b.IsActive)
            .Select(b => new BreederReportDto
            {
                Id = b.Id,
                Name = b.Name,
                Phone = b.Phone,
                Region = b.Region,
                BranchName = b.Branch.Name,
                AnimalsCount = b.Animals.Sum(a => a.Count),
                TreatmentsCount = b.Animals.SelectMany(a => a.Treatments).Count(),
                TotalSpent = b.Animals.SelectMany(a => a.Treatments).Sum(t => t.Cost),
                LastVisit = b.Animals.SelectMany(a => a.Treatments).Any() 
                    ? b.Animals.SelectMany(a => a.Treatments).Max(t => t.TreatmentDate)
                    : b.CreatedAt
            })
            .OrderByDescending(b => b.TotalSpent)
            .ToListAsync();

        return Ok(breeders);
    }

    // GET: api/Reports/Animals
    [HttpGet("Animals")]
    public async Task<ActionResult<IEnumerable<AnimalReportDto>>> GetAnimalsReport(
        [FromQuery] int? branchId = null,
        [FromQuery] AnimalCategory? category = null,
        [FromQuery] AnimalStatus? status = null)
    {
        var query = _context.Animals
            .Include(a => a.AnimalType)
            .Include(a => a.Breeder)
            .Include(a => a.Branch)
            .Include(a => a.Treatments)
            .Include(a => a.LabTests)
            .AsQueryable();

        if (branchId.HasValue)
        {
            query = query.Where(a => a.BranchId == branchId.Value);
        }

        if (category.HasValue)
        {
            query = query.Where(a => a.Category == category.Value);
        }

        if (status.HasValue)
        {
            query = query.Where(a => a.Status == status.Value);
        }

        var animals = await query
            .Select(a => new AnimalReportDto
            {
                Id = a.Id,
                Name = a.Name,
                AnimalTypeName = a.AnimalType.Name,
                Count = a.Count,
                Category = a.Category,
                Status = a.Status,
                BreederName = a.Breeder.Name,
                BranchName = a.Branch.Name,
                TreatmentsCount = a.Treatments.Count,
                LabTestsCount = a.LabTests.Count,
                TotalTreatmentCost = a.Treatments.Sum(t => t.Cost)
            })
            .OrderByDescending(a => a.TotalTreatmentCost)
            .ToListAsync();

        return Ok(animals);
    }

    // GET: api/Reports/Treatments
    [HttpGet("Treatments")]
    public async Task<ActionResult<IEnumerable<TreatmentReportDto>>> GetTreatmentsReport(
        [FromQuery] int? branchId = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] TreatmentStatus? status = null)
    {
        var query = _context.Treatments
            .Include(t => t.Animal)
                .ThenInclude(a => a.AnimalType)
            .Include(t => t.Animal)
                .ThenInclude(a => a.Breeder)
            .Include(t => t.Disease)
            .Include(t => t.Branch)
            .Include(t => t.TreatmentMedicines)
            .AsQueryable();

        if (branchId.HasValue)
        {
            query = query.Where(t => t.BranchId == branchId.Value);
        }

        if (fromDate.HasValue)
        {
            query = query.Where(t => t.TreatmentDate.Date >= fromDate.Value.Date);
        }

        if (toDate.HasValue)
        {
            query = query.Where(t => t.TreatmentDate.Date <= toDate.Value.Date);
        }

        if (status.HasValue)
        {
            query = query.Where(t => t.Status == status.Value);
        }

        var treatments = await query
            .Select(t => new TreatmentReportDto
            {
                Id = t.Id,
                TreatmentDate = t.TreatmentDate,
                AnimalInfo = (t.Animal.Name ?? t.Animal.AnimalType.Name) + " - " + t.Animal.AnimalType.Name,
                BreederName = t.Animal.Breeder.Name,
                DiseaseName = t.Disease != null ? t.Disease.Name : null,
                Status = t.Status,
                Cost = t.Cost,
                VeterinarianName = t.VeterinarianName,
                BranchName = t.Branch.Name,
                MedicinesCount = t.TreatmentMedicines.Count
            })
            .OrderByDescending(t => t.TreatmentDate)
            .ToListAsync();

        return Ok(treatments);
    }

    // GET: api/Reports/Financial
    [HttpGet("Financial")]
    public async Task<ActionResult<IEnumerable<FinancialReportDto>>> GetFinancialReport(
        [FromQuery] int? branchId = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] string groupBy = "day") // day, month, year
    {
        var treatmentsQuery = _context.Treatments.Include(t => t.Branch).AsQueryable();
        var labTestsQuery = _context.LabTests.Include(lt => lt.Branch).AsQueryable();

        if (branchId.HasValue)
        {
            treatmentsQuery = treatmentsQuery.Where(t => t.BranchId == branchId.Value);
            labTestsQuery = labTestsQuery.Where(lt => lt.BranchId == branchId.Value);
        }

        var startDate = fromDate ?? DateTime.UtcNow.AddMonths(-1);
        var endDate = toDate ?? DateTime.UtcNow;

        treatmentsQuery = treatmentsQuery.Where(t => t.TreatmentDate.Date >= startDate.Date && t.TreatmentDate.Date <= endDate.Date);
        labTestsQuery = labTestsQuery.Where(lt => lt.RequestDate.Date >= startDate.Date && lt.RequestDate.Date <= endDate.Date);

        var treatmentData = await treatmentsQuery
            .GroupBy(t => new { Date = t.TreatmentDate.Date, BranchName = t.Branch.Name })
            .Select(g => new
            {
                Date = g.Key.Date,
                BranchName = g.Key.BranchName,
                Revenue = g.Sum(t => t.Cost),
                Count = g.Count()
            })
            .ToListAsync();

        var labTestData = await labTestsQuery
            .GroupBy(lt => new { Date = lt.RequestDate.Date, BranchName = lt.Branch.Name })
            .Select(g => new
            {
                Date = g.Key.Date,
                BranchName = g.Key.BranchName,
                Revenue = g.Sum(lt => lt.Cost),
                Count = g.Count()
            })
            .ToListAsync();

        var financialReport = treatmentData
            .GroupJoin(labTestData,
                t => new { t.Date, t.BranchName },
                lt => new { lt.Date, lt.BranchName },
                (t, ltGroup) => new FinancialReportDto
                {
                    Date = t.Date,
                    BranchName = t.BranchName,
                    TreatmentRevenue = t.Revenue,
                    LabTestRevenue = ltGroup.Sum(lt => lt.Revenue),
                    TotalRevenue = t.Revenue + ltGroup.Sum(lt => lt.Revenue),
                    TreatmentsCount = t.Count,
                    LabTestsCount = ltGroup.Sum(lt => lt.Count)
                })
            .Union(labTestData
                .Where(lt => !treatmentData.Any(t => t.Date == lt.Date && t.BranchName == lt.BranchName))
                .Select(lt => new FinancialReportDto
                {
                    Date = lt.Date,
                    BranchName = lt.BranchName,
                    TreatmentRevenue = 0,
                    LabTestRevenue = lt.Revenue,
                    TotalRevenue = lt.Revenue,
                    TreatmentsCount = 0,
                    LabTestsCount = lt.Count
                }))
            .OrderByDescending(f => f.Date)
            .ToList();

        return Ok(financialReport);
    }
}
