@page "/animals/add"
@inject IAnimalService AnimalService
@inject IBranchService BranchService
@inject IBreederService BreederService
@inject IAnimalTypeService AnimalTypeService
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<PageTitle>إضافة حيوان جديد - نظام بيطارة</PageTitle>

<MudContainer MaxWidth="MaxWidth.Medium">
    <MudPaper Class="pa-6" Elevation="3">
        <MudText Typo="Typo.h4" Color="Color.Primary" Class="mb-6">
            <MudIcon Icon="@Icons.Material.Filled.Add" Class="ml-2" />
            إضافة حيوان جديد
        </MudText>

        <MudForm @ref="form" @bind-IsValid="@isFormValid">
            <MudGrid>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="animal.Name"
                                 Label="اسم الحيوان (اختياري)"
                                 MaxLength="100" />
                </MudItem>
                
                <MudItem xs="12" md="6">
                    <MudNumericField @bind-Value="animal.Count"
                                    Label="العدد"
                                    Required="true"
                                    RequiredError="العدد مطلوب"
                                    Min="1" />
                </MudItem>
                
                <MudItem xs="12" md="6">
                    <MudSelect @bind-Value="animal.AnimalTypeId"
                              Label="نوع الحيوان"
                              Required="true"
                              RequiredError="نوع الحيوان مطلوب">
                        @foreach (var animalType in animalTypes)
                        {
                            <MudSelectItem Value="@animalType.Id">@animalType.Name</MudSelectItem>
                        }
                    </MudSelect>
                </MudItem>
                
                <MudItem xs="12" md="6">
                    <MudSelect @bind-Value="animal.Category"
                              Label="التصنيف"
                              Required="true"
                              RequiredError="التصنيف مطلوب">
                        <MudSelectItem Value="@AnimalCategory.Free">مجاني</MudSelectItem>
                        <MudSelectItem Value="@AnimalCategory.Economic">اقتصادي</MudSelectItem>
                        <MudSelectItem Value="@AnimalCategory.NonEconomic">غير اقتصادي</MudSelectItem>
                    </MudSelect>
                </MudItem>
                
                <MudItem xs="12" md="6">
                    <MudSelect @bind-Value="animal.Status"
                              Label="الحالة">
                        <MudSelectItem Value="@AnimalStatus.Healthy">سليم</MudSelectItem>
                        <MudSelectItem Value="@AnimalStatus.Sick">مريض</MudSelectItem>
                        <MudSelectItem Value="@AnimalStatus.UnderTreatment">تحت العلاج</MudSelectItem>
                        <MudSelectItem Value="@AnimalStatus.Recovered">متعافي</MudSelectItem>
                        <MudSelectItem Value="@AnimalStatus.Dead">نافق</MudSelectItem>
                    </MudSelect>
                </MudItem>
                
                <MudItem xs="12" md="6">
                    <MudSelect @bind-Value="animal.BranchId"
                              Label="الفرع"
                              Required="true"
                              RequiredError="الفرع مطلوب">
                        @foreach (var branch in branches)
                        {
                            <MudSelectItem Value="@branch.Id">@branch.Name</MudSelectItem>
                        }
                    </MudSelect>
                </MudItem>
                
                <MudItem xs="12">
                    <MudSelect @bind-Value="animal.BreederId"
                              Label="المربي"
                              Required="true"
                              RequiredError="المربي مطلوب"
                              Disabled="@(animal.BranchId == 0)">
                        @foreach (var breeder in breeders)
                        {
                            <MudSelectItem Value="@breeder.Id">@breeder.Name - @breeder.Phone</MudSelectItem>
                        }
                    </MudSelect>
                </MudItem>
                
                <MudItem xs="12">
                    <MudTextField @bind-Value="animal.Notes"
                                 Label="الملاحظات"
                                 Lines="3"
                                 MaxLength="500" />
                </MudItem>
                
                <MudItem xs="12" Class="text-center mt-6">
                    <MudButton Variant="Variant.Outlined" 
                              Color="Color.Secondary" 
                              StartIcon="@Icons.Material.Filled.ArrowBack"
                              OnClick="GoBack"
                              Class="ml-4">
                        العودة
                    </MudButton>
                    
                    <MudButton Color="Color.Primary" 
                              Variant="Variant.Filled" 
                              StartIcon="@Icons.Material.Filled.Save"
                              OnClick="Submit"
                              Disabled="@(!isFormValid || isSubmitting)">
                        @if (isSubmitting)
                        {
                            <MudProgressCircular Class="ml-2" Size="Size.Small" Indeterminate="true" />
                            <span>جاري الحفظ...</span>
                        }
                        else
                        {
                            <span>حفظ</span>
                        }
                    </MudButton>
                </MudItem>
            </MudGrid>
        </MudForm>
    </MudPaper>
</MudContainer>

@code {
    private MudForm form = null!;
    private bool isFormValid;
    private bool isSubmitting;
    private CreateAnimalDto animal = new() { Status = AnimalStatus.Healthy };
    private List<Branch> branches = new();
    private List<AnimalType> animalTypes = new();
    private List<BreederDto> breeders = new();

    public class CreateAnimalDto
    {
        public string? Name { get; set; }
        public int Count { get; set; } = 1;
        public AnimalCategory Category { get; set; }
        public AnimalStatus Status { get; set; } = AnimalStatus.Healthy;
        public string? Notes { get; set; }
        public int AnimalTypeId { get; set; }
        public int BreederId { get; set; }
        public int BranchId { get; set; }
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadBranches();
        await LoadAnimalTypes();
    }

    private async Task LoadBranches()
    {
        try
        {
            branches = (await BranchService.GetBranchesAsync(isActive: true)).ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل الفروع: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadAnimalTypes()
    {
        try
        {
            animalTypes = (await AnimalTypeService.GetAnimalTypesAsync(isActive: true)).ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل أنواع الحيوانات: {ex.Message}", Severity.Error);
        }
    }

    private async Task OnBranchChanged()
    {
        animal.BreederId = 0; // إعادة تعيين المربي
        if (animal.BranchId > 0)
        {
            await LoadBreeders(animal.BranchId);
        }
    }

    private async Task LoadBreeders(int branchId)
    {
        try
        {
            var result = await BreederService.GetBreedersAsync(branchId: branchId, isActive: true);
            breeders = result.ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل المربين: {ex.Message}", Severity.Error);
        }
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/animals");
    }

    private async Task Submit()
    {
        if (!isFormValid) return;

        isSubmitting = true;
        try
        {
            await AnimalService.CreateAnimalAsync(animal);
            Snackbar.Add("تم إضافة الحيوان بنجاح", Severity.Success);
            Navigation.NavigateTo("/animals");
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في إضافة الحيوان: {ex.Message}", Severity.Error);
        }
        finally
        {
            isSubmitting = false;
        }
    }
}
