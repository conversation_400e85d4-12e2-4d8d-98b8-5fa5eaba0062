﻿<MudThemeProvider />
<MudPopoverProvider />
<MudDialogProvider />
<MudSnackbarProvider />

<Router AppAssembly="@typeof(App).Assembly">
    <Found Context="routeData">
        <RouteView RouteData="@routeData" DefaultLayout="@typeof(Layout.MainLayout)" />
        <FocusOnNavigate RouteData="@routeData" Selector="h1" />
    </Found>
    <NotFound>
        <PageTitle>غير موجود</PageTitle>
        <LayoutView Layout="@typeof(Layout.MainLayout)">
            <div class="d-flex justify-center align-center" style="height: 100vh;">
                <MudPaper Class="pa-8 text-center">
                    <MudIcon Icon="@Icons.Material.Filled.Error" Size="Size.Large" Color="Color.Error" Class="mb-4" />
                    <MudText Typo="Typo.h4" Class="mb-4">الصفحة غير موجودة</MudText>
                    <MudText Typo="Typo.body1" Class="mb-4">عذراً، الصفحة المطلوبة غير موجودة</MudText>
                    <MudButton Variant="Variant.Filled" Color="Color.Primary" Href="/">العودة للرئيسية</MudButton>
                </MudPaper>
            </div>
        </LayoutView>
    </NotFound>
</Router>
