﻿@inherits LayoutComponentBase
@namespace Baytara.Client.Layout
@using MudBlazor
@using Baytara.Client.Services
@inject IBranchService BranchService

<MudRTLProvider RightToLeft="true">
    <MudLayout>
        <!-- شريط علوي -->
        <MudAppBar Elevation="1" Fixed="true" Style="background: linear-gradient(45deg, #42A5F5 30%, #64B5F6 90%); font-family: 'Cairo', sans-serif;">
            <MudIconButton Icon="@Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start" OnClick="@ToggleDrawer" />

            <!-- العيادة البيطرية واسم الفرع على أقصى اليمين -->
            <div class="d-flex align-center" style="margin-right: auto;">
                <MudIcon Icon="@Icons.Material.Filled.Pets"
                         Style="color: white; font-size: 32px; margin-left: 12px; background: rgba(255,255,255,0.1); border-radius: 50%; padding: 8px; width: 48px; height: 48px; display: inline-flex; align-items: center; justify-content: center;" />
                <div>
                    <MudText Typo="Typo.h6" Style="color: white; font-weight: 600; margin: 0; font-family: 'Cairo', sans-serif; font-size: 18px;">
                        العيادة البيطرية - فرع مسقط الرئيسي
                    </MudText>
                    <MudText Typo="Typo.caption" Style="color: rgba(255,255,255,0.9); font-size: 12px; margin: 0; font-family: 'Cairo', sans-serif; line-height: 1.3;">
                        مسقط، سلطنة عمان - إدارة العيادات البيطرية
                    </MudText>
                </div>
            </div>

            <!-- اسم المستخدم على أقصى اليسار -->
            <div class="d-flex align-center" style="margin-left: auto;">
                <div class="text-right" style="margin-left: 12px;">
                    <MudText Typo="Typo.body2" Style="color: white; font-weight: 500; margin: 0; font-family: 'Cairo', sans-serif; font-size: 14px;">
                        مرحباً، @currentUserName
                    </MudText>
                    <MudText Typo="Typo.caption" Style="color: rgba(255,255,255,0.8); font-size: 11px; margin: 0; font-family: 'Cairo', sans-serif;">
                        @currentUserRole - @currentUserTitle
                    </MudText>
                </div>
                <MudIconButton Icon="@Icons.Material.Filled.AccountCircle"
                              Style="color: white; font-size: 32px;"
                              Size="Size.Large" />
            </div>
        </MudAppBar>

        <!-- الشريط الجانبي -->
        <MudDrawer @bind-Open="@_drawerOpen" Elevation="2" Variant="@DrawerVariant.Responsive"
                   ClipMode="DrawerClipMode.Always" Anchor="Anchor.Right"
                   Style="background: linear-gradient(180deg, #42A5F5 0%, #64B5F6 100%);">
            <NavMenu />
        </MudDrawer>

        <!-- المحتوى الرئيسي -->
        <MudMainContent>
            <MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="pa-4">
                <!-- إطار للمحتوى الرئيسي -->
                <MudPaper Class="pa-4" Elevation="2" Style="min-height: calc(100vh - 120px);">
                    @Body
                </MudPaper>
            </MudContainer>
        </MudMainContent>
    </MudLayout>
</MudRTLProvider>

@code {
    private bool _drawerOpen = true;

    // بيانات الفرع الحالي
    private string currentBranchName = "فرع مسقط الرئيسي";
    private string currentBranchAddress = "مسقط";

    // بيانات المستخدم الحالي
    private string currentUserName = "د. أحمد محمد";
    private string currentUserRole = "طبيب بيطري";
    private string currentUserTitle = "مدير النظام";

    protected override async Task OnInitializedAsync()
    {
        await LoadCurrentBranchInfo();
        await LoadCurrentUserInfo();
    }

    private void ToggleDrawer()
    {
        _drawerOpen = !_drawerOpen;
    }

    private async Task LoadCurrentBranchInfo()
    {
        try
        {
            // جلب معلومات الفرع الحالي من قاعدة البيانات
            var currentBranch = await BranchService.GetCurrentBranchAsync();
            currentBranchName = currentBranch?.Name ?? "فرع مسقط الرئيسي";
            currentBranchAddress = currentBranch?.Address ?? "مسقط";
        }
        catch (Exception ex)
        {
            // معالجة الأخطاء
            currentBranchName = "فرع افتراضي";
            currentBranchAddress = "الموقع";
        }
    }

    private async Task LoadCurrentUserInfo()
    {
        try
        {
            // TODO: جلب معلومات المستخدم الحالي من قاعدة البيانات
            // var currentUser = await UserService.GetCurrentUserAsync();
            // currentUserName = currentUser?.Name ?? "مستخدم";
            // currentUserRole = currentUser?.Role ?? "موظف";
            // currentUserTitle = currentUser?.Title ?? "مستخدم النظام";

            // بيانات مؤقتة للعرض
            currentUserName = "د. أحمد محمد";
            currentUserRole = "طبيب بيطري";
            currentUserTitle = "مدير النظام";
        }
        catch (Exception ex)
        {
            // معالجة الأخطاء
            currentUserName = "مستخدم";
            currentUserRole = "موظف";
            currentUserTitle = "مستخدم النظام";
        }
    }
}
