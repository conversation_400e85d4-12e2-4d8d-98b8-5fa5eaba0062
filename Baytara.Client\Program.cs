using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using MudBlazor.Services;
using Baytara.Client;
using Baytara.Client.Services;

var builder = WebAssemblyHostBuilder.CreateDefault(args);
builder.RootComponents.Add<App>("#app");
builder.RootComponents.Add<HeadOutlet>("head::after");

// إضافة MudBlazor
builder.Services.AddMudServices();

// إضافة HttpClient للتواصل مع الـ API
builder.Services.AddScoped(sp => new HttpClient { BaseAddress = new Uri("http://localhost:5287/") });

// إضافة خدمات التطبيق
builder.Services.AddScoped<IBreederService, BreederService>();
builder.Services.AddScoped<IBranchService, BranchService>();
builder.Services.AddScoped<IAnimalTypeService, AnimalTypeService>();
builder.Services.AddScoped<IAnimalService, AnimalService>();
builder.Services.AddScoped<IDiseaseService, DiseaseService>();
builder.Services.AddScoped<IMedicineService, MedicineService>();
builder.Services.AddScoped<ITreatmentService, TreatmentService>();
builder.Services.AddScoped<ILabTestService, LabTestService>();
builder.Services.AddScoped<IReportService, ReportService>();

await builder.Build().RunAsync();
