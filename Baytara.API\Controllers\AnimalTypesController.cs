using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Baytara.API.Data;
using Baytara.Shared.Models;

namespace Baytara.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AnimalTypesController : ControllerBase
{
    private readonly BaytaraDbContext _context;

    public AnimalTypesController(BaytaraDbContext context)
    {
        _context = context;
    }

    // GET: api/AnimalTypes
    [HttpGet]
    public async Task<ActionResult<IEnumerable<AnimalType>>> GetAnimalTypes(
        [FromQuery] string? search = null,
        [FromQuery] bool? isActive = null)
    {
        var query = _context.AnimalTypes.AsQueryable();

        if (!string.IsNullOrEmpty(search))
        {
            query = query.Where(at => at.Name.Contains(search) || 
                                    (at.Description != null && at.Description.Contains(search)));
        }

        if (isActive.HasValue)
        {
            query = query.Where(at => at.IsActive == isActive.Value);
        }

        var animalTypes = await query
            .OrderBy(at => at.Name)
            .ToListAsync();

        return Ok(animalTypes);
    }

    // GET: api/AnimalTypes/5
    [HttpGet("{id}")]
    public async Task<ActionResult<AnimalType>> GetAnimalType(int id)
    {
        var animalType = await _context.AnimalTypes.FindAsync(id);

        if (animalType == null)
        {
            return NotFound();
        }

        return Ok(animalType);
    }

    // POST: api/AnimalTypes
    [HttpPost]
    public async Task<ActionResult<AnimalType>> CreateAnimalType(AnimalType animalType)
    {
        // التحقق من عدم تكرار اسم نوع الحيوان
        var existingType = await _context.AnimalTypes
            .FirstOrDefaultAsync(at => at.Name == animalType.Name);

        if (existingType != null)
        {
            return BadRequest("اسم نوع الحيوان موجود مسبقاً");
        }

        animalType.CreatedAt = DateTime.UtcNow;
        _context.AnimalTypes.Add(animalType);
        await _context.SaveChangesAsync();

        return CreatedAtAction(nameof(GetAnimalType), new { id = animalType.Id }, animalType);
    }

    // PUT: api/AnimalTypes/5
    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateAnimalType(int id, AnimalType animalType)
    {
        if (id != animalType.Id)
        {
            return BadRequest();
        }

        var existingType = await _context.AnimalTypes.FindAsync(id);
        if (existingType == null)
        {
            return NotFound();
        }

        // التحقق من عدم تكرار اسم نوع الحيوان
        var duplicateType = await _context.AnimalTypes
            .FirstOrDefaultAsync(at => at.Name == animalType.Name && at.Id != id);

        if (duplicateType != null)
        {
            return BadRequest("اسم نوع الحيوان موجود مسبقاً");
        }

        existingType.Name = animalType.Name;
        existingType.Description = animalType.Description;
        existingType.IsActive = animalType.IsActive;

        try
        {
            await _context.SaveChangesAsync();
        }
        catch (DbUpdateConcurrencyException)
        {
            if (!AnimalTypeExists(id))
            {
                return NotFound();
            }
            throw;
        }

        return NoContent();
    }

    // DELETE: api/AnimalTypes/5
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteAnimalType(int id)
    {
        var animalType = await _context.AnimalTypes
            .Include(at => at.Animals)
            .FirstOrDefaultAsync(at => at.Id == id);

        if (animalType == null)
        {
            return NotFound();
        }

        // التحقق من وجود حيوانات مرتبطة
        if (animalType.Animals.Any())
        {
            return BadRequest("لا يمكن حذف نوع الحيوان لوجود حيوانات مرتبطة به");
        }

        _context.AnimalTypes.Remove(animalType);
        await _context.SaveChangesAsync();

        return NoContent();
    }

    private bool AnimalTypeExists(int id)
    {
        return _context.AnimalTypes.Any(e => e.Id == id);
    }
}
