using Baytara.Shared.DTOs;

namespace Baytara.Client.Services;

public interface IBreederService
{
    Task<IEnumerable<BreederDto>> GetBreedersAsync(string? search = null, int? branchId = null, string? region = null, bool? isActive = null, int page = 1, int pageSize = 10);
    Task<BreederDto?> GetBreederAsync(int id);
    Task<BreederDto> CreateBreederAsync(CreateBreederDto breeder);
    Task UpdateBreederAsync(int id, UpdateBreederDto breeder);
    Task DeleteBreederAsync(int id);
    Task<bool> CheckPhoneExistsAsync(string phone);
}
