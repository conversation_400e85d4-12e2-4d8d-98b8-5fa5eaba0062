@page "/lab-tests"
@inject ILabTestService LabTestService
@inject IBranchService BranchService
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<PageTitle>المختبرات والفحوصات - نظام بيطره</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge">
    <!-- العنوان وشريط البحث -->
    <MudPaper Class="pa-4 mb-4" Elevation="2">
        <MudGrid AlignItems="Center">
            <MudItem xs="12" md="6">
                <MudText Typo="Typo.h4" Color="Color.Primary">
                    <MudIcon Icon="@Icons.Material.Filled.Science" Class="ml-2" />
                    🔬 المختبرات والفحوصات
                </MudText>
                <MudText Typo="Typo.body1" Class="mt-2">
                    إدارة الفحوصات المختبرية والتحاليل البيطرية
                </MudText>
            </MudItem>
            <MudItem xs="12" md="6" Class="text-left">
                <MudButton Variant="Variant.Filled" 
                          Color="Color.Primary" 
                          StartIcon="@Icons.Material.Filled.Add"
                          Href="/lab-tests/add">
                    طلب فحص جديد
                </MudButton>
            </MudItem>
        </MudGrid>
        
        <!-- شريط البحث والفلاتر -->
        <MudGrid Class="mt-4">
            <MudItem xs="12" md="4">
                <MudTextField @bind-Value="searchText" 
                             Label="البحث" 
                             Placeholder="البحث باسم الفحص أو المختبر"
                             Adornment="Adornment.Start" 
                             AdornmentIcon="@Icons.Material.Filled.Search"
                             OnKeyUp="@(async (e) => { if (e.Key == "Enter") await SearchLabTests(); })" />
            </MudItem>
            <MudItem xs="12" md="2">
                <MudSelect @bind-Value="selectedBranchId" Label="الفرع" Clearable="true">
                    @foreach (var branch in branches)
                    {
                        <MudSelectItem Value="@branch.Id">@branch.Name</MudSelectItem>
                    }
                </MudSelect>
            </MudItem>
            <MudItem xs="12" md="2">
                <MudSelect @bind-Value="selectedType" Label="نوع الفحص" Clearable="true">
                    <MudSelectItem Value="@LabTestType.BloodTest">فحص دم</MudSelectItem>
                    <MudSelectItem Value="@LabTestType.UrineTest">فحص بول</MudSelectItem>
                    <MudSelectItem Value="@LabTestType.StoolTest">فحص براز</MudSelectItem>
                    <MudSelectItem Value="@LabTestType.SkinTest">فحص جلد</MudSelectItem>
                    <MudSelectItem Value="@LabTestType.Biopsy">خزعة</MudSelectItem>
                    <MudSelectItem Value="@LabTestType.Xray">أشعة سينية</MudSelectItem>
                    <MudSelectItem Value="@LabTestType.Ultrasound">موجات فوق صوتية</MudSelectItem>
                    <MudSelectItem Value="@LabTestType.Other">أخرى</MudSelectItem>
                </MudSelect>
            </MudItem>
            <MudItem xs="12" md="2">
                <MudSelect @bind-Value="selectedStatus" Label="حالة الفحص" Clearable="true">
                    <MudSelectItem Value="@LabTestStatus.Pending">في الانتظار</MudSelectItem>
                    <MudSelectItem Value="@LabTestStatus.InProgress">جاري</MudSelectItem>
                    <MudSelectItem Value="@LabTestStatus.Completed">مكتمل</MudSelectItem>
                    <MudSelectItem Value="@LabTestStatus.Cancelled">ملغي</MudSelectItem>
                </MudSelect>
            </MudItem>
            <MudItem xs="12" md="2">
                <MudButton Variant="Variant.Filled" 
                          Color="Color.Secondary" 
                          FullWidth="true"
                          OnClick="SearchLabTests">
                    بحث
                </MudButton>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- جدول الفحوصات -->
    <MudPaper Class="pa-4" Elevation="2">
        @if (isLoading)
        {
            <div class="text-center pa-8">
                <MudProgressCircular Indeterminate="true" />
                <MudText Class="mt-4">جاري تحميل البيانات...</MudText>
            </div>
        }
        else if (!labTests.Any())
        {
            <div class="text-center pa-8">
                <MudIcon Icon="@Icons.Material.Filled.SearchOff" Size="Size.Large" Color="Color.Secondary" />
                <MudText Typo="Typo.h6" Class="mt-4">لا توجد نتائج</MudText>
                <MudText>لم يتم العثور على فحوصات مطابقة لمعايير البحث</MudText>
            </div>
        }
        else
        {
            <MudTable Items="@labTests" Hover="true" Striped="true" Dense="true">
                <HeaderContent>
                    <MudTh>تاريخ الطلب</MudTh>
                    <MudTh>اسم الفحص</MudTh>
                    <MudTh>النوع</MudTh>
                    <MudTh>الحيوان</MudTh>
                    <MudTh>المربي</MudTh>
                    <MudTh>المختبر</MudTh>
                    <MudTh>الحالة</MudTh>
                    <MudTh>التكلفة (ر.ع)</MudTh>
                    <MudTh>الإجراءات</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd DataLabel="تاريخ الطلب">
                        -
                    </MudTd>
                    <MudTd DataLabel="اسم الفحص">
                        <MudText Typo="Typo.body2" Style="font-weight: 500;">-</MudText>
                    </MudTd>
                    <MudTd DataLabel="النوع">-</MudTd>
                    <MudTd DataLabel="الحيوان">
                        <div>
                            <MudText Typo="Typo.body2">
                                -
                            </MudText>
                            <MudText Typo="Typo.caption">-</MudText>
                        </div>
                    </MudTd>
                    <MudTd DataLabel="المربي">
                        <div>
                            <MudText Typo="Typo.body2">-</MudText>
                            <MudText Typo="Typo.caption">-</MudText>
                        </div>
                    </MudTd>
                    <MudTd DataLabel="المختبر">
                        -
                    </MudTd>
                    <MudTd DataLabel="الحالة">
                        <MudChip T="string" Size="Size.Small" Color="Color.Default">
                            -
                        </MudChip>
                    </MudTd>
                    <MudTd DataLabel="التكلفة">
                        0.00
                    </MudTd>
                    <MudTd DataLabel="الإجراءات">
                        <MudButtonGroup Variant="Variant.Text" Size="Size.Small">
                            <MudIconButton Icon="@Icons.Material.Filled.Visibility" 
                                          Color="Color.Info" 
                                          Size="Size.Small"
                                          OnClick="@(() => ViewLabTest(context))"
                                          Title="عرض التفاصيل" />
                            <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                          Color="Color.Primary"
                                          Size="Size.Small"
                                          Href="/lab-tests/edit/1"
                                          Title="تعديل" />
                            <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                          Color="Color.Error"
                                          Size="Size.Small"
                                          OnClick="@(() => DeleteLabTest(1))"
                                          Title="حذف" />
                        </MudButtonGroup>
                    </MudTd>
                </RowTemplate>
            </MudTable>

            <!-- التصفح -->
            <div class="d-flex justify-center mt-4">
                <MudPagination Count="@totalPages" 
                              Selected="@currentPage" 
                              SelectedChanged="@OnPageChanged" 
                              ShowFirstButton="true" 
                              ShowLastButton="true" />
            </div>
        }
    </MudPaper>
</MudContainer>

<!-- حوار عرض تفاصيل الفحص -->
<MudDialog @bind-IsVisible="showDetailsDialog" Options="dialogOptions">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.Science" Class="ml-2" />
            تفاصيل الفحص المختبري
        </MudText>
    </TitleContent>
    <DialogContent>
        <MudGrid>
            <MudItem xs="12" md="6">
                <MudText Typo="Typo.subtitle2" Style="font-weight: 500;">اسم الفحص:</MudText>
                <MudText Typo="Typo.body2">-</MudText>
            </MudItem>

            <MudItem xs="12" md="6">
                <MudText Typo="Typo.subtitle2" Style="font-weight: 500;">النوع:</MudText>
                <MudText Typo="Typo.body2">-</MudText>
            </MudItem>

            <MudItem xs="12">
                <MudText Typo="Typo.subtitle2" Style="font-weight: 500;">الوصف:</MudText>
                <MudText Typo="Typo.body2">-</MudText>
            </MudItem>

            <MudItem xs="12" md="6">
                <MudText Typo="Typo.subtitle2" Style="font-weight: 500;">تاريخ الطلب:</MudText>
                <MudText Typo="Typo.body2">-</MudText>
            </MudItem>

            <MudItem xs="12" md="6">
                <MudText Typo="Typo.subtitle2" Style="font-weight: 500;">الحالة:</MudText>
                <MudChip T="string" Size="Size.Small" Color="Color.Default">
                    -
                </MudChip>
            </MudItem>

            <MudItem xs="12" md="6">
                <MudText Typo="Typo.subtitle2" Style="font-weight: 500;">التكلفة:</MudText>
                <MudText Typo="Typo.body2">0.00 ر.ع</MudText>
            </MudItem>

            <MudItem xs="12" md="6">
                <MudText Typo="Typo.subtitle2" Style="font-weight: 500;">المختبر:</MudText>
                <MudText Typo="Typo.body2">-</MudText>
            </MudItem>
        </MudGrid>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="CloseDetailsDialog">إغلاق</MudButton>
    </DialogActions>
</MudDialog>

@code {
    private List<object> labTests = new();
    private List<Branch> branches = new();
    private bool isLoading = true;
    private string searchText = "";
    private int? selectedBranchId = null;
    private LabTestType? selectedType = null;
    private LabTestStatus? selectedStatus = null;
    private int currentPage = 1;
    private int pageSize = 10;
    private int totalPages = 1;
    private bool showDetailsDialog = false;
    private object? selectedLabTest = null;
    
    private DialogOptions dialogOptions = new() { MaxWidth = MaxWidth.Large, FullWidth = true };

    protected override async Task OnInitializedAsync()
    {
        await LoadBranches();
        await LoadLabTests();
    }

    private async Task LoadBranches()
    {
        try
        {
            branches = (await BranchService.GetBranchesAsync(isActive: true)).ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل الفروع: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadLabTests()
    {
        isLoading = true;
        try
        {
            var result = await LabTestService.GetLabTestsAsync(
                search: string.IsNullOrEmpty(searchText) ? null : searchText,
                branchId: selectedBranchId,
                page: currentPage,
                pageSize: pageSize
            );
            
            labTests = result.ToList();
            totalPages = Math.Max(1, (int)Math.Ceiling(labTests.Count / (double)pageSize));
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل الفحوصات: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task SearchLabTests()
    {
        currentPage = 1;
        await LoadLabTests();
    }

    private async Task OnPageChanged(int page)
    {
        currentPage = page;
        await LoadLabTests();
    }

    private void ViewLabTest(dynamic labTest)
    {
        selectedLabTest = labTest;
        showDetailsDialog = true;
    }

    private void CloseDetailsDialog()
    {
        showDetailsDialog = false;
        selectedLabTest = null;
    }

    private async Task DeleteLabTest(int labTestId)
    {
        try
        {
            await LabTestService.DeleteLabTestAsync(labTestId);
            await LoadLabTests();
            Snackbar.Add("تم حذف الفحص بنجاح", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في حذف الفحص: {ex.Message}", Severity.Error);
        }
    }

    private string GetLabTestTypeText(LabTestType type)
    {
        return type switch
        {
            LabTestType.BloodTest => "فحص دم",
            LabTestType.UrineTest => "فحص بول",
            LabTestType.StoolTest => "فحص براز",
            LabTestType.SkinTest => "فحص جلد",
            LabTestType.Biopsy => "خزعة",
            LabTestType.Xray => "أشعة سينية",
            LabTestType.Ultrasound => "موجات فوق صوتية",
            LabTestType.Other => "أخرى",
            _ => "غير محدد"
        };
    }

    private Color GetStatusColor(LabTestStatus status)
    {
        return status switch
        {
            LabTestStatus.Pending => Color.Info,
            LabTestStatus.InProgress => Color.Warning,
            LabTestStatus.Completed => Color.Success,
            LabTestStatus.Cancelled => Color.Error,
            _ => Color.Default
        };
    }

    private string GetStatusText(LabTestStatus status)
    {
        return status switch
        {
            LabTestStatus.Pending => "في الانتظار",
            LabTestStatus.InProgress => "جاري",
            LabTestStatus.Completed => "مكتمل",
            LabTestStatus.Cancelled => "ملغي",
            _ => "غير محدد"
        };
    }
}
