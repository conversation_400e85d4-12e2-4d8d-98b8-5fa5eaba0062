using System.Net.Http.Json;
using System.Text.Json;
using Baytara.Shared.Models;

namespace Baytara.Client.Services;

public class BranchService : IBranchService
{
    private readonly HttpClient _httpClient;
    private readonly JsonSerializerOptions _jsonOptions;

    public BranchService(HttpClient httpClient)
    {
        _httpClient = httpClient;
        _jsonOptions = new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase };
    }

    public async Task<IEnumerable<Branch>> GetBranchesAsync(string? search = null, bool? isActive = null)
    {
        try
        {
            var queryParams = new List<string>();
            if (!string.IsNullOrEmpty(search)) queryParams.Add($"search={Uri.EscapeDataString(search)}");
            if (isActive.HasValue) queryParams.Add($"isActive={isActive.Value}");

            var queryString = string.Join("&", queryParams);
            var response = await _httpClient.GetAsync($"api/branches?{queryString}");

            if (response.IsSuccessStatusCode)
            {
                var branches = await response.Content.ReadFromJsonAsync<IEnumerable<Branch>>(_jsonOptions);
                return branches ?? new List<Branch>();
            }

            // إذا فشل الطلب، إرجاع فرع افتراضي
            return new List<Branch>
            {
                new Branch { Id = 1, Name = "الفرع الرئيسي", IsActive = true }
            };
        }
        catch (Exception)
        {
            // في حالة فشل الاتصال، إرجاع فرع افتراضي
            return new List<Branch>
            {
                new Branch { Id = 1, Name = "الفرع الرئيسي", IsActive = true }
            };
        }
    }

    public async Task<Branch?> GetBranchAsync(int id)
    {
        var response = await _httpClient.GetAsync($"api/branches/{id}");
        return response.IsSuccessStatusCode ? await response.Content.ReadFromJsonAsync<Branch>(_jsonOptions) : null;
    }

    public async Task<Branch?> GetCurrentBranchAsync()
    {
        try
        {
            // محاولة جلب الفرع الحالي من API
            var response = await _httpClient.GetAsync("api/branches/current");

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<Branch>(_jsonOptions);
            }

            // إذا لم يكن هناك endpoint للفرع الحالي، جلب أول فرع نشط
            var branches = await GetBranchesAsync(isActive: true);
            return branches.FirstOrDefault();
        }
        catch (Exception)
        {
            // في حالة فشل الاتصال، إرجاع فرع افتراضي
            return new Branch
            {
                Id = 1,
                Name = "فرع مسقط الرئيسي",
                Address = "مسقط، سلطنة عمان",
                IsActive = true
            };
        }
    }

    public async Task<Branch> CreateBranchAsync(Branch branch)
    {
        var response = await _httpClient.PostAsJsonAsync("api/branches", branch, _jsonOptions);
        response.EnsureSuccessStatusCode();
        return await response.Content.ReadFromJsonAsync<Branch>(_jsonOptions) ?? throw new InvalidOperationException();
    }

    public async Task UpdateBranchAsync(int id, Branch branch)
    {
        var response = await _httpClient.PutAsJsonAsync($"api/branches/{id}", branch, _jsonOptions);
        response.EnsureSuccessStatusCode();
    }

    public async Task DeleteBranchAsync(int id)
    {
        var response = await _httpClient.DeleteAsync($"api/branches/{id}");
        response.EnsureSuccessStatusCode();
    }
}

public class AnimalTypeService : IAnimalTypeService
{
    private readonly HttpClient _httpClient;
    private readonly JsonSerializerOptions _jsonOptions;

    public AnimalTypeService(HttpClient httpClient)
    {
        _httpClient = httpClient;
        _jsonOptions = new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase };
    }

    public async Task<IEnumerable<AnimalType>> GetAnimalTypesAsync(string? search = null, bool? isActive = null)
    {
        var queryParams = new List<string>();
        if (!string.IsNullOrEmpty(search)) queryParams.Add($"search={Uri.EscapeDataString(search)}");
        if (isActive.HasValue) queryParams.Add($"isActive={isActive.Value}");

        var queryString = string.Join("&", queryParams);
        var response = await _httpClient.GetAsync($"api/animaltypes?{queryString}");
        
        if (response.IsSuccessStatusCode)
            return await response.Content.ReadFromJsonAsync<IEnumerable<AnimalType>>(_jsonOptions) ?? new List<AnimalType>();
        
        return new List<AnimalType>();
    }

    public async Task<AnimalType?> GetAnimalTypeAsync(int id)
    {
        var response = await _httpClient.GetAsync($"api/animaltypes/{id}");
        return response.IsSuccessStatusCode ? await response.Content.ReadFromJsonAsync<AnimalType>(_jsonOptions) : null;
    }

    public async Task<AnimalType> CreateAnimalTypeAsync(AnimalType animalType)
    {
        var response = await _httpClient.PostAsJsonAsync("api/animaltypes", animalType, _jsonOptions);
        response.EnsureSuccessStatusCode();
        return await response.Content.ReadFromJsonAsync<AnimalType>(_jsonOptions) ?? throw new InvalidOperationException();
    }

    public async Task UpdateAnimalTypeAsync(int id, AnimalType animalType)
    {
        var response = await _httpClient.PutAsJsonAsync($"api/animaltypes/{id}", animalType, _jsonOptions);
        response.EnsureSuccessStatusCode();
    }

    public async Task DeleteAnimalTypeAsync(int id)
    {
        var response = await _httpClient.DeleteAsync($"api/animaltypes/{id}");
        response.EnsureSuccessStatusCode();
    }
}

public class AnimalService : IAnimalService
{
    private readonly HttpClient _httpClient;
    private readonly JsonSerializerOptions _jsonOptions;

    public AnimalService(HttpClient httpClient)
    {
        _httpClient = httpClient;
        _jsonOptions = new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase };
    }

    public async Task<IEnumerable<object>> GetAnimalsAsync(string? search = null, int? branchId = null, int? breederId = null, int? animalTypeId = null, int page = 1, int pageSize = 10)
    {
        var queryParams = new List<string>();
        if (!string.IsNullOrEmpty(search)) queryParams.Add($"search={Uri.EscapeDataString(search)}");
        if (branchId.HasValue) queryParams.Add($"branchId={branchId.Value}");
        if (breederId.HasValue) queryParams.Add($"breederId={breederId.Value}");
        if (animalTypeId.HasValue) queryParams.Add($"animalTypeId={animalTypeId.Value}");
        queryParams.Add($"page={page}");
        queryParams.Add($"pageSize={pageSize}");

        var queryString = string.Join("&", queryParams);
        var response = await _httpClient.GetAsync($"api/animals?{queryString}");
        
        if (response.IsSuccessStatusCode)
            return await response.Content.ReadFromJsonAsync<IEnumerable<object>>(_jsonOptions) ?? new List<object>();
        
        return new List<object>();
    }

    public async Task<object?> GetAnimalAsync(int id)
    {
        var response = await _httpClient.GetAsync($"api/animals/{id}");
        return response.IsSuccessStatusCode ? await response.Content.ReadFromJsonAsync<object>(_jsonOptions) : null;
    }

    public async Task<object> CreateAnimalAsync(object animal)
    {
        var response = await _httpClient.PostAsJsonAsync("api/animals", animal, _jsonOptions);
        response.EnsureSuccessStatusCode();
        return await response.Content.ReadFromJsonAsync<object>(_jsonOptions) ?? throw new InvalidOperationException();
    }

    public async Task UpdateAnimalAsync(int id, object animal)
    {
        var response = await _httpClient.PutAsJsonAsync($"api/animals/{id}", animal, _jsonOptions);
        response.EnsureSuccessStatusCode();
    }

    public async Task DeleteAnimalAsync(int id)
    {
        var response = await _httpClient.DeleteAsync($"api/animals/{id}");
        response.EnsureSuccessStatusCode();
    }
}

public class DiseaseService : IDiseaseService
{
    private readonly HttpClient _httpClient;
    private readonly JsonSerializerOptions _jsonOptions;

    public DiseaseService(HttpClient httpClient)
    {
        _httpClient = httpClient;
        _jsonOptions = new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase };
    }

    public async Task<IEnumerable<Disease>> GetDiseasesAsync(string? search = null, bool? isActive = null)
    {
        var queryParams = new List<string>();
        if (!string.IsNullOrEmpty(search)) queryParams.Add($"search={Uri.EscapeDataString(search)}");
        if (isActive.HasValue) queryParams.Add($"isActive={isActive.Value}");

        var queryString = string.Join("&", queryParams);
        var response = await _httpClient.GetAsync($"api/diseases?{queryString}");
        
        if (response.IsSuccessStatusCode)
            return await response.Content.ReadFromJsonAsync<IEnumerable<Disease>>(_jsonOptions) ?? new List<Disease>();
        
        return new List<Disease>();
    }

    public async Task<Disease?> GetDiseaseAsync(int id)
    {
        var response = await _httpClient.GetAsync($"api/diseases/{id}");
        return response.IsSuccessStatusCode ? await response.Content.ReadFromJsonAsync<Disease>(_jsonOptions) : null;
    }

    public async Task<Disease> CreateDiseaseAsync(Disease disease)
    {
        var response = await _httpClient.PostAsJsonAsync("api/diseases", disease, _jsonOptions);
        response.EnsureSuccessStatusCode();
        return await response.Content.ReadFromJsonAsync<Disease>(_jsonOptions) ?? throw new InvalidOperationException();
    }

    public async Task UpdateDiseaseAsync(int id, Disease disease)
    {
        var response = await _httpClient.PutAsJsonAsync($"api/diseases/{id}", disease, _jsonOptions);
        response.EnsureSuccessStatusCode();
    }

    public async Task DeleteDiseaseAsync(int id)
    {
        var response = await _httpClient.DeleteAsync($"api/diseases/{id}");
        response.EnsureSuccessStatusCode();
    }
}

public class MedicineService : IMedicineService
{
    private readonly HttpClient _httpClient;
    private readonly JsonSerializerOptions _jsonOptions;

    public MedicineService(HttpClient httpClient)
    {
        _httpClient = httpClient;
        _jsonOptions = new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase };
    }

    public async Task<IEnumerable<Medicine>> GetMedicinesAsync(string? search = null, bool? isActive = null, int page = 1, int pageSize = 10)
    {
        var queryParams = new List<string>();
        if (!string.IsNullOrEmpty(search)) queryParams.Add($"search={Uri.EscapeDataString(search)}");
        if (isActive.HasValue) queryParams.Add($"isActive={isActive.Value}");
        queryParams.Add($"page={page}");
        queryParams.Add($"pageSize={pageSize}");

        var queryString = string.Join("&", queryParams);
        var response = await _httpClient.GetAsync($"api/medicines?{queryString}");
        
        if (response.IsSuccessStatusCode)
            return await response.Content.ReadFromJsonAsync<IEnumerable<Medicine>>(_jsonOptions) ?? new List<Medicine>();
        
        return new List<Medicine>();
    }

    public async Task<Medicine?> GetMedicineAsync(int id)
    {
        var response = await _httpClient.GetAsync($"api/medicines/{id}");
        return response.IsSuccessStatusCode ? await response.Content.ReadFromJsonAsync<Medicine>(_jsonOptions) : null;
    }

    public async Task<Medicine> CreateMedicineAsync(Medicine medicine)
    {
        var response = await _httpClient.PostAsJsonAsync("api/medicines", medicine, _jsonOptions);
        response.EnsureSuccessStatusCode();
        return await response.Content.ReadFromJsonAsync<Medicine>(_jsonOptions) ?? throw new InvalidOperationException();
    }

    public async Task UpdateMedicineAsync(int id, Medicine medicine)
    {
        var response = await _httpClient.PutAsJsonAsync($"api/medicines/{id}", medicine, _jsonOptions);
        response.EnsureSuccessStatusCode();
    }

    public async Task DeleteMedicineAsync(int id)
    {
        var response = await _httpClient.DeleteAsync($"api/medicines/{id}");
        response.EnsureSuccessStatusCode();
    }
}

public class TreatmentService : ITreatmentService
{
    private readonly HttpClient _httpClient;
    private readonly JsonSerializerOptions _jsonOptions;

    public TreatmentService(HttpClient httpClient)
    {
        _httpClient = httpClient;
        _jsonOptions = new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase };
    }

    public async Task<IEnumerable<object>> GetTreatmentsAsync(string? search = null, int? branchId = null, int? animalId = null, int page = 1, int pageSize = 10)
    {
        var queryParams = new List<string>();
        if (!string.IsNullOrEmpty(search)) queryParams.Add($"search={Uri.EscapeDataString(search)}");
        if (branchId.HasValue) queryParams.Add($"branchId={branchId.Value}");
        if (animalId.HasValue) queryParams.Add($"animalId={animalId.Value}");
        queryParams.Add($"page={page}");
        queryParams.Add($"pageSize={pageSize}");

        var queryString = string.Join("&", queryParams);
        var response = await _httpClient.GetAsync($"api/treatments?{queryString}");

        if (response.IsSuccessStatusCode)
            return await response.Content.ReadFromJsonAsync<IEnumerable<object>>(_jsonOptions) ?? new List<object>();

        return new List<object>();
    }

    public async Task<object?> GetTreatmentAsync(int id)
    {
        var response = await _httpClient.GetAsync($"api/treatments/{id}");
        return response.IsSuccessStatusCode ? await response.Content.ReadFromJsonAsync<object>(_jsonOptions) : null;
    }

    public async Task<object> CreateTreatmentAsync(object treatment)
    {
        var response = await _httpClient.PostAsJsonAsync("api/treatments", treatment, _jsonOptions);
        response.EnsureSuccessStatusCode();
        return await response.Content.ReadFromJsonAsync<object>(_jsonOptions) ?? throw new InvalidOperationException();
    }

    public async Task UpdateTreatmentAsync(int id, object treatment)
    {
        var response = await _httpClient.PutAsJsonAsync($"api/treatments/{id}", treatment, _jsonOptions);
        response.EnsureSuccessStatusCode();
    }

    public async Task DeleteTreatmentAsync(int id)
    {
        var response = await _httpClient.DeleteAsync($"api/treatments/{id}");
        response.EnsureSuccessStatusCode();
    }
}

public class LabTestService : ILabTestService
{
    private readonly HttpClient _httpClient;
    private readonly JsonSerializerOptions _jsonOptions;

    public LabTestService(HttpClient httpClient)
    {
        _httpClient = httpClient;
        _jsonOptions = new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase };
    }

    public async Task<IEnumerable<object>> GetLabTestsAsync(string? search = null, int? branchId = null, int? animalId = null, int page = 1, int pageSize = 10)
    {
        var queryParams = new List<string>();
        if (!string.IsNullOrEmpty(search)) queryParams.Add($"search={Uri.EscapeDataString(search)}");
        if (branchId.HasValue) queryParams.Add($"branchId={branchId.Value}");
        if (animalId.HasValue) queryParams.Add($"animalId={animalId.Value}");
        queryParams.Add($"page={page}");
        queryParams.Add($"pageSize={pageSize}");

        var queryString = string.Join("&", queryParams);
        var response = await _httpClient.GetAsync($"api/labtests?{queryString}");

        if (response.IsSuccessStatusCode)
            return await response.Content.ReadFromJsonAsync<IEnumerable<object>>(_jsonOptions) ?? new List<object>();

        return new List<object>();
    }

    public async Task<object?> GetLabTestAsync(int id)
    {
        var response = await _httpClient.GetAsync($"api/labtests/{id}");
        return response.IsSuccessStatusCode ? await response.Content.ReadFromJsonAsync<object>(_jsonOptions) : null;
    }

    public async Task<object> CreateLabTestAsync(object labTest)
    {
        var response = await _httpClient.PostAsJsonAsync("api/labtests", labTest, _jsonOptions);
        response.EnsureSuccessStatusCode();
        return await response.Content.ReadFromJsonAsync<object>(_jsonOptions) ?? throw new InvalidOperationException();
    }

    public async Task UpdateLabTestAsync(int id, object labTest)
    {
        var response = await _httpClient.PutAsJsonAsync($"api/labtests/{id}", labTest, _jsonOptions);
        response.EnsureSuccessStatusCode();
    }

    public async Task DeleteLabTestAsync(int id)
    {
        var response = await _httpClient.DeleteAsync($"api/labtests/{id}");
        response.EnsureSuccessStatusCode();
    }
}

public class ReportService : IReportService
{
    private readonly HttpClient _httpClient;
    private readonly JsonSerializerOptions _jsonOptions;

    public ReportService(HttpClient httpClient)
    {
        _httpClient = httpClient;
        _jsonOptions = new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase };
    }

    public async Task<object> GetDashboardStatsAsync(int? branchId = null)
    {
        var queryParams = new List<string>();
        if (branchId.HasValue) queryParams.Add($"branchId={branchId.Value}");

        var queryString = string.Join("&", queryParams);
        var response = await _httpClient.GetAsync($"api/reports/dashboard?{queryString}");

        if (response.IsSuccessStatusCode)
            return await response.Content.ReadFromJsonAsync<object>(_jsonOptions) ?? new object();

        return new object();
    }

    public async Task<IEnumerable<object>> GetBreedersReportAsync(int? branchId = null, DateTime? fromDate = null, DateTime? toDate = null)
    {
        var queryParams = new List<string>();
        if (branchId.HasValue) queryParams.Add($"branchId={branchId.Value}");
        if (fromDate.HasValue) queryParams.Add($"fromDate={fromDate.Value:yyyy-MM-dd}");
        if (toDate.HasValue) queryParams.Add($"toDate={toDate.Value:yyyy-MM-dd}");

        var queryString = string.Join("&", queryParams);
        var response = await _httpClient.GetAsync($"api/reports/breeders?{queryString}");

        if (response.IsSuccessStatusCode)
            return await response.Content.ReadFromJsonAsync<IEnumerable<object>>(_jsonOptions) ?? new List<object>();

        return new List<object>();
    }

    public async Task<IEnumerable<object>> GetAnimalsReportAsync(int? branchId = null)
    {
        var queryParams = new List<string>();
        if (branchId.HasValue) queryParams.Add($"branchId={branchId.Value}");

        var queryString = string.Join("&", queryParams);
        var response = await _httpClient.GetAsync($"api/reports/animals?{queryString}");

        if (response.IsSuccessStatusCode)
            return await response.Content.ReadFromJsonAsync<IEnumerable<object>>(_jsonOptions) ?? new List<object>();

        return new List<object>();
    }

    public async Task<IEnumerable<object>> GetTreatmentsReportAsync(int? branchId = null, DateTime? fromDate = null, DateTime? toDate = null)
    {
        var queryParams = new List<string>();
        if (branchId.HasValue) queryParams.Add($"branchId={branchId.Value}");
        if (fromDate.HasValue) queryParams.Add($"fromDate={fromDate.Value:yyyy-MM-dd}");
        if (toDate.HasValue) queryParams.Add($"toDate={toDate.Value:yyyy-MM-dd}");

        var queryString = string.Join("&", queryParams);
        var response = await _httpClient.GetAsync($"api/reports/treatments?{queryString}");

        if (response.IsSuccessStatusCode)
            return await response.Content.ReadFromJsonAsync<IEnumerable<object>>(_jsonOptions) ?? new List<object>();

        return new List<object>();
    }

    public async Task<IEnumerable<object>> GetFinancialReportAsync(int? branchId = null, DateTime? fromDate = null, DateTime? toDate = null)
    {
        var queryParams = new List<string>();
        if (branchId.HasValue) queryParams.Add($"branchId={branchId.Value}");
        if (fromDate.HasValue) queryParams.Add($"fromDate={fromDate.Value:yyyy-MM-dd}");
        if (toDate.HasValue) queryParams.Add($"toDate={toDate.Value:yyyy-MM-dd}");

        var queryString = string.Join("&", queryParams);
        var response = await _httpClient.GetAsync($"api/reports/financial?{queryString}");

        if (response.IsSuccessStatusCode)
            return await response.Content.ReadFromJsonAsync<IEnumerable<object>>(_jsonOptions) ?? new List<object>();

        return new List<object>();
    }
}
