@page "/branches"
@inject IBranchService BranchService
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<PageTitle>إدارة الفروع - نظام بيطارة</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large">
    <!-- العنوان وشريط البحث -->
    <MudPaper Class="pa-4 mb-4" Elevation="2">
        <MudGrid AlignItems="Center">
            <MudItem xs="12" md="6">
                <MudText Typo="Typo.h4" Color="Color.Primary">
                    <MudIcon Icon="@Icons.Material.Filled.Business" Class="ml-2" />
                    إدارة الفروع
                </MudText>
            </MudItem>
            <MudItem xs="12" md="6" Class="text-left">
                <MudButton Variant="Variant.Filled" 
                          Color="Color.Primary" 
                          StartIcon="@Icons.Material.Filled.Add"
                          OnClick="OpenAddDialog">
                    إضافة فرع جديد
                </MudButton>
            </MudItem>
        </MudGrid>
        
        <!-- شريط البحث -->
        <MudGrid Class="mt-4">
            <MudItem xs="12" md="8">
                <MudTextField @bind-Value="searchText" 
                             Label="البحث" 
                             Placeholder="البحث بالاسم أو العنوان"
                             Adornment="Adornment.Start" 
                             AdornmentIcon="@Icons.Material.Filled.Search"
                             OnKeyUp="@(async (e) => { if (e.Key == "Enter") await SearchBranches(); })" />
            </MudItem>
            <MudItem xs="12" md="2">
                <MudSwitch @bind-Value="showActiveOnly" Label="النشطة فقط" Color="Color.Primary" />
            </MudItem>
            <MudItem xs="12" md="2">
                <MudButton Variant="Variant.Filled" 
                          Color="Color.Secondary" 
                          FullWidth="true"
                          OnClick="SearchBranches">
                    بحث
                </MudButton>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- جدول الفروع -->
    <MudPaper Class="pa-4" Elevation="2">
        @if (isLoading)
        {
            <div class="text-center pa-8">
                <MudProgressCircular Indeterminate="true" />
                <MudText Class="mt-4">جاري تحميل البيانات...</MudText>
            </div>
        }
        else if (!branches.Any())
        {
            <div class="text-center pa-8">
                <MudIcon Icon="@Icons.Material.Filled.SearchOff" Size="Size.Large" Color="Color.Secondary" />
                <MudText Typo="Typo.h6" Class="mt-4">لا توجد نتائج</MudText>
                <MudText>لم يتم العثور على فروع مطابقة لمعايير البحث</MudText>
            </div>
        }
        else
        {
            <MudTable Items="@branches" Hover="true" Striped="true" Dense="true">
                <HeaderContent>
                    <MudTh>اسم الفرع</MudTh>
                    <MudTh>العنوان</MudTh>
                    <MudTh>رقم الهاتف</MudTh>
                    <MudTh>الحالة</MudTh>
                    <MudTh>تاريخ الإضافة</MudTh>
                    <MudTh>الإجراءات</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd DataLabel="اسم الفرع">
                        <MudText Typo="Typo.body2" Style="font-weight: 500;">@context.Name</MudText>
                    </MudTd>
                    <MudTd DataLabel="العنوان">
                        @(context.Address ?? "-")
                    </MudTd>
                    <MudTd DataLabel="رقم الهاتف">
                        @(context.Phone ?? "-")
                    </MudTd>
                    <MudTd DataLabel="الحالة">
                        <MudChip T="string" Size="Size.Small" 
                                Color="@(context.IsActive ? Color.Success : Color.Error)">
                            @(context.IsActive ? "نشط" : "غير نشط")
                        </MudChip>
                    </MudTd>
                    <MudTd DataLabel="تاريخ الإضافة">
                        @context.CreatedAt.ToString("yyyy/MM/dd")
                    </MudTd>
                    <MudTd DataLabel="الإجراءات">
                        <MudButtonGroup Variant="Variant.Text" Size="Size.Small">
                            <MudIconButton Icon="@Icons.Material.Filled.Edit" 
                                          Color="Color.Primary" 
                                          Size="Size.Small"
                                          OnClick="@(() => OpenEditDialog(context))"
                                          Title="تعديل" />
                            <MudIconButton Icon="@Icons.Material.Filled.Delete" 
                                          Color="Color.Error" 
                                          Size="Size.Small"
                                          OnClick="@(() => DeleteBranch(context.Id))"
                                          Title="حذف"
                                          Disabled="@(context.Id == 1)" />
                        </MudButtonGroup>
                    </MudTd>
                </RowTemplate>
            </MudTable>
        }
    </MudPaper>
</MudContainer>

<!-- حوار إضافة/تعديل الفرع -->
<MudDialog @bind-IsVisible="showDialog" Options="dialogOptions">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.Business" Class="ml-2" />
            @(isEditMode ? "تعديل الفرع" : "إضافة فرع جديد")
        </MudText>
    </TitleContent>
    <DialogContent>
        <MudForm @ref="form" @bind-IsValid="@isFormValid">
            <MudGrid>
                <MudItem xs="12">
                    <MudTextField @bind-Value="branchModel.Name"
                                 Label="اسم الفرع"
                                 Required="true"
                                 RequiredError="اسم الفرع مطلوب"
                                 MaxLength="100" />
                </MudItem>
                
                <MudItem xs="12">
                    <MudTextField @bind-Value="branchModel.Address"
                                 Label="العنوان"
                                 Lines="2"
                                 MaxLength="200" />
                </MudItem>
                
                <MudItem xs="12">
                    <MudTextField @bind-Value="branchModel.Phone"
                                 Label="رقم الهاتف"
                                 MaxLength="20" />
                </MudItem>
                
                @if (isEditMode)
                {
                    <MudItem xs="12">
                        <MudSwitch @bind-Value="branchModel.IsActive" 
                                  Label="نشط" 
                                  Color="Color.Primary"
                                  Disabled="@(branchModel.Id == 1)" />
                    </MudItem>
                }
            </MudGrid>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="CloseDialog">إلغاء</MudButton>
        <MudButton Color="Color.Primary" 
                  Variant="Variant.Filled" 
                  OnClick="SaveBranch"
                  Disabled="@(!isFormValid || isSubmitting)">
            @if (isSubmitting)
            {
                <MudProgressCircular Class="ml-2" Size="Size.Small" Indeterminate="true" />
                <span>جاري الحفظ...</span>
            }
            else
            {
                <span>@(isEditMode ? "حفظ التغييرات" : "إضافة")</span>
            }
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    private List<Branch> branches = new();
    private bool isLoading = true;
    private string searchText = "";
    private bool showActiveOnly = true;
    private bool showDialog = false;
    private bool isEditMode = false;
    private bool isFormValid = false;
    private bool isSubmitting = false;
    private MudForm form = null!;
    private Branch branchModel = new();
    
    private DialogOptions dialogOptions = new() { MaxWidth = MaxWidth.Small, FullWidth = true };

    protected override async Task OnInitializedAsync()
    {
        await LoadBranches();
    }

    private async Task LoadBranches()
    {
        isLoading = true;
        try
        {
            var result = await BranchService.GetBranchesAsync(
                search: string.IsNullOrEmpty(searchText) ? null : searchText,
                isActive: showActiveOnly ? true : null
            );
            
            branches = result.ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل الفروع: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task SearchBranches()
    {
        await LoadBranches();
    }

    private void OpenAddDialog()
    {
        branchModel = new Branch { IsActive = true };
        isEditMode = false;
        showDialog = true;
    }

    private void OpenEditDialog(Branch branch)
    {
        branchModel = new Branch
        {
            Id = branch.Id,
            Name = branch.Name,
            Address = branch.Address,
            Phone = branch.Phone,
            IsActive = branch.IsActive
        };
        isEditMode = true;
        showDialog = true;
    }

    private void CloseDialog()
    {
        showDialog = false;
        branchModel = new();
        isEditMode = false;
        isSubmitting = false;
    }

    private async Task SaveBranch()
    {
        if (!isFormValid) return;

        isSubmitting = true;
        try
        {
            if (isEditMode)
            {
                await BranchService.UpdateBranchAsync(branchModel.Id, branchModel);
                Snackbar.Add("تم تحديث الفرع بنجاح", Severity.Success);
            }
            else
            {
                await BranchService.CreateBranchAsync(branchModel);
                Snackbar.Add("تم إضافة الفرع بنجاح", Severity.Success);
            }
            
            CloseDialog();
            await LoadBranches();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في حفظ الفرع: {ex.Message}", Severity.Error);
        }
        finally
        {
            isSubmitting = false;
        }
    }

    private async Task DeleteBranch(int branchId)
    {
        if (branchId == 1)
        {
            Snackbar.Add("لا يمكن حذف الفرع الرئيسي", Severity.Warning);
            return;
        }

        try
        {
            await BranchService.DeleteBranchAsync(branchId);
            await LoadBranches();
            Snackbar.Add("تم حذف الفرع بنجاح", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في حذف الفرع: {ex.Message}", Severity.Error);
        }
    }
}
