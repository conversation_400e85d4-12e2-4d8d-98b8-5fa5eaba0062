using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using Baytara.Shared.DTOs;

namespace Baytara.Client.Services;

public class BreederService : IBreederService
{
    private readonly HttpClient _httpClient;
    private readonly JsonSerializerOptions _jsonOptions;

    public BreederService(HttpClient httpClient)
    {
        _httpClient = httpClient;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    public async Task<IEnumerable<BreederDto>> GetBreedersAsync(string? search = null, int? branchId = null, string? region = null, bool? isActive = null, int page = 1, int pageSize = 10)
    {
        try
        {
            var queryParams = new List<string>();

            if (!string.IsNullOrEmpty(search))
                queryParams.Add($"search={Uri.EscapeDataString(search)}");

            if (branchId.HasValue)
                queryParams.Add($"branchId={branchId.Value}");

            if (!string.IsNullOrEmpty(region))
                queryParams.Add($"region={Uri.EscapeDataString(region)}");

            if (isActive.HasValue)
                queryParams.Add($"isActive={isActive.Value}");

            queryParams.Add($"page={page}");
            queryParams.Add($"pageSize={pageSize}");

            var queryString = string.Join("&", queryParams);
            var response = await _httpClient.GetAsync($"api/breeders?{queryString}");

            if (response.IsSuccessStatusCode)
            {
                var breeders = await response.Content.ReadFromJsonAsync<IEnumerable<BreederDto>>(_jsonOptions);
                return breeders ?? new List<BreederDto>();
            }

            return new List<BreederDto>();
        }
        catch (Exception)
        {
            // في حالة فشل الاتصال، إرجاع قائمة فارغة
            return new List<BreederDto>();
        }
    }

    public async Task<BreederDto?> GetBreederAsync(int id)
    {
        var response = await _httpClient.GetAsync($"api/breeders/{id}");
        
        if (response.IsSuccessStatusCode)
        {
            return await response.Content.ReadFromJsonAsync<BreederDto>(_jsonOptions);
        }
        
        return null;
    }

    public async Task<BreederDto> CreateBreederAsync(CreateBreederDto breeder)
    {
        try
        {
            var json = JsonSerializer.Serialize(breeder, _jsonOptions);
            Console.WriteLine($"Sending data: {json}");

            var response = await _httpClient.PostAsJsonAsync("api/breeders", breeder, _jsonOptions);

            Console.WriteLine($"Response status: {response.StatusCode}");

            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<BreederDto>(_jsonOptions);
                return result ?? throw new InvalidOperationException("Failed to deserialize response");
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"Error response: {errorContent}");
                throw new InvalidOperationException($"خطأ من الخادم: {response.StatusCode} - {errorContent}");
            }
        }
        catch (HttpRequestException ex)
        {
            Console.WriteLine($"HttpRequestException: {ex.Message}");
            throw new InvalidOperationException($"خطأ في الاتصال بالخادم: {ex.Message}", ex);
        }
        catch (TaskCanceledException ex)
        {
            Console.WriteLine($"TaskCanceledException: {ex.Message}");
            throw new InvalidOperationException($"انتهت مهلة الاتصال: {ex.Message}", ex);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"General Exception: {ex.Message}");
            throw new InvalidOperationException($"خطأ في إنشاء المربي: {ex.Message}", ex);
        }
    }

    public async Task UpdateBreederAsync(int id, UpdateBreederDto breeder)
    {
        var response = await _httpClient.PutAsJsonAsync($"api/breeders/{id}", breeder, _jsonOptions);
        response.EnsureSuccessStatusCode();
    }

    public async Task DeleteBreederAsync(int id)
    {
        var response = await _httpClient.DeleteAsync($"api/breeders/{id}");
        response.EnsureSuccessStatusCode();
    }

    public async Task<bool> CheckPhoneExistsAsync(string phone)
    {
        var response = await _httpClient.GetAsync($"api/breeders/CheckPhone/{Uri.EscapeDataString(phone)}");
        
        if (response.IsSuccessStatusCode)
        {
            return await response.Content.ReadFromJsonAsync<bool>(_jsonOptions);
        }
        
        return false;
    }
}
