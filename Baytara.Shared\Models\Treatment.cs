using System.ComponentModel.DataAnnotations;

namespace Baytara.Shared.Models;

// حالة العلاج
public enum TreatmentStatus
{
    Planned = 1,    // مخطط
    InProgress = 2, // جاري
    Completed = 3,  // مكتمل
    Cancelled = 4   // ملغي
}

// نموذج العلاج
public class Treatment
{
    public int Id { get; set; }
    
    [Required(ErrorMessage = "تاريخ العلاج مطلوب")]
    public DateTime TreatmentDate { get; set; } = DateTime.UtcNow;
    
    [StringLength(500, ErrorMessage = "الأعراض يجب أن تكون أقل من 500 حرف")]
    public string? Symptoms { get; set; }
    
    [StringLength(500, ErrorMessage = "التشخيص يجب أن يكون أقل من 500 حرف")]
    public string? Diagnosis { get; set; }
    
    [StringLength(1000, ErrorMessage = "خطة العلاج يجب أن تكون أقل من 1000 حرف")]
    public string? TreatmentPlan { get; set; }
    
    [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
    public string? Notes { get; set; }
    
    [Required(ErrorMessage = "حالة العلاج مطلوبة")]
    public TreatmentStatus Status { get; set; } = TreatmentStatus.Planned;
    
    [Range(0, double.MaxValue, ErrorMessage = "التكلفة يجب أن تكون أكبر من أو تساوي صفر")]
    public decimal Cost { get; set; } = 0;
    
    public DateTime? NextVisitDate { get; set; }
    
    [StringLength(100, ErrorMessage = "اسم الطبيب يجب أن يكون أقل من 100 حرف")]
    public string? VeterinarianName { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? LastUpdated { get; set; }
    
    // المفاتيح الخارجية
    [Required(ErrorMessage = "الحيوان مطلوب")]
    public int AnimalId { get; set; }
    public virtual Animal Animal { get; set; } = null!;
    
    public int? DiseaseId { get; set; }
    public virtual Disease? Disease { get; set; }
    
    public int BranchId { get; set; }
    public virtual Branch Branch { get; set; } = null!;
    
    // العلاقات
    public virtual ICollection<TreatmentMedicine> TreatmentMedicines { get; set; } = new List<TreatmentMedicine>();
    public virtual ICollection<LabTest> LabTests { get; set; } = new List<LabTest>();
}

// نموذج ربط العلاج بالأدوية
public class TreatmentMedicine
{
    public int Id { get; set; }
    
    [Required(ErrorMessage = "العلاج مطلوب")]
    public int TreatmentId { get; set; }
    public virtual Treatment Treatment { get; set; } = null!;
    
    [Required(ErrorMessage = "الدواء مطلوب")]
    public int MedicineId { get; set; }
    public virtual Medicine Medicine { get; set; } = null!;
    
    [Required(ErrorMessage = "الكمية مطلوبة")]
    [Range(0.01, double.MaxValue, ErrorMessage = "الكمية يجب أن تكون أكبر من صفر")]
    public decimal Quantity { get; set; }
    
    [StringLength(200, ErrorMessage = "تعليمات الاستخدام يجب أن تكون أقل من 200 حرف")]
    public string? Instructions { get; set; }
    
    [Range(1, int.MaxValue, ErrorMessage = "عدد الأيام يجب أن يكون أكبر من صفر")]
    public int? DurationDays { get; set; }
    
    [Range(1, int.MaxValue, ErrorMessage = "عدد الجرعات يجب أن يكون أكبر من صفر")]
    public int? DosesPerDay { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}
