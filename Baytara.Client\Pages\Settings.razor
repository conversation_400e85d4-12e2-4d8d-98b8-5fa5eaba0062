@page "/settings"
@inject NavigationManager Navigation

<PageTitle>الإعدادات - نظام بيطارة</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large">
    <!-- العنوان -->
    <MudPaper Class="pa-4 mb-4" Elevation="2">
        <MudText Typo="Typo.h4" Color="Color.Primary">
            <MudIcon Icon="@Icons.Material.Filled.Settings" Class="ml-2" />
            إعدادات النظام
        </MudText>
        <MudText Typo="Typo.body1" Class="mt-2">
            إدارة الإعدادات الأساسية للنظام والبيانات المرجعية
        </MudText>
    </MudPaper>

    <!-- بطاقات الإعدادات -->
    <MudGrid>
        <!-- إدارة الفروع -->
        <MudItem xs="12" md="6" lg="4">
            <MudCard Class="pa-4 h-100" Elevation="3">
                <MudCardContent Class="text-center">
                    <MudIcon Icon="@Icons.Material.Filled.Business" 
                            Size="Size.Large" 
                            Color="Color.Primary" 
                            Class="mb-4" />
                    <MudText Typo="Typo.h6" Class="mb-2">إدارة الفروع</MudText>
                    <MudText Typo="Typo.body2" Class="mb-4">
                        إضافة وتعديل وحذف فروع العيادة البيطرية
                    </MudText>
                </MudCardContent>
                <MudCardActions Class="justify-center">
                    <MudButton Variant="Variant.Filled" 
                              Color="Color.Primary" 
                              FullWidth="true"
                              Href="/branches">
                        إدارة الفروع
                    </MudButton>
                </MudCardActions>
            </MudCard>
        </MudItem>

        <!-- أنواع الحيوانات -->
        <MudItem xs="12" md="6" lg="4">
            <MudCard Class="pa-4 h-100" Elevation="3">
                <MudCardContent Class="text-center">
                    <MudIcon Icon="@Icons.Material.Filled.Category" 
                            Size="Size.Large" 
                            Color="Color.Secondary" 
                            Class="mb-4" />
                    <MudText Typo="Typo.h6" Class="mb-2">أنواع الحيوانات</MudText>
                    <MudText Typo="Typo.body2" Class="mb-4">
                        إدارة أنواع الحيوانات المختلفة في النظام
                    </MudText>
                </MudCardContent>
                <MudCardActions Class="justify-center">
                    <MudButton Variant="Variant.Filled" 
                              Color="Color.Secondary" 
                              FullWidth="true"
                              Href="/animal-types">
                        إدارة الأنواع
                    </MudButton>
                </MudCardActions>
            </MudCard>
        </MudItem>

        <!-- إدارة الأدوية -->
        <MudItem xs="12" md="6" lg="4">
            <MudCard Class="pa-4 h-100" Elevation="3">
                <MudCardContent Class="text-center">
                    <MudIcon Icon="@Icons.Material.Filled.Medication" 
                            Size="Size.Large" 
                            Color="Color.Success" 
                            Class="mb-4" />
                    <MudText Typo="Typo.h6" Class="mb-2">إدارة الأدوية</MudText>
                    <MudText Typo="Typo.body2" Class="mb-4">
                        إدارة مخزون الأدوية والعقاقير البيطرية
                    </MudText>
                </MudCardContent>
                <MudCardActions Class="justify-center">
                    <MudButton Variant="Variant.Filled" 
                              Color="Color.Success" 
                              FullWidth="true"
                              Href="/medicines">
                        إدارة الأدوية
                    </MudButton>
                </MudCardActions>
            </MudCard>
        </MudItem>

        <!-- إدارة الأمراض -->
        <MudItem xs="12" md="6" lg="4">
            <MudCard Class="pa-4 h-100" Elevation="3">
                <MudCardContent Class="text-center">
                    <MudIcon Icon="@Icons.Material.Filled.Coronavirus" 
                            Size="Size.Large" 
                            Color="Color.Warning" 
                            Class="mb-4" />
                    <MudText Typo="Typo.h6" Class="mb-2">إدارة الأمراض</MudText>
                    <MudText Typo="Typo.body2" Class="mb-4">
                        إدارة قاعدة بيانات الأمراض البيطرية
                    </MudText>
                </MudCardContent>
                <MudCardActions Class="justify-center">
                    <MudButton Variant="Variant.Filled" 
                              Color="Color.Warning" 
                              FullWidth="true"
                              Href="/diseases">
                        إدارة الأمراض
                    </MudButton>
                </MudCardActions>
            </MudCard>
        </MudItem>

        <!-- إعدادات النظام -->
        <MudItem xs="12" md="6" lg="4">
            <MudCard Class="pa-4 h-100" Elevation="3">
                <MudCardContent Class="text-center">
                    <MudIcon Icon="@Icons.Material.Filled.Tune" 
                            Size="Size.Large" 
                            Color="Color.Info" 
                            Class="mb-4" />
                    <MudText Typo="Typo.h6" Class="mb-2">إعدادات النظام</MudText>
                    <MudText Typo="Typo.body2" Class="mb-4">
                        إعدادات عامة للنظام والتفضيلات
                    </MudText>
                </MudCardContent>
                <MudCardActions Class="justify-center">
                    <MudButton Variant="Variant.Filled" 
                              Color="Color.Info" 
                              FullWidth="true"
                              OnClick="ShowSystemSettings">
                        إعدادات النظام
                    </MudButton>
                </MudCardActions>
            </MudCard>
        </MudItem>

        <!-- النسخ الاحتياطي -->
        <MudItem xs="12" md="6" lg="4">
            <MudCard Class="pa-4 h-100" Elevation="3">
                <MudCardContent Class="text-center">
                    <MudIcon Icon="@Icons.Material.Filled.Backup" 
                            Size="Size.Large" 
                            Color="Color.Dark" 
                            Class="mb-4" />
                    <MudText Typo="Typo.h6" Class="mb-2">النسخ الاحتياطي</MudText>
                    <MudText Typo="Typo.body2" Class="mb-4">
                        إنشاء واستعادة النسخ الاحتياطية للبيانات
                    </MudText>
                </MudCardContent>
                <MudCardActions Class="justify-center">
                    <MudButton Variant="Variant.Filled" 
                              Color="Color.Dark" 
                              FullWidth="true"
                              OnClick="ShowBackupSettings">
                        إدارة النسخ
                    </MudButton>
                </MudCardActions>
            </MudCard>
        </MudItem>
    </MudGrid>

    <!-- إحصائيات سريعة -->
    <MudPaper Class="pa-4 mt-4" Elevation="2">
        <MudText Typo="Typo.h5" Class="mb-4">إحصائيات النظام</MudText>
        <MudGrid>
            <MudItem xs="12" sm="6" md="3">
                <MudPaper Class="pa-4 text-center" Elevation="1">
                    <MudIcon Icon="@Icons.Material.Filled.Business" Size="Size.Large" Color="Color.Primary" Class="mb-2" />
                    <MudText Typo="Typo.h4" Color="Color.Primary">1</MudText>
                    <MudText Typo="Typo.body1">الفروع النشطة</MudText>
                </MudPaper>
            </MudItem>
            
            <MudItem xs="12" sm="6" md="3">
                <MudPaper Class="pa-4 text-center" Elevation="1">
                    <MudIcon Icon="@Icons.Material.Filled.Category" Size="Size.Large" Color="Color.Secondary" Class="mb-2" />
                    <MudText Typo="Typo.h4" Color="Color.Secondary">5</MudText>
                    <MudText Typo="Typo.body1">أنواع الحيوانات</MudText>
                </MudPaper>
            </MudItem>
            
            <MudItem xs="12" sm="6" md="3">
                <MudPaper Class="pa-4 text-center" Elevation="1">
                    <MudIcon Icon="@Icons.Material.Filled.Medication" Size="Size.Large" Color="Color.Success" Class="mb-2" />
                    <MudText Typo="Typo.h4" Color="Color.Success">0</MudText>
                    <MudText Typo="Typo.body1">الأدوية المتوفرة</MudText>
                </MudPaper>
            </MudItem>
            
            <MudItem xs="12" sm="6" md="3">
                <MudPaper Class="pa-4 text-center" Elevation="1">
                    <MudIcon Icon="@Icons.Material.Filled.Coronavirus" Size="Size.Large" Color="Color.Warning" Class="mb-2" />
                    <MudText Typo="Typo.h4" Color="Color.Warning">0</MudText>
                    <MudText Typo="Typo.body1">الأمراض المسجلة</MudText>
                </MudPaper>
            </MudItem>
        </MudGrid>
    </MudPaper>
</MudContainer>

<!-- حوار إعدادات النظام -->
<MudDialog @bind-IsVisible="showSystemSettingsDialog" Options="dialogOptions">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.Tune" Class="ml-2" />
            إعدادات النظام
        </MudText>
    </TitleContent>
    <DialogContent>
        <MudGrid>
            <MudItem xs="12">
                <MudTextField @bind-Value="clinicName" 
                             Label="اسم العيادة" 
                             FullWidth="true" />
            </MudItem>
            
            <MudItem xs="12">
                <MudTextField @bind-Value="clinicAddress" 
                             Label="عنوان العيادة" 
                             Lines="2"
                             FullWidth="true" />
            </MudItem>
            
            <MudItem xs="12" md="6">
                <MudTextField @bind-Value="clinicPhone" 
                             Label="هاتف العيادة" 
                             FullWidth="true" />
            </MudItem>
            
            <MudItem xs="12" md="6">
                <MudSelect @bind-Value="defaultCurrency" Label="العملة الافتراضية">
                    <MudSelectItem Value="@("OMR")">ريال عماني (OMR)</MudSelectItem>
                    <MudSelectItem Value="@("SAR")">ريال سعودي (SAR)</MudSelectItem>
                    <MudSelectItem Value="@("AED")">درهم إماراتي (AED)</MudSelectItem>
                    <MudSelectItem Value="@("USD")">دولار أمريكي (USD)</MudSelectItem>
                </MudSelect>
            </MudItem>
            
            <MudItem xs="12">
                <MudSwitch @bind-Value="enableNotifications" 
                          Label="تفعيل الإشعارات" 
                          Color="Color.Primary" />
            </MudItem>
            
            <MudItem xs="12">
                <MudSwitch @bind-Value="enableAutoBackup" 
                          Label="النسخ الاحتياطي التلقائي" 
                          Color="Color.Primary" />
            </MudItem>
        </MudGrid>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="CloseSystemSettingsDialog">إلغاء</MudButton>
        <MudButton Color="Color.Primary" 
                  Variant="Variant.Filled" 
                  OnClick="SaveSystemSettings">
            حفظ الإعدادات
        </MudButton>
    </DialogActions>
</MudDialog>

<!-- حوار النسخ الاحتياطي -->
<MudDialog @bind-IsVisible="showBackupDialog" Options="dialogOptions">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.Backup" Class="ml-2" />
            إدارة النسخ الاحتياطية
        </MudText>
    </TitleContent>
    <DialogContent>
        <MudGrid>
            <MudItem xs="12">
                <MudText Typo="Typo.h6" Class="mb-4">إنشاء نسخة احتياطية</MudText>
                <MudButton Variant="Variant.Filled" 
                          Color="Color.Primary" 
                          StartIcon="@Icons.Material.Filled.Download"
                          FullWidth="true"
                          Class="mb-4">
                    تحميل نسخة احتياطية
                </MudButton>
            </MudItem>
            
            <MudItem xs="12">
                <MudText Typo="Typo.h6" Class="mb-4">استعادة نسخة احتياطية</MudText>
                <MudFileUpload T="IBrowserFile" Accept=".bak,.sql" FilesChanged="OnBackupFileSelected">
                    <ActivatorContent>
                        <MudButton Variant="Variant.Filled"
                                  Color="Color.Secondary"
                                  StartIcon="@Icons.Material.Filled.Upload">
                            اختيار ملف النسخة الاحتياطية
                        </MudButton>
                    </ActivatorContent>
                </MudFileUpload>
                
                @if (selectedBackupFile != null)
                {
                    <MudText Class="mt-2">الملف المحدد: @selectedBackupFile.Name</MudText>
                    <MudButton Variant="Variant.Filled" 
                              Color="Color.Warning" 
                              StartIcon="@Icons.Material.Filled.Restore"
                              Class="mt-2"
                              OnClick="RestoreBackup">
                        استعادة النسخة الاحتياطية
                    </MudButton>
                }
            </MudItem>
        </MudGrid>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="CloseBackupDialog">إغلاق</MudButton>
    </DialogActions>
</MudDialog>

@code {
    private bool showSystemSettingsDialog = false;
    private bool showBackupDialog = false;
    private DialogOptions dialogOptions = new() { MaxWidth = MaxWidth.Medium, FullWidth = true };
    
    // إعدادات النظام
    private string clinicName = "نظام بيطره لإدارة الثروة الحيوانية";
    private string clinicAddress = "مسقط، سلطنة عمان";
    private string clinicPhone = "+968 24123456";
    private string defaultCurrency = "OMR";
    private bool enableNotifications = true;
    private bool enableAutoBackup = false;
    
    // النسخ الاحتياطي
    private IBrowserFile? selectedBackupFile;

    private void ShowSystemSettings()
    {
        showSystemSettingsDialog = true;
    }

    private void CloseSystemSettingsDialog()
    {
        showSystemSettingsDialog = false;
    }

    private void SaveSystemSettings()
    {
        // حفظ الإعدادات في قاعدة البيانات أو التخزين المحلي
        showSystemSettingsDialog = false;
        // يمكن إضافة منطق حفظ الإعدادات هنا
    }

    private void ShowBackupSettings()
    {
        showBackupDialog = true;
    }

    private void CloseBackupDialog()
    {
        showBackupDialog = false;
        selectedBackupFile = null;
    }

    private void OnBackupFileSelected(IBrowserFile file)
    {
        selectedBackupFile = file;
    }

    private void RestoreBackup()
    {
        if (selectedBackupFile != null)
        {
            // منطق استعادة النسخة الاحتياطية
            CloseBackupDialog();
        }
    }
}
