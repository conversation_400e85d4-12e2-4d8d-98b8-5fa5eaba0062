using System.ComponentModel.DataAnnotations;

namespace Baytara.Shared.Models;

// نوع الدواء
public enum MedicineType
{
    Antibiotic = 1,     // مضاد حيوي
    Vaccine = 2,        // لقاح
    Vitamin = 3,        // فيتامين
    Painkiller = 4,     // مسكن
    AntiInflammatory = 5, // مضاد التهاب
    Other = 6           // أخرى
}

// وحدة القياس
public enum MeasurementUnit
{
    ML = 1,     // مليلتر
    MG = 2,     // ميليجرام
    Tablet = 3, // قرص
    Capsule = 4, // كبسولة
    Injection = 5, // حقنة
    Dose = 6    // جرعة
}

// نموذج الدواء
public class Medicine
{
    public int Id { get; set; }
    
    [Required(ErrorMessage = "اسم الدواء مطلوب")]
    [StringLength(100, ErrorMessage = "اسم الدواء يجب أن يكون أقل من 100 حرف")]
    public string Name { get; set; } = string.Empty;
    
    [StringLength(100, ErrorMessage = "الاسم التجاري يجب أن يكون أقل من 100 حرف")]
    public string? TradeName { get; set; }
    
    [Required(ErrorMessage = "نوع الدواء مطلوب")]
    public MedicineType Type { get; set; }
    
    [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
    public string? Description { get; set; }
    
    [StringLength(200, ErrorMessage = "الشركة المصنعة يجب أن تكون أقل من 200 حرف")]
    public string? Manufacturer { get; set; }
    
    [Required(ErrorMessage = "وحدة القياس مطلوبة")]
    public MeasurementUnit Unit { get; set; }
    
    [Range(0, double.MaxValue, ErrorMessage = "الكمية المتوفرة يجب أن تكون أكبر من أو تساوي صفر")]
    public decimal AvailableQuantity { get; set; } = 0;
    
    [Range(0, double.MaxValue, ErrorMessage = "السعر يجب أن يكون أكبر من أو يساوي صفر")]
    public decimal Price { get; set; } = 0;
    
    public DateTime? ExpiryDate { get; set; }
    
    [StringLength(500, ErrorMessage = "طريقة الاستخدام يجب أن تكون أقل من 500 حرف")]
    public string? UsageInstructions { get; set; }
    
    [StringLength(500, ErrorMessage = "التحذيرات يجب أن تكون أقل من 500 حرف")]
    public string? Warnings { get; set; }
    
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    // العلاقات
    public virtual ICollection<TreatmentMedicine> TreatmentMedicines { get; set; } = new List<TreatmentMedicine>();
}
