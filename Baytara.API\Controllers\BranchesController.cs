using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Baytara.API.Data;
using Baytara.Shared.Models;

namespace Baytara.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class BranchesController : ControllerBase
{
    private readonly BaytaraDbContext _context;

    public BranchesController(BaytaraDbContext context)
    {
        _context = context;
    }

    // GET: api/Branches
    [HttpGet]
    public async Task<ActionResult<IEnumerable<Branch>>> GetBranches(
        [FromQuery] string? search = null,
        [FromQuery] bool? isActive = null)
    {
        var query = _context.Branches.AsQueryable();

        if (!string.IsNullOrEmpty(search))
        {
            query = query.Where(b => b.Name.Contains(search) || 
                                   (b.Address != null && b.Address.Contains(search)));
        }

        if (isActive.HasValue)
        {
            query = query.Where(b => b.IsActive == isActive.Value);
        }

        var branches = await query
            .OrderBy(b => b.Name)
            .ToListAsync();

        return Ok(branches);
    }

    // GET: api/Branches/5
    [HttpGet("{id}")]
    public async Task<ActionResult<Branch>> GetBranch(int id)
    {
        var branch = await _context.Branches.FindAsync(id);

        if (branch == null)
        {
            return NotFound();
        }

        return Ok(branch);
    }

    // POST: api/Branches
    [HttpPost]
    public async Task<ActionResult<Branch>> CreateBranch(Branch branch)
    {
        // التحقق من عدم تكرار اسم الفرع
        var existingBranch = await _context.Branches
            .FirstOrDefaultAsync(b => b.Name == branch.Name);

        if (existingBranch != null)
        {
            return BadRequest("اسم الفرع موجود مسبقاً");
        }

        branch.CreatedAt = DateTime.UtcNow;
        _context.Branches.Add(branch);
        await _context.SaveChangesAsync();

        return CreatedAtAction(nameof(GetBranch), new { id = branch.Id }, branch);
    }

    // PUT: api/Branches/5
    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateBranch(int id, Branch branch)
    {
        if (id != branch.Id)
        {
            return BadRequest();
        }

        var existingBranch = await _context.Branches.FindAsync(id);
        if (existingBranch == null)
        {
            return NotFound();
        }

        // التحقق من عدم تكرار اسم الفرع
        var duplicateBranch = await _context.Branches
            .FirstOrDefaultAsync(b => b.Name == branch.Name && b.Id != id);

        if (duplicateBranch != null)
        {
            return BadRequest("اسم الفرع موجود مسبقاً");
        }

        existingBranch.Name = branch.Name;
        existingBranch.Address = branch.Address;
        existingBranch.Phone = branch.Phone;
        existingBranch.IsActive = branch.IsActive;

        try
        {
            await _context.SaveChangesAsync();
        }
        catch (DbUpdateConcurrencyException)
        {
            if (!BranchExists(id))
            {
                return NotFound();
            }
            throw;
        }

        return NoContent();
    }

    // DELETE: api/Branches/5
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteBranch(int id)
    {
        var branch = await _context.Branches
            .Include(b => b.Breeders)
            .Include(b => b.Animals)
            .Include(b => b.Treatments)
            .FirstOrDefaultAsync(b => b.Id == id);

        if (branch == null)
        {
            return NotFound();
        }

        // التحقق من وجود بيانات مرتبطة
        if (branch.Breeders.Any() || branch.Animals.Any() || branch.Treatments.Any())
        {
            return BadRequest("لا يمكن حذف الفرع لوجود بيانات مرتبطة به");
        }

        _context.Branches.Remove(branch);
        await _context.SaveChangesAsync();

        return NoContent();
    }

    private bool BranchExists(int id)
    {
        return _context.Branches.Any(e => e.Id == id);
    }
}
