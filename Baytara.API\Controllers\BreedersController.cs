using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Baytara.API.Data;
using Baytara.Shared.Models;
using Baytara.Shared.DTOs;

namespace Baytara.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class BreedersController : ControllerBase
{
    private readonly BaytaraDbContext _context;

    public BreedersController(BaytaraDbContext context)
    {
        _context = context;
    }

    // GET: api/Breeders
    [HttpGet]
    public async Task<ActionResult<IEnumerable<BreederDto>>> GetBreeders(
        [FromQuery] string? search = null,
        [FromQuery] int? branchId = null,
        [FromQuery] string? region = null,
        [FromQuery] bool? isActive = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10)
    {
        var query = _context.Breeders
            .Include(b => b.Branch)
            .Include(b => b.Animals)
            .AsQueryable();

        // تطبيق الفلاتر
        if (!string.IsNullOrEmpty(search))
        {
            query = query.Where(b => b.Name.Contains(search) || 
                                   b.Phone.Contains(search) || 
                                   (b.NationalId != null && b.NationalId.Contains(search)));
        }

        if (branchId.HasValue)
        {
            query = query.Where(b => b.BranchId == branchId.Value);
        }

        if (!string.IsNullOrEmpty(region))
        {
            query = query.Where(b => b.Region != null && b.Region.Contains(region));
        }

        if (isActive.HasValue)
        {
            query = query.Where(b => b.IsActive == isActive.Value);
        }

        // ترتيب النتائج
        query = query.OrderBy(b => b.Name);

        // تطبيق التصفح
        var totalCount = await query.CountAsync();
        var breeders = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .Select(b => new BreederDto
            {
                Id = b.Id,
                Name = b.Name,
                Phone = b.Phone,
                Address = b.Address,
                Region = b.Region,
                NationalId = b.NationalId,
                IsActive = b.IsActive,
                CreatedAt = b.CreatedAt,
                BranchId = b.BranchId,
                BranchName = b.Branch.Name,
                AnimalsCount = b.Animals.Count
            })
            .ToListAsync();

        Response.Headers["X-Total-Count"] = totalCount.ToString();
        Response.Headers["X-Page"] = page.ToString();
        Response.Headers["X-Page-Size"] = pageSize.ToString();

        return Ok(breeders);
    }

    // GET: api/Breeders/5
    [HttpGet("{id}")]
    public async Task<ActionResult<BreederDto>> GetBreeder(int id)
    {
        var breeder = await _context.Breeders
            .Include(b => b.Branch)
            .Include(b => b.Animals)
            .FirstOrDefaultAsync(b => b.Id == id);

        if (breeder == null)
        {
            return NotFound();
        }

        var breederDto = new BreederDto
        {
            Id = breeder.Id,
            Name = breeder.Name,
            Phone = breeder.Phone,
            Address = breeder.Address,
            Region = breeder.Region,
            NationalId = breeder.NationalId,
            IsActive = breeder.IsActive,
            CreatedAt = breeder.CreatedAt,
            BranchId = breeder.BranchId,
            BranchName = breeder.Branch.Name,
            AnimalsCount = breeder.Animals.Count
        };

        return Ok(breederDto);
    }

    // POST: api/Breeders
    [HttpPost]
    public async Task<ActionResult<BreederDto>> CreateBreeder(CreateBreederDto createBreederDto)
    {
        // التحقق من وجود رقم الهاتف مسبقاً
        var existingBreeder = await _context.Breeders
            .FirstOrDefaultAsync(b => b.Phone == createBreederDto.Phone);

        if (existingBreeder != null)
        {
            return BadRequest("رقم الهاتف مستخدم مسبقاً");
        }

        // التحقق من وجود الفرع
        var branch = await _context.Branches.FindAsync(createBreederDto.BranchId);
        if (branch == null)
        {
            return BadRequest("الفرع المحدد غير موجود");
        }

        var breeder = new Breeder
        {
            Name = createBreederDto.Name,
            Phone = createBreederDto.Phone,
            Address = createBreederDto.Address,
            Region = createBreederDto.Region,
            NationalId = createBreederDto.NationalId,
            BranchId = createBreederDto.BranchId,
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };

        _context.Breeders.Add(breeder);
        await _context.SaveChangesAsync();

        // إعادة تحميل البيانات مع العلاقات
        await _context.Entry(breeder)
            .Reference(b => b.Branch)
            .LoadAsync();

        var breederDto = new BreederDto
        {
            Id = breeder.Id,
            Name = breeder.Name,
            Phone = breeder.Phone,
            Address = breeder.Address,
            Region = breeder.Region,
            NationalId = breeder.NationalId,
            IsActive = breeder.IsActive,
            CreatedAt = breeder.CreatedAt,
            BranchId = breeder.BranchId,
            BranchName = breeder.Branch.Name,
            AnimalsCount = 0
        };

        return CreatedAtAction(nameof(GetBreeder), new { id = breeder.Id }, breederDto);
    }

    // PUT: api/Breeders/5
    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateBreeder(int id, UpdateBreederDto updateBreederDto)
    {
        var breeder = await _context.Breeders.FindAsync(id);
        if (breeder == null)
        {
            return NotFound();
        }

        // التحقق من رقم الهاتف إذا تم تغييره
        if (breeder.Phone != updateBreederDto.Phone)
        {
            var existingBreeder = await _context.Breeders
                .FirstOrDefaultAsync(b => b.Phone == updateBreederDto.Phone && b.Id != id);

            if (existingBreeder != null)
            {
                return BadRequest("رقم الهاتف مستخدم مسبقاً");
            }
        }

        // التحقق من وجود الفرع
        var branch = await _context.Branches.FindAsync(updateBreederDto.BranchId);
        if (branch == null)
        {
            return BadRequest("الفرع المحدد غير موجود");
        }

        breeder.Name = updateBreederDto.Name;
        breeder.Phone = updateBreederDto.Phone;
        breeder.Address = updateBreederDto.Address;
        breeder.Region = updateBreederDto.Region;
        breeder.NationalId = updateBreederDto.NationalId;
        breeder.IsActive = updateBreederDto.IsActive;
        breeder.BranchId = updateBreederDto.BranchId;

        try
        {
            await _context.SaveChangesAsync();
        }
        catch (DbUpdateConcurrencyException)
        {
            if (!BreederExists(id))
            {
                return NotFound();
            }
            throw;
        }

        return NoContent();
    }

    // DELETE: api/Breeders/5
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteBreeder(int id)
    {
        var breeder = await _context.Breeders
            .FirstOrDefaultAsync(b => b.Id == id);

        if (breeder == null)
        {
            return NotFound();
        }

        // التحقق من وجود حيوانات مرتبطة (بطريقة مبسطة)
        var hasAnimals = await _context.Animals.AnyAsync(a => a.BreederId == id);
        if (hasAnimals)
        {
            return BadRequest("لا يمكن حذف المربي لوجود حيوانات مرتبطة به");
        }

        _context.Breeders.Remove(breeder);
        await _context.SaveChangesAsync();

        return NoContent();
    }

    // GET: api/Breeders/CheckPhone/{phone}
    [HttpGet("CheckPhone/{phone}")]
    public async Task<ActionResult<bool>> CheckPhoneExists(string phone)
    {
        var exists = await _context.Breeders.AnyAsync(b => b.Phone == phone);
        return Ok(exists);
    }

    private bool BreederExists(int id)
    {
        return _context.Breeders.Any(e => e.Id == id);
    }
}
