using Baytara.Shared.Models;

namespace Baytara.Client.Services;

public interface IBranchService
{
    Task<IEnumerable<Branch>> GetBranchesAsync(string? search = null, bool? isActive = null);
    Task<Branch?> GetBranchAsync(int id);
    Task<Branch?> GetCurrentBranchAsync();
    Task<Branch> CreateBranchAsync(Branch branch);
    Task UpdateBranchAsync(int id, Branch branch);
    Task DeleteBranchAsync(int id);
}

public interface IAnimalTypeService
{
    Task<IEnumerable<AnimalType>> GetAnimalTypesAsync(string? search = null, bool? isActive = null);
    Task<AnimalType?> GetAnimalTypeAsync(int id);
    Task<AnimalType> CreateAnimalTypeAsync(AnimalType animalType);
    Task UpdateAnimalTypeAsync(int id, AnimalType animalType);
    Task DeleteAnimalTypeAsync(int id);
}

public interface IAnimalService
{
    Task<IEnumerable<object>> GetAnimalsAsync(string? search = null, int? branchId = null, int? breederId = null, int? animalTypeId = null, int page = 1, int pageSize = 10);
    Task<object?> GetAnimalAsync(int id);
    Task<object> CreateAnimalAsync(object animal);
    Task UpdateAnimalAsync(int id, object animal);
    Task DeleteAnimalAsync(int id);
}

public interface IDiseaseService
{
    Task<IEnumerable<Disease>> GetDiseasesAsync(string? search = null, bool? isActive = null);
    Task<Disease?> GetDiseaseAsync(int id);
    Task<Disease> CreateDiseaseAsync(Disease disease);
    Task UpdateDiseaseAsync(int id, Disease disease);
    Task DeleteDiseaseAsync(int id);
}

public interface IMedicineService
{
    Task<IEnumerable<Medicine>> GetMedicinesAsync(string? search = null, bool? isActive = null, int page = 1, int pageSize = 10);
    Task<Medicine?> GetMedicineAsync(int id);
    Task<Medicine> CreateMedicineAsync(Medicine medicine);
    Task UpdateMedicineAsync(int id, Medicine medicine);
    Task DeleteMedicineAsync(int id);
}

public interface ITreatmentService
{
    Task<IEnumerable<object>> GetTreatmentsAsync(string? search = null, int? branchId = null, int? animalId = null, int page = 1, int pageSize = 10);
    Task<object?> GetTreatmentAsync(int id);
    Task<object> CreateTreatmentAsync(object treatment);
    Task UpdateTreatmentAsync(int id, object treatment);
    Task DeleteTreatmentAsync(int id);
}

public interface ILabTestService
{
    Task<IEnumerable<object>> GetLabTestsAsync(string? search = null, int? branchId = null, int? animalId = null, int page = 1, int pageSize = 10);
    Task<object?> GetLabTestAsync(int id);
    Task<object> CreateLabTestAsync(object labTest);
    Task UpdateLabTestAsync(int id, object labTest);
    Task DeleteLabTestAsync(int id);
}

public interface IReportService
{
    Task<object> GetDashboardStatsAsync(int? branchId = null);
    Task<IEnumerable<object>> GetBreedersReportAsync(int? branchId = null, DateTime? fromDate = null, DateTime? toDate = null);
    Task<IEnumerable<object>> GetAnimalsReportAsync(int? branchId = null);
    Task<IEnumerable<object>> GetTreatmentsReportAsync(int? branchId = null, DateTime? fromDate = null, DateTime? toDate = null);
    Task<IEnumerable<object>> GetFinancialReportAsync(int? branchId = null, DateTime? fromDate = null, DateTime? toDate = null);
}
