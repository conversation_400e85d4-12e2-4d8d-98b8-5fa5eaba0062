@page "/breeders"
@inject IBreederService BreederService
@inject IBranchService BranchService
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<PageTitle>المربين والحيوانات - نظام بيطره</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge">
    <!-- العنوان وشريط البحث -->
    <MudPaper Class="pa-4 mb-4" Elevation="2">
        <MudGrid AlignItems="Center">
            <MudItem xs="12" md="6">
                <MudText Typo="Typo.h4" Color="Color.Primary">
                    <MudIcon Icon="@Icons.Material.Filled.People" Class="ml-2" />
                    المربين والحيوانات
                </MudText>
                <MudText Typo="Typo.body1" Class="mt-2">
                    إدارة شاملة للمربين وحيواناتهم
                </MudText>
            </MudItem>
            <MudItem xs="12" md="6" Class="text-left">
                <MudButton Variant="Variant.Filled"
                          Color="Color.Primary"
                          StartIcon="@Icons.Material.Filled.Add"
                          Href="/breeders/add">
                    إضافة مربي جديد
                </MudButton>
            </MudItem>
        </MudGrid>
        
        <!-- شريط البحث والفلاتر -->
        <MudGrid Class="mt-4">
            <MudItem xs="12" md="4">
                <MudTextField @bind-Value="searchText" 
                             Label="البحث" 
                             Placeholder="البحث بالاسم، الهاتف، أو رقم الهوية"
                             Adornment="Adornment.Start" 
                             AdornmentIcon="@Icons.Material.Filled.Search"
                             OnKeyUp="@(async (e) => { if (e.Key == "Enter") await SearchBreeders(); })" />
            </MudItem>
            <MudItem xs="12" md="3">
                <MudSelect T="int?" @bind-Value="selectedBranchId" Label="الفرع" Clearable="true">
                    @foreach (var branch in branches)
                    {
                        <MudSelectItem T="int?" Value="@branch.Id">@branch.Name</MudSelectItem>
                    }
                </MudSelect>
            </MudItem>
            <MudItem xs="12" md="3">
                <MudTextField @bind-Value="regionFilter" 
                             Label="المنطقة" 
                             Placeholder="فلترة حسب المنطقة" />
            </MudItem>
            <MudItem xs="12" md="2">
                <MudButton Variant="Variant.Filled" 
                          Color="Color.Secondary" 
                          FullWidth="true"
                          OnClick="SearchBreeders">
                    بحث
                </MudButton>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- جدول المربين -->
    <MudPaper Class="pa-4" Elevation="2">
        @if (isLoading)
        {
            <div class="text-center pa-8">
                <MudProgressCircular Indeterminate="true" />
                <MudText Class="mt-4">جاري تحميل البيانات...</MudText>
            </div>
        }
        else if (!breeders.Any())
        {
            <div class="text-center pa-8">
                <MudIcon Icon="@Icons.Material.Filled.SearchOff" Size="Size.Large" Color="Color.Secondary" />
                <MudText Typo="Typo.h6" Class="mt-4">لا توجد نتائج</MudText>
                <MudText>لم يتم العثور على مربين مطابقين لمعايير البحث</MudText>
            </div>
        }
        else
        {
            <MudTable Items="@breeders" Hover="true" Striped="true" Dense="true">
                <HeaderContent>
                    <MudTh>الاسم</MudTh>
                    <MudTh>رقم الهاتف</MudTh>
                    <MudTh>المنطقة</MudTh>
                    <MudTh>الفرع</MudTh>
                    <MudTh>عدد الحيوانات</MudTh>
                    <MudTh>الحالة</MudTh>
                    <MudTh>الإجراءات</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd DataLabel="الاسم">
                        <MudText Typo="Typo.body2" Style="font-weight: 500;">@context.Name</MudText>
                    </MudTd>
                    <MudTd DataLabel="رقم الهاتف">@context.Phone</MudTd>
                    <MudTd DataLabel="المنطقة">@(context.Region ?? "-")</MudTd>
                    <MudTd DataLabel="الفرع">@context.BranchName</MudTd>
                    <MudTd DataLabel="عدد الحيوانات">
                        <MudChip T="string" Size="Size.Small" Color="Color.Info">@context.AnimalsCount</MudChip>
                    </MudTd>
                    <MudTd DataLabel="الحالة">
                        <MudChip T="string" Size="Size.Small"
                                Color="@(context.IsActive ? Color.Success : Color.Error)">
                            @(context.IsActive ? "نشط" : "غير نشط")
                        </MudChip>
                    </MudTd>
                    <MudTd DataLabel="الإجراءات">
                        <MudButtonGroup Variant="Variant.Text" Size="Size.Small">
                            <MudIconButton Icon="@Icons.Material.Filled.Visibility"
                                          Color="Color.Success"
                                          Size="Size.Small"
                                          OnClick="@(() => ViewBreederAnimals(context))"
                                          Title="عرض التفاصيل" />
                            <MudIconButton Icon="@Icons.Material.Filled.Agriculture"
                                          Color="Color.Info"
                                          Size="Size.Small"
                                          OnClick="@(() => AddAnimalToBreeder(context))"
                                          Title="إضافة حيوان" />
                            <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                          Color="Color.Primary"
                                          Size="Size.Small"
                                          Href="@($"/breeders/edit/{context.Id}")"
                                          Title="تعديل المربي" />
                            <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                          Color="Color.Error"
                                          Size="Size.Small"
                                          OnClick="@(() => DeleteBreeder(context.Id))"
                                          Title="حذف المربي" />
                        </MudButtonGroup>
                    </MudTd>
                </RowTemplate>
            </MudTable>

            <!-- التصفح -->
            <div class="d-flex justify-center mt-4">
                <MudPagination Count="@totalPages" 
                              Selected="@currentPage" 
                              SelectedChanged="@OnPageChanged" 
                              ShowFirstButton="true" 
                              ShowLastButton="true" />
            </div>
        }
    </MudPaper>
</MudContainer>

<!-- حوار عرض حيوانات المربي -->
<MudDialog @bind-IsVisible="showAnimalsDialog" Options="animalsDialogOptions">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.Pets" Class="ml-2" />
            حيوانات المربي: @selectedBreeder?.Name
        </MudText>
    </TitleContent>
    <DialogContent>
        @if (breederAnimals.Any())
        {
            <MudTable Items="@breederAnimals" Dense="true" Hover="true">
                <HeaderContent>
                    <MudTh>النوع</MudTh>
                    <MudTh>الاسم</MudTh>
                    <MudTh>العدد</MudTh>
                    <MudTh>الحالة</MudTh>
                    <MudTh>الإجراءات</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd>-</MudTd>
                    <MudTd>-</MudTd>
                    <MudTd>0</MudTd>
                    <MudTd>
                        <MudChip T="string" Size="Size.Small" Color="Color.Default">
                            -
                        </MudChip>
                    </MudTd>
                    <MudTd>
                        <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                      Size="Size.Small"
                                      Color="Color.Primary"
                                      OnClick="@(() => EditAnimal(context))" />
                        <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                      Size="Size.Small"
                                      Color="Color.Error"
                                      OnClick="@(() => DeleteAnimal(1))" />
                    </MudTd>
                </RowTemplate>
            </MudTable>
        }
        else
        {
            <div class="text-center pa-4">
                <MudIcon Icon="@Icons.Material.Filled.Pets" Size="Size.Large" Color="Color.Secondary" />
                <MudText Typo="Typo.h6" Class="mt-2">لا توجد حيوانات</MudText>
                <MudText>لم يتم تسجيل أي حيوانات لهذا المربي بعد</MudText>
            </div>
        }
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="CloseAnimalsDialog">إغلاق</MudButton>
        <MudButton Color="Color.Primary"
                  Variant="Variant.Filled"
                  StartIcon="@Icons.Material.Filled.Agriculture"
                  OnClick="@(() => AddAnimalToBreeder(selectedBreeder))">
            إضافة حيوان جديد
        </MudButton>
    </DialogActions>
</MudDialog>

<!-- حوار إضافة حيوان جديد -->
<MudDialog @bind-IsVisible="showAddAnimalDialog" Options="addAnimalDialogOptions">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.Agriculture" Class="ml-2" />
            إضافة حيوان جديد للمربي: @selectedBreeder?.Name
        </MudText>
    </TitleContent>
    <DialogContent>
        <MudGrid>
            <MudItem xs="12" md="6">
                <MudSelect @bind-Value="newAnimal.AnimalTypeId" Label="نوع الحيوان" Required="true">
                    @foreach (var type in animalTypes)
                    {
                        <MudSelectItem Value="@type.Id">@type.Name</MudSelectItem>
                    }
                </MudSelect>
            </MudItem>

            <MudItem xs="12" md="6">
                <MudTextField @bind-Value="newAnimal.Name" Label="اسم الحيوان (اختياري)" />
            </MudItem>

            <MudItem xs="12" md="6">
                <MudNumericField @bind-Value="newAnimal.Count" Label="العدد" Min="1" Required="true" />
            </MudItem>

            <MudItem xs="12" md="6">
                <MudSelect @bind-Value="newAnimal.Category" Label="التصنيف">
                    <MudSelectItem Value="@AnimalCategory.Free">مجاني</MudSelectItem>
                    <MudSelectItem Value="@AnimalCategory.Economic">اقتصادي</MudSelectItem>
                    <MudSelectItem Value="@AnimalCategory.NonEconomic">غير اقتصادي</MudSelectItem>
                </MudSelect>
            </MudItem>

            <MudItem xs="12">
                <MudTextField @bind-Value="newAnimal.Notes" Label="ملاحظات" Lines="2" />
            </MudItem>
        </MudGrid>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="CloseAddAnimalDialog">إلغاء</MudButton>
        <MudButton Color="Color.Primary"
                  Variant="Variant.Filled"
                  OnClick="SaveNewAnimal"
                  Disabled="@isSavingAnimal">
            @if (isSavingAnimal)
            {
                <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                <span class="ml-2">جاري الحفظ...</span>
            }
            else
            {
                <span>حفظ الحيوان</span>
            }
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    private List<BreederDto> breeders = new();
    private List<Branch> branches = new();
    private List<object> breederAnimals = new();
    private List<AnimalType> animalTypes = new();
    private bool isLoading = true;
    private string searchText = "";
    private string regionFilter = "";
    private int? selectedBranchId = null;
    private int currentPage = 1;
    private int pageSize = 10;
    private int totalPages = 1;

    // حوارات
    private bool showAnimalsDialog = false;
    private bool showAddAnimalDialog = false;
    private bool isSavingAnimal = false;
    private BreederDto? selectedBreeder = null;
    private Animal newAnimal = new();

    private DialogOptions animalsDialogOptions = new() { MaxWidth = MaxWidth.Large, FullWidth = true };
    private DialogOptions addAnimalDialogOptions = new() { MaxWidth = MaxWidth.Medium, FullWidth = true };

    protected override async Task OnInitializedAsync()
    {
        await LoadBranches();
        await LoadAnimalTypes();
        await LoadBreeders();
    }

    private async Task LoadBranches()
    {
        try
        {
            branches = (await BranchService.GetBranchesAsync(isActive: true)).ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل الفروع: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadBreeders()
    {
        isLoading = true;
        try
        {
            var result = await BreederService.GetBreedersAsync(
                search: string.IsNullOrEmpty(searchText) ? null : searchText,
                branchId: selectedBranchId,
                region: string.IsNullOrEmpty(regionFilter) ? null : regionFilter,
                page: currentPage,
                pageSize: pageSize
            );
            
            breeders = result.ToList();
            // حساب عدد الصفحات (يجب إضافة header للعدد الكلي من الـ API)
            totalPages = Math.Max(1, (int)Math.Ceiling(breeders.Count / (double)pageSize));
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل المربين: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task SearchBreeders()
    {
        currentPage = 1;
        await LoadBreeders();
    }

    private async Task OnPageChanged(int page)
    {
        currentPage = page;
        await LoadBreeders();
    }



    private async Task LoadAnimalTypes()
    {
        try
        {
            // يجب إضافة خدمة أنواع الحيوانات
            animalTypes = new List<AnimalType>
            {
                new AnimalType { Id = 1, Name = "إبل" },
                new AnimalType { Id = 2, Name = "أبقار" },
                new AnimalType { Id = 3, Name = "أغنام" },
                new AnimalType { Id = 4, Name = "ماعز" },
                new AnimalType { Id = 5, Name = "دواجن" }
            };
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل أنواع الحيوانات: {ex.Message}", Severity.Error);
        }
    }

    private async Task ViewBreederAnimals(BreederDto breeder)
    {
        selectedBreeder = breeder;
        try
        {
            // تحميل حيوانات المربي
            breederAnimals = new List<object>(); // مؤقت
            showAnimalsDialog = true;
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في تحميل حيوانات المربي: {ex.Message}", Severity.Error);
        }
    }

    private void AddAnimalToBreeder(BreederDto breeder)
    {
        selectedBreeder = breeder;
        newAnimal = new Animal
        {
            BreederId = breeder.Id,
            BranchId = breeder.BranchId,
            Category = AnimalCategory.Free,
            Status = AnimalStatus.Healthy,
            Count = 1
        };
        showAddAnimalDialog = true;
    }

    private void CloseAnimalsDialog()
    {
        showAnimalsDialog = false;
        selectedBreeder = null;
    }

    private void CloseAddAnimalDialog()
    {
        showAddAnimalDialog = false;
        selectedBreeder = null;
        newAnimal = new Animal();
    }

    private async Task SaveNewAnimal()
    {
        isSavingAnimal = true;
        try
        {
            // حفظ الحيوان الجديد
            Snackbar.Add("تم إضافة الحيوان بنجاح", Severity.Success);
            CloseAddAnimalDialog();
            await LoadBreeders(); // إعادة تحميل لتحديث عدد الحيوانات
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في إضافة الحيوان: {ex.Message}", Severity.Error);
        }
        finally
        {
            isSavingAnimal = false;
        }
    }

    private void EditAnimal(object animal)
    {
        // تنفيذ تعديل الحيوان
        Snackbar.Add("سيتم تنفيذ تعديل الحيوان قريباً", Severity.Info);
    }

    private async Task DeleteAnimal(int animalId)
    {
        try
        {
            // حذف الحيوان
            Snackbar.Add("تم حذف الحيوان بنجاح", Severity.Success);
            await ViewBreederAnimals(selectedBreeder!); // إعادة تحميل القائمة
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في حذف الحيوان: {ex.Message}", Severity.Error);
        }
    }

    private Color GetAnimalStatusColor(AnimalStatus status)
    {
        return status switch
        {
            AnimalStatus.Healthy => Color.Success,
            AnimalStatus.Sick => Color.Warning,
            AnimalStatus.UnderTreatment => Color.Info,
            AnimalStatus.Dead => Color.Dark,
            _ => Color.Default
        };
    }

    private string GetAnimalStatusText(AnimalStatus status)
    {
        return status switch
        {
            AnimalStatus.Healthy => "سليم",
            AnimalStatus.Sick => "مريض",
            AnimalStatus.UnderTreatment => "تحت العلاج",
            AnimalStatus.Dead => "نافق",
            _ => "غير محدد"
        };
    }

    private async Task DeleteBreeder(int breederId)
    {
        try
        {
            await BreederService.DeleteBreederAsync(breederId);
            await LoadBreeders();
            Snackbar.Add("تم حذف المربي بنجاح", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في حذف المربي: {ex.Message}", Severity.Error);
        }
    }
}
