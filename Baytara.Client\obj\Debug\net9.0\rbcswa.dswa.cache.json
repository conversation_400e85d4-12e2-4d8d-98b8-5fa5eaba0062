{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["Os30ozOUKjAlYdvE6BTHoLQXirr6Ji3xgcF9/cGDFAI=", "Fkng+0tbNZXLhXNYQtAhHvdsvJhXmh428kk19FPiwsQ=", "T+fv+gD1UxHtr6TuS2Js+caJUyoexKS7oF2n25aHVWs=", "UGFbeKsy/Sv1wAd50Fce7sP3jtGYY0z2phFG5tk0Wg0=", "8SOcqg38vT31sXxf3zwcQv2gS5LDW6V6YVBxOcj+5PY=", "It12qmT6GpYSEyYaOZ8XdKlQ5hBMEJwbxad91h12f/k=", "EUgMHicjUIznsNd9pKqgS3XM166PMxj04F0GKKBwoCo=", "un0ojZC4Hs8fCgwz3L4+4x05W4n+wYFjgEZIl2v+FMg=", "OBauY+a9/8jN4cN7XfXsan+BAcab2VqD/sp0Sh7kXfA=", "V/Ep5M8hiLnsTs8D36sJFNUxcgfDb+HEPX6Kizto/EQ=", "aIOqmuyRAXuSazjqhxA36BkflbuzpYzSuq+kVjwvHks=", "o6aAMbkfPb1r8YeqGp6UVu+u+gKNATcKaS6XSHbMR4o=", "kyrXYmfBcly6hGzk5U5Uqb82o7zjHHrdgSlefqR54yc=", "QK7/LqChV27XxmyOltIHM4iTcjn0X/XPlldNl3yLVq8=", "hq0vlecwlhpyAsQ/q/aQzB0wQFy1R/G1lH5jvNL7hWQ=", "uJMIVwIIh7sU6GKVcNkFhdRbDUDdrc22tm+bPnBUoHI=", "E4nGmdo0Grs/dtT4ZKz4sUIQ5dJn5Ru8jpjjsGpBNeM=", "2Nn6ZGTBvXFFuKFtMbOh3vq/vxwJKm3kJGLilKzdprk=", "+70FsqZWqDa4fpg8i7qlvsB1BJoLAStAZ5Ksjsag1EQ=", "B/csOmgu+os2A37r0JpgD1DPV0AZIdjeLtlgfpj7mTI=", "LbneNPQ/J4I2zMm4bzYzF8Ao7TMJW4w7hDisTIB5hps=", "Ki+NbdTJNqX5TRtT1ApTPUxBYMdtbi7z/5hQ7xPi3cg=", "0wX8jvy2qkvn8wBqJ4GMiX3KO0V5FE5V/Vx07XCF5hw=", "qNXXNNYqa01Ozx5QOwpEFNfegju5xSD/2dImwHpmmF8=", "R3IsAJKW1SkzCiJRSWNqDC6RnnMlKFqwy+kl6KuY25c=", "mmomVA2shu2myX0P2mwqPLA3+GcCRzBj1MzZbKhGD1I=", "6CxX/q2EJKgMfzrCxgl/M9CSF2Bi+nL7hnxASqJUubg=", "3kdm8KTdoese9bx42OP3vTDHB9bujNYP2g4NWlZ2WU4=", "hvDZ+W7S1Vey3/dIUBSMMwJxwzjTUhJd2LNhxWcc91c=", "UK5yXCVDMDOznQIq9AJo2Y2xoojqJeSH2SjfJyty90U=", "VytctmZoVGXkzEgGG6oIE5lDrTqy57dBb6D85RCbMMQ=", "HN8LUCU7EH6gaHwKyn9DcK2/gmFC8YpkGtix1xv24J4=", "gynUzOeyUkF+SvgesxqHe4py+tvsSQ+ZeW18GxCP+wg=", "m2TTn0GfBpmpidLadJXYY9gilsBaY/W2Sjy3CuBVHpA=", "epgbr/TeV13fT/zGejZYur9lJTKmIF86tCEmtNP4nac=", "e04KTrDl1swxQSkRN9OjjURe3katYXSlRHWDSbBBRCU=", "cBsrMoOR1igHQfpyfuparvM+nn7mVC+Tq7ZnfN1+DEU=", "sx9EyethQwsTe7jgR+oaK+zMiYLN6ed6bCbklVy0jVs=", "uNjiXXzvmrd2HNmgkU/k1ljsVxKP6ipdWDkOWM6vXzQ=", "56A+nQmXwOALieXyw2pymX0AUAIsKf+MZG8galv1mA4=", "Q4u0LBSpNMMMd06+tMVvhetjm9xeyl+6ZRvh7s7XfFI=", "DmhBI9MzNGx2zsURmpWlhOi69C1mUluWEBdP7aGl/Q0=", "a/V/bL6vh+ucRje8LBfzUzV7io4VQonS2OuDpWdC6jI=", "yMmUgmJbqObDWGevNvi2TO4E9sCNTvyiyvpCZO+5v30=", "Z90sJmuPHMmEOhmPXy0pCh8NEiHyvYFqH4KyE2rG37U=", "5Gm8XtZfcJETbpHUJb5H62QCdJSNFcZdI6eIf1womXE=", "gAZQYTBlgOY463e0yK45rJSAsyFCPigX+5akFNuSSgg=", "4qHAbOX6dp7dU5FByJDyVnrdZuw5Bpk8l2Lz59GhlMw=", "TbAHnrCVU4Y9agRFPyc73A60dcCfoEwsD4I/qdmyUm4=", "+SLZifEmKW55s4tzT8oseLDo+iGa8lcrbL8wDmUjH/M=", "kFGH+e/N8Urwj7F4eBjcVHxCRgAbqd+vzczn7lOEwWg=", "TWtGzSwZLCS3jq0/Dl+KqrPR6XyVbNoGWepV/wwlnaQ=", "sqXTLhjBGtU1J7fFaIjTJSmrpam/sF71Ug6wOl/lTLA=", "haVzW1KK5GLl7qSrCT/TW6bNQRa5WssOt0zp0ERCSuI=", "YQrc+JlM+IZTlf+iX6eD1vFVEVVuOZyoRaHjzXGy5IA=", "uFpJZCRa3YpDxJM93zGKOBpaXt7XRKC3aWXwS2xAZao=", "GwYIM4JyyuVYYDprWL9cQTCUZnYTXibY5YMroiTYjKs=", "4aP+PEZMZ4ANsYHnGO1YkG1PMrOkumWKmVQAOZrsuKE=", "AV9TTNWjbJa0siaecj047Z29mz9btZnOoqEiQz7Enlw=", "u5ePn4O/sPUc3kaeZ0Ge3TpYWh5xxI3idwbJI80nj/Q=", "GEPC/ONCW9GnpyXpemKgwCk55mZMeJkWd9lD6fAYyf0=", "T20XUK60GKCMfwshFFAokDHzEca7LxMjNgNWrK3a1Ps=", "cMUDQUbtdSc8aUHtG2Pb9BqnfJRy5BezFVhsDnDYWDE=", "afibiGEXI0UeL3/WRYx0ZYlf1ALZPXLs64kibG7WFfw=", "jk3WwayXE+TGNWvOzd7cFdgXhKhc/SlKQCzizULfL9Y=", "/Syt5dBmiGSfw8YhSizAu3xx5pV0rRO6jxk2H5XovaU=", "953avpU8NX8zMiCwVjwi4VDJImBqtqOE3nsse90vImE=", "WxMl5ia+0hvKbrYPSqZ2BDVNkiQIX0V/jTAHBHlIKMk=", "sOM7rKDujb2srBeF9zlP+kHYJu2h+RZkcrPjqs9+7r8=", "W9ePCOF69+Mka9EMctxlODtgeBQEG1dsZjzTQH1iFB4=", "IhENr426QevFbYOS4TscuKYVJbegNB82ZaDgbkBPYFU=", "GMdfmvXyLE0dr0P1hHyaLAfZLchxOiW1p2WEV1BgAGw=", "VWKulVdXHCCjF6j9v0io2JLMakjJs8RH08iplJ/QK6U=", "6RjzpgByjb09swYGL9KDgoPbgayOxbaZ8gKJ5oifoyo=", "8h7dYGBuugsYQNub4xKI0gMfrGbneykhR8N4qxdWbZA=", "z6TpaVq/eKv1iRsAF1ecqgRJd53437/K7O/TXqkN/lw=", "zqfzaQsOz9XUCG1yeLXw7lNSHYp/Msnr4ytFhS4Tr6E=", "hO5IH+9kHdP3fLN/0IqcgEixpLVoWo0bUVcFqBc87VE=", "fjHFi7ivuG/IQ1Dq1hoVAYT/5gXUvw+06si73wwNp74=", "S2bQSaSHggsPLHEqcQsv34y0qGL278UIIRYz2I07IKs=", "HM99XkOjAF6eS/HTwhofMWqSTQWNVodCOMOHmeuC7B8=", "/VHYniogYAa77rb6Blf6AttFK0X9KDjARSEFdXk0B8M=", "Wmpj2VKdNgJSYdhbS01fK5p++8S121CTEgVB9l14pdk=", "SNIxfM8jPCW34dmvoGJwf+Evdo/BPhnnKUvr/7g/JVI=", "Ks5cgItK4GDuankUe43UX29cRRffY97fb/gLEUPTI9c=", "XZwbMXkD0VPyUgXYbQpJoZJ1mpk1zUFcrcWS2LGoGDI=", "kTH2UUKVB8jex9PrhfjhHKY1ixs894zzcPDZH5rhyX4=", "zTeER2YzeIBK6/5xoKXsEDhmhrE4RzDLP4jaMkxgxhU=", "GCdEkP4U/u3EGuL1G7hDelS3Yz152EqbB2T1Tqo9SAE=", "g1oBUkIrilq0qtr7mpIxI7gLRf5COyGXLz89/ROQySU=", "EiBwQWSRTraWT6uI+9sy7I/2Gan4t/fFkYdJGKyBMiw=", "CV1KEfkRu0FdbfYxORaUUulZF1Wf/Bp5y0xEfcz4Dgk=", "k1bBXa6ubadu4P56COCEj4qo8yuVA04Bed42OOAcGh8=", "x5ZGTtWRAk6TWnzOEbZRRtCXEnkp0qpQCYg54ndhk+U=", "xNjfQQ/tt+frKYG4iuCsitc0CVU/ihLO/jCxYDJY82Q=", "a+sxWWY5ibOCwB4Kxm2iiDtPodtaYrPD5V1cz/eQDnw=", "Xo+PLHo80B4RDEScHietxgZm06mm2SqI5fCkYC/MJF4=", "lcj+Y6nAPUsOEvQPrfDz6yyXj1287qBmHKQbM4nWxcA=", "s+wLPy/hdn/7pDsZmWC7+mB6eppH2+UCGCKuFEXdfic=", "uHPKJYF7YXirMGRU4BECs/gtx9M4IoPPNscpgQhSTf0=", "Txi+g9LCDG6BwZ7ltk/0zZIS4zCr2l0qy64Cngee+N4=", "NwJBGP7xbeZ1Jc6kcIgjY2i4uB2W1dyppMLMbtWEkl8=", "F5QnK3KfHhsZuPcPIy4wQ1DciTmCHRBL+IoqxeZM1po=", "COi3L+Q8XwwzFVtIMvbdnisIEUSdE0Zd/hvJj78Ai3w=", "46gAaX55GHPFx/5iSsaO8WyHg7F7uitptK0HNvgAl00=", "/7Ggm0UlqSLzA4TBoF9lY9QaHQQmWgEDkbbS/iB142s=", "Bm7m3oOftn7lQQjno5qD0VhiSUOUO7/ZwRD4kxWKvQo=", "12NsLuvFwR/Q9mK0OshZkFJ3Jj0vf1JnhehXJDEQMOA=", "+LY3xzBBGmW9Zeo6pw05Do+PRxMf5UCZbLUEevz1Ljw=", "LGri2WSbIMlx3OqK2NXCkgY3oOXG1TzHSHab7PHXeAc=", "0KPp+HItAJf1oUlBOR/7fALCU41PrxcbJKjy/MLz3cc=", "viHCgNACPnM8OhHlSGjB8pNnA+mI+/vy++j7eCTUXOA=", "bA/VtOHb7OXyukLAwYIayVCHEpsHyOD0j9zu5RSC+9Y=", "X9Qjqf/Tx0uY00rkMB6N6y/TbO6HvMX1SrXS7zAVLes=", "fFd0Dc23eZmLN6gUpr4KuHa6BkjkbqvjR+78KNmGNsM=", "hSlmRVB096g04HKuxvIxgJyOXaGkUN9nN0HPD05ME50=", "Ny3HxALMaO5Ah1OtsxKZFIOX+ZbJiEDShzRWfWSuyeA=", "Akrjk0/OfvC+NB8Z26s+LT5cpaXlRrohgSazGZyz2tc=", "kSfmaULKXNHOYoF7t6cgOIrHw1YZ7RlPzOZsOpxp3Vo=", "+oSCI+4LSBTPwvt4uiIfMXzXrf3kZ9+SSrYUJrbuCUc=", "gydkvK33J+M8vkwYkO8LJPaAGDpj7GZVkYISTVe6FxY=", "32m54c1TThYZMTHnUscvO8W4gxExGFM6xn8AqMcFtfQ=", "fa7Ww7QibKCj9lm9DNje449P7wzH4OyQHTdo5fPxG10=", "DDs9JxyFiYH7vHOQx2En8eVpYBVqaYc7n3898UcFE0U=", "Zs8Il33Mba0U5zTM+PaDCzilS93DJbSElaEBMhHbG4U=", "pa26SaSOJx1rjWPZbM7zbWp9sZDa2/FbJvkQYm9uF/k=", "xhyX9JTS4zEbh3ZhUxvVSffhzeg8weZSta/c9fmcnck=", "JDgoJ8Y82uQh7aQOVligdCz1J4jCh6DpcsCNgsOETXk=", "leqIpWvbAB0b5knJrPUwKF2QjtxaoZsAEea4joJwdmQ=", "b0ehLnJZgdIcCiF38M3fpXRi2bUihIt1iZDUwSSez00=", "Fpd+3vewWxPrkUss8kBb9m8GVSX01LQuRJF+Q0KEzyI=", "GycsDipbShJQhznFRTi4iiA4UMTUUGA9SnGK6DtfoF4=", "oTGDbuY746DRtYFaRa88ABBsZQU83d8PE5rNBloxcSw=", "ASqlNohfcLJSmRSphleUa7g6V+ZHSmgRetNN1HB3MPs=", "IGdXU17J3To1XDQcbY74YJq12bbjHE99OUIcAjIjvoY=", "jtvQUtyYlbJ6r9ZKwguptC+NVY0EYBGt4X21E2OHXp4=", "FPKOoJONl2BsFwdhm7GCtEUUiMesulRuHU/62Iz3zFc=", "FlUaN11EtR3P/reBa3Dw7dBIFJeox3zBP+ufcFelw58=", "rF6K7l5gNDJNzfcMP0NkWEIDLzzj025AKiiL+/x5Xj8=", "zVtbMMv+livcidEn3Pkg6IGb5vggFWfePr84wCi7zYc=", "3p91K2YH6AdBrMAoLuVBcYUlCg8Zye5JRF6ue0qJX4s=", "mgnDwptjAW1DO/EpxYicyqXpNkIEE2qIfff/YgpOt34=", "piC0AwpI7/pbuxPIzhAS3ro6TbWPhsezkhV/luEEsPk=", "/2WL8AMw5j9fzW3Qy6S4GM/d2yoIKVR9Y+vjyjDmywY=", "j7Wt/uiRxqL8A69kqtCxsg1EuYbB32UTKnSMt4q824U=", "Jb3upwCgUfZ+QwMT1w3TpxrQlDzxRGk6xSsUVrgNWCA=", "AgmYkv4g8BwMBXw4AHzA/iQyaS6ejI3ziHwtXewUqA8=", "IemvcaxmqSL1Q2qbO6lE/svWm3L168OXWi+paCmnJIQ=", "W1k8Kj6p7+QWvvb6f5EStq0eCNhzwkhEstSTAyytebA=", "oVtKGT3NMFd1tAudD3cQUZ12/bqM1w9hUjMj0hBu53U=", "BjfpOO83/koMEu803xttd347UxYvxuDD9Jr1cJIRmYg=", "5rQB6b8risEzE+L82h6MRjOI25hSkSVqJfQdtnF1zwo=", "oYw3Gb+0E7r/UCEbUhq3C9Xq2DRgZe+wioqI16gDakg=", "guUN9k/uYpFl0tN9VCZYy0m7qVVZeaxFa9imNUAoeDc=", "hR+5SJIYS24mdic2DUfxjKWhNIC8cGhwFbaUjUcHSjA=", "Ji6JoZ1rFPmvB8UUvfXh4i7OoKP/r66re+m6PZA75D8=", "I195yGkLt/zosOA5koV0QB7hwpgDe7pUGqmjwGDxkSg=", "oEQObTrvb/68dIgQsHXAHTiCgj2f9jOsR+HXYtGGE00=", "nXQI0tQpwbjH3DtlCBtt1A15NwQSPLXacBu7kKkca3g=", "lmHICGOasYSyiFV/LfBJ4NTjUqnvH5decDqN7FeXqT8=", "vneSpUY6a1eExH0GBxKC0rOE4s76SaXG4gcdthf9L7c=", "vMbC8mPhPfOYekQtWmVw6KVYIv6ehpMnSx6Wl4Wk24c=", "Q99B1wBpxlWYguOpojK62xjv8ORLHj6O7b620bMDGCU=", "p1moZYaQplrAi7/6jlABqA1THO47ED2DL/+S5YMPVgI=", "drA2lsSZq8iIevQQzdJFuKGG8svALkeusTQ77yvvnQU=", "ODDO0paFFH6snzWdrdKKY6GziIRxYRJ8YElsaJ8WYl4=", "z9KK6nRA4Nx10EMFR/jNfKlh/2HbMZMflXsPR/sJMX0=", "I5c1cD6Rph0XzQiDI7Vv2sPJOWFmW9YsDQCzkQ8eBk4=", "/BKNYVq5/XvFLSZfp6ikE7uMbkO2ragv75WeXzlmMJc=", "/2o4UaMtb8R7ef+5Ay8kLnWHNRcw33jnEgPSsklVSNg=", "UXVjadJLAD/DdiFGQ/aopW3VhnIRb7gK17UQl2vrrtI=", "gRuxNLDnQCWMDVWKZz7HRhV8UuZOUch14XT6C7PO4cM=", "pa+s7qiVjdkfwp/6nvtdOGcplY9qgiW/K656Cs3xyG0=", "w1ReQo6ybMWnUw0aOoEqeCfYVYPwknwEi8LZGSYm8UI=", "Zcud9DumH2hN5tbSef8ZOKThjRunr0gCxVWIRBuZ3cY=", "arEHBNmYJX/LjkU0hE4UX+UE1QcUaLpdwwN13Gbr6rw=", "CM0E8yjgM9F9gXN+vEwl90WTZ/PVOTa5OuNKGEfclpw=", "nDuIcyvwzpExZKfVw8lnWansAK8HBqd0z0OHijd/2BQ=", "f4UUj9e5XZkKaSJipghr88auOWTTyn9pKVxkusYDXwU=", "3erY9CrH3GcS5SNAad7C+4D/zoiRoF+tTBRbsrTiyv8=", "SUP3eJKjeHNzgM815p1/j+FV9Vw20lFuR28R3BG7UUQ=", "GiMwU7tnGWbNWHj4ctw5nIHHR7CxIO3LKf+KBwwycC0=", "Ny9s8gTfuBbgV+mWwJy55LM23TqBfWsiZPsLutkNjnY=", "V7jEHpG9QfCy6FnrTL1Pw6/OT8wkcpHHuqUxKaW8kBY=", "sdLJZa7FShotFG418dhbN4Jy5fPwD7xpQRKHVeIIWio=", "PfyQNqEDJWeUduhvSARVVUiOr3PGvbKWUBFnQoAsVbY=", "CGniF0ullgBW8hjAZTj0qyEU2a8ZOSoqJ0046RULF5A=", "4FR7XHzh0Q/dqyYyjD0Gbi1MVmxMavk/jEbL0xLUYB0=", "jLYB/cN+qyjStBJDeR1fZ5A4NLsN6uUvw4870MkkczA=", "3OTyvHevk+rtQtKZpl4+rxS9nJuSh5OaFoo04qK0cdI=", "8+tnTnJ7RkOLAQ0B3oiSZo8MKiMpjKUqblk2cG7+YCE=", "LxC8ycnNy5TrbDzMyP4OReRXPVRNPUCJIgSmUegqI3s=", "A75LqydAP3mKiwYajXLaA4jBlr6x5T8ajfLHvyyXCsU=", "Uy1DCH+lP1Cu/H05ahudfqrUX6nkh2ZiSmaYhiQJ7y0=", "RNS3IY7D7wnBrI0JiFHIoBXTSKCxqXfDr+bYieYiTFA=", "CBMPYqV+sDC+6r+1B5pQRRWeyCkZ2h6+sEkxxsbHpSA=", "Z9jCh61t8IgmWlqRdy+NyAmHyK6U0q/vgpk6oKiQsS4=", "dmuS6CItSDhVI/VogMWeguncmcIojBYMUwNlX0zP7LU=", "smicuEfoNjHjXCdySwmiRTtyv0Da7GyQ4ALmshV0BHE=", "cJhc3TvkKzGVkLyvfrizXftHrxU/xsoi+8FnB/a97fw=", "lsmnJlqOzV9o4ghptE5SJw/TOkiQ/Dy07qjbpFC6Gvw=", "uIBY+D5ApIjAvPkfH3YkJWlZv4LITI3XunMr83qanDU=", "xTyP4PxGYlbNM0AgCl++C8Y8VOt6CqJ0rsWNsrGUKiE=", "/zsYxBjeUTW6GCS/gJxHtKCao8hiwSCIFrqzQMl8T8I=", "HK7tNU4L2TozAGN+es5Dbid1BLiWAHPNBZuzerBpXRY=", "fsaSZY4Rqp7/K5wBxutNO5skClP2pNvHE8FdZGQvNqQ=", "R0TwJxQbx+T0LpnLC7xXkJLRoBkJk9YFuD0FDB5O/9c=", "6+SImCeGYN/R21UI3TEBfijUBoFv6/1iGHsfE9zKoZY=", "Yo3nDZ96GX5DCzEHqnZdVm0VQCFSAqDpk3WtMr3OY5k=", "yMRDDZxQuiwf9FswTe6P99c0xw/VHQNoAoKBnILyQh8=", "SeMpf69fs1/0+Yn6nriEONsiCt96wRsulXZpxbMZ/YI=", "+IAASAnMKRFMEP73va3xYKaSVjdB/Yx1e+5d/m4bsH8=", "n2uKpDmwJEgt+fvE4slIV/XY+73lWJPIQKqYhuFBtEM=", "MBhA0MX+v0JzLSPVU9RX2C4HgqFqjDa1481NXwLFJCM=", "rKV/tFraK24l88Fx+MP+U7/aa1OudmZ8+69XG0CgAns=", "P4XohDJ/3bhPQhseCvmanl3uUA3hbfHn33D+zWE1yO4=", "bXIWatQYfa+TlVq7Nw/2O4aKYC+UFB49CZ/MMg9dSh8=", "XPd9yYh/C/lPHmkXNxSYpaclxpJsuNkXO3eVFdxOCLE=", "llYdYNvGDr7IuR4ipyiuwQo3nVXK0xPpBI5Nj6vdncI=", "T2mhiHtHlmWOTG4ySvB+r9b+o5mhS0bp8vMorUGUq8s=", "KRUkMUqNSUHI2xsZyUsRJjvYtgPSKb1fGQjghRlj7oY=", "R+cS7SzVMJBSvU8S8WwYTtB+LppnAuj5h2xBfcIHSwc=", "tijtS6DdndBVRWY7CDhzHdGenmFc408ti4uXAShg2uA=", "u96P9p1YST0M3NXV++JMVCQwUuA9IyAbq/+0/N+aA4o=", "iPP9cVroRHuv9z2U/JwRcaU6UAyFq04+AIXlZ9NL53I=", "p1vvE0dCVj50gPbWunsWD25e0PM4cI7M2COOSPHZsJg=", "QMuElbyloH84teWNCmiLiuTuihUJHANQvV9VGqnOWGw=", "OtSNH+V4FSgSJGXGZkbjprXLvWdOmjGVDOHfiyvVF5g=", "JBuX8VbnZwah9i1H2eTxcvc7medhGxzZ/vV6Z+y7ekI=", "tX2ZFzU1NQrL5KAOlSUreJ0NbS3GNa0EddV8qaaUCKw=", "IycPHIDseMlqfBWWh/2JvFkJymEAQ3rH6b4UEdFgbLQ=", "SA2eFZbHmONN85/zm5gDK3/hfFtNSUltBcbDalUoHOw=", "tLM9nNpA0IR60B7dEVIjreJtXlQtceDrulK5e+1WJ4U=", "07PpuyqN8YYZ/RQe+RsLChevLU7VV4SEQai+EgwJgXY=", "HekucyI8Lpj58ye5GYyR0JtQRfehiPzC2RAr6KVtbyY=", "dw4m3gR3z+BkKvA67e/S7Vjs64J+6OqJj9riunUosEA=", "GvDge+wgCBqZ0bTGsGaSPloojTXaQkq2/8j76eXmrp8=", "g7phg/DAS6+MOcc253nYWetidKtsGpPPiTd0u3+BgF0=", "8Ylx0gm34XGLwfoTA3zhTt84mZFdGBphN/h1FC0WOkA=", "gvww0pxIDjhdfLFAAd2c//yOQVhXEpISG7WCAxPHQLA=", "3s5qVmrHDWdlsz5AryYLo2aNo/jbIvun6F9Wqi3ae44=", "gf9xeWOihcbLGUGTGLzSGWc2UC8POLtLpP3WWsHQHuA=", "8OJ4ZMhEmPNyE59uufx99FqUWC+bjs2qchxoqQPtCYs=", "CCkOtVN/jqLVeqPwnhShNatFzKzpLpoROZBsNgK+g/Q=", "QXNP0YOfmKeTQHiHwJnEOqCtL0lNbdvnBxVNWfQx7mM=", "WNZk0Kj19Clf38t89QzSEssoVPyHw4/X4JDE9rP3wSA=", "6C9PeI4lVWk/N11CCRdcjeQxhfea4IAuygV+Fl1zigY=", "yNZSLTpYNwCCd0xSb/WUKABPhxyu2XUz7SenH7epBR8=", "POEYGns4yi23smPNe3aeDAupJbRChBHvwhIXt2CLcY8=", "YXtBB8kDt0j/SacREOeebCF4zD+dJMhtNGHNpaApViA=", "8AYtDHMiO4c6DrN1Q15LGd9whDeyBUNrtJiJ4nm1d4k=", "IXgW+EzOawAVmZgrZPNaPt//u+YRpWkcStIwecr5z24=", "lfONajwS8qyPYYhr9tnLpvk9hx7BjoNT2XFbGGep6J4=", "P4S3jABjG94isim3A8UrjNsmYz71KyPDRmRgpiMa4xo=", "Vmq7SS3omqv56Q9m6CfGPNA+Fs+IALEjP+4WQF7/bl4=", "zTUjnHOaBbnKsrMhd2E0V6qzWLtRY/6Ix1bvHyy4+ok=", "Fc7lMx/TubvFTsZMTYVbv9paIBVW3DmDWqnElpngWnc=", "Vo88GI4jQ5zGBGvLPFPJzU5sF2UMJX17UAdO9d/9pQ0=", "cgSgjh/4QRRYM1X0Yl+ayqiLG8gfCGbl0YbvQv24I0U=", "R8PdHylNExoyGNkBOhuBXyqgOKU5kW1nhP9s4q8rmVU=", "o81or9snCI6xoLmPX2s0d1Ib6bvAW3RYr3mUZ+HC87M=", "GlIuLk7vsFEXf8mnbd3/keCqCwSucnhPjUr/g10/lmE=", "xSVa5pDOP8Fufcz6S0dNUb+wMV3Tc0gr8MioNDgdvJU=", "oElTycyt/w3XHCs8nzgKKdrKxpvGdmbmYAzXNmB5gxk="], "CachedAssets": {"Os30ozOUKjAlYdvE6BTHoLQXirr6Ji3xgcF9/cGDFAI=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\tzxjg6is5z-au1zu6ifz9.gz", "SourceId": "MudBlazor", "SourceType": "Package", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.8.0\\staticwebassets\\MudBlazor.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6z1h9rw0aq", "Integrity": "Qhkmb/tdD68/rTyDHgtk2X0Mct/RmabnrG4NRBGJoTY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.8.0\\staticwebassets\\MudBlazor.min.css", "FileLength": 65094, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "Fkng+0tbNZXLhXNYQtAhHvdsvJhXmh428kk19FPiwsQ=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\0wz98yz2xy-b8x8f7e52z.gz", "SourceId": "MudBlazor", "SourceType": "Package", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.8.0\\staticwebassets\\MudBlazor.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5rcl324siu", "Integrity": "L2HJaFxT6cjl2vWPEqZI31uHtg9IympiWZsPogeicXE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.8.0\\staticwebassets\\MudBlazor.min.js", "FileLength": 15475, "LastWriteTime": "2025-06-22T09:51:19.7937449+00:00"}, "T+fv+gD1UxHtr6TuS2Js+caJUyoexKS7oF2n25aHVWs=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\gvea8o5ayi-8rbvw3on5j.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint=8rbvw3on5j}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\css\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ri8oomj080", "Integrity": "3V6n6GUWrXibPBz7fhqi3nKUDtOkIOblRDVtLs9wCh4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\css\\app.css", "FileLength": 2031, "LastWriteTime": "2025-06-22T09:51:19.7937449+00:00"}, "UGFbeKsy/Sv1wAd50Fce7sP3jtGYY0z2phFG5tk0Wg0=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\pedj6js7rk-r9rtk0r04t.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint=r9rtk0r04t}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2trpyr3b55", "Integrity": "Bve7XO9MjK14d+byEKcQ+Jd+f5CxzYW31KsKqx0Mzl8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\index.html", "FileLength": 1532, "LastWriteTime": "2025-06-23T10:09:11.403967+00:00"}, "8SOcqg38vT31sXxf3zwcQv2gS5LDW6V6YVBxOcj+5PY=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\pkrlppxero-bqjiyaj88i.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "It12qmT6GpYSEyYaOZ8XdKlQ5hBMEJwbxad91h12f/k=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\mozzebg29h-c2jlpeoesf.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "EUgMHicjUIznsNd9pKqgS3XM166PMxj04F0GKKBwoCo=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ypv0x2z36g-erw9l3u2r3.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "un0ojZC4Hs8fCgwz3L4+4x05W4n+wYFjgEZIl2v+FMg=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\308uiji20w-aexeepp0ev.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-06-22T09:51:19.8249957+00:00"}, "OBauY+a9/8jN4cN7XfXsan+BAcab2VqD/sp0Sh7kXfA=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\w6br6pgqsq-d7shbmvgxk.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-06-22T09:51:19.8249957+00:00"}, "V/Ep5M8hiLnsTs8D36sJFNUxcgfDb+HEPX6Kizto/EQ=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\hgocfi3yyg-ausgxo2sd3.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "aIOqmuyRAXuSazjqhxA36BkflbuzpYzSuq+kVjwvHks=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\4wtrc6f4dx-k8d9w2qqmf.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "o6aAMbkfPb1r8YeqGp6UVu+u+gKNATcKaS6XSHbMR4o=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\e71c4gxx5a-cosvhxvwiu.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "kyrXYmfBcly6hGzk5U5Uqb82o7zjHHrdgSlefqR54yc=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\6axn0ny9bk-ub07r2b239.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "QK7/LqChV27XxmyOltIHM4iTcjn0X/XPlldNl3yLVq8=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\4ai3x35njl-fvhpjtyr6v.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-06-22T09:51:19.8562463+00:00"}, "hq0vlecwlhpyAsQ/q/aQzB0wQFy1R/G1lH5jvNL7hWQ=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\4j6jw32ruj-b7pk76d08c.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-06-22T09:51:19.8562463+00:00"}, "uJMIVwIIh7sU6GKVcNkFhdRbDUDdrc22tm+bPnBUoHI=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\drhhamuxfi-fsbi9cje9m.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-06-22T09:51:19.8562463+00:00"}, "E4nGmdo0Grs/dtT4ZKz4sUIQ5dJn5Ru8jpjjsGpBNeM=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\n3e2fc27c5-rzd6atqjts.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-06-22T09:51:19.8562463+00:00"}, "2Nn6ZGTBvXFFuKFtMbOh3vq/vxwJKm3kJGLilKzdprk=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\e2xp6ck32p-ee0r1s7dh0.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-06-22T09:51:19.8562463+00:00"}, "+70FsqZWqDa4fpg8i7qlvsB1BJoLAStAZ5Ksjsag1EQ=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\0glb43rxjj-dxx9fxp4il.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-06-22T09:51:19.8562463+00:00"}, "B/csOmgu+os2A37r0JpgD1DPV0AZIdjeLtlgfpj7mTI=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\qbege0tfrh-jd9uben2k1.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "LbneNPQ/J4I2zMm4bzYzF8Ao7TMJW4w7hDisTIB5hps=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\17n5vvvhyw-khv3u5hwcm.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "Ki+NbdTJNqX5TRtT1ApTPUxBYMdtbi7z/5hQ7xPi3cg=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\nx75zsy2d3-r4e9w2rdcm.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "0wX8jvy2qkvn8wBqJ4GMiX3KO0V5FE5V/Vx07XCF5hw=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\jb9z4ej1p7-lcd1t2u6c8.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "qNXXNNYqa01Ozx5QOwpEFNfegju5xSD/2dImwHpmmF8=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\zuwccb0bud-c2oey78nd0.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-06-22T09:51:19.887496+00:00"}, "R3IsAJKW1SkzCiJRSWNqDC6RnnMlKFqwy+kl6KuY25c=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\u4x2ymkaf1-tdbxkamptv.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-06-22T09:51:19.887496+00:00"}, "mmomVA2shu2myX0P2mwqPLA3+GcCRzBj1MzZbKhGD1I=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\rq51h9zu4u-j5mq2jizvt.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "6CxX/q2EJKgMfzrCxgl/M9CSF2Bi+nL7hnxASqJUubg=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\s1an1alosy-06098lyss8.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "3kdm8KTdoese9bx42OP3vTDHB9bujNYP2g4NWlZ2WU4=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\mc608celym-nvvlpmu67g.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "hvDZ+W7S1Vey3/dIUBSMMwJxwzjTUhJd2LNhxWcc91c=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\4rsjzjn3pv-s35ty4nyc5.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-06-22T09:51:19.9343738+00:00"}, "UK5yXCVDMDOznQIq9AJo2Y2xoojqJeSH2SjfJyty90U=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\48zxvtlmkd-pj5nd1wqec.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-06-22T09:51:19.9499992+00:00"}, "VytctmZoVGXkzEgGG6oIE5lDrTqy57dBb6D85RCbMMQ=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\sep0zbrkwk-46ein0sx1k.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-06-22T09:51:19.9499992+00:00"}, "HN8LUCU7EH6gaHwKyn9DcK2/gmFC8YpkGtix1xv24J4=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\wufgq07dkb-v0zj4ognzu.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-06-22T09:51:19.9656248+00:00"}, "gynUzOeyUkF+SvgesxqHe4py+tvsSQ+ZeW18GxCP+wg=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ud3udwn460-37tfw0ft22.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "m2TTn0GfBpmpidLadJXYY9gilsBaY/W2Sjy3CuBVHpA=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\i4ycg8hsoy-hrwsygsryq.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "epgbr/TeV13fT/zGejZYur9lJTKmIF86tCEmtNP4nac=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\mrho5uk1dg-pk9g2wxc8p.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "e04KTrDl1swxQSkRN9OjjURe3katYXSlRHWDSbBBRCU=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ztz9b8zmne-ft3s53vfgj.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-06-22T09:51:19.8249957+00:00"}, "cBsrMoOR1igHQfpyfuparvM+nn7mVC+Tq7ZnfN1+DEU=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\qoozf79qw1-wm25hb1crz.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=wm25hb1crz}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ju1bbmp7qm", "Integrity": "GzFKIcNnv8MH0aRfufUG7b1upLIPhtqAcC8vGJBPlmQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44357, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "sx9EyethQwsTe7jgR+oaK+zMiYLN6ed6bCbklVy0jVs=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\blxozgl06o-9i1hoof2cg.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=9i1hoof2cg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zoveoziu1o", "Integrity": "87sxCkbmGewOKQoPArOTEUMgt6Lub+jw8vCThi8nbEo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92054, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "uNjiXXzvmrd2HNmgkU/k1ljsVxKP6ipdWDkOWM6vXzQ=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\620b52je60-493y06b0oq.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-06-22T09:51:19.8562463+00:00"}, "56A+nQmXwOALieXyw2pymX0AUAIsKf+MZG8galv1mA4=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\nrbdr8ikve-0nj8p7ttgy.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=0nj8p7ttgy}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4qbfgzrp0o", "Integrity": "5KrrFMF05PWkdCZm1mJUwV9mzFVlzlEsvqrsw/FVxA4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86959, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "Q4u0LBSpNMMMd06+tMVvhetjm9xeyl+6ZRvh7s7XfFI=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\o4bwk4zsoe-fy2gvia3cj.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=fy2gvia3cj}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zf8ie22w3i", "Integrity": "wc6lrnvdUA3A+m814StkpxDAxyVZ7IE5UySHZs/siUQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28856, "LastWriteTime": "2025-06-22T09:51:19.8562463+00:00"}, "DmhBI9MzNGx2zsURmpWlhOi69C1mUluWEBdP7aGl/Q0=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\jjcy7qpafr-c97sm6mace.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=c97sm6mace}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "msk5yfapgv", "Integrity": "htah18zIb+YdUJyOBX3hzGqiwRFcYU6ttE5pJZ+ZHts=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64127, "LastWriteTime": "2025-06-22T09:51:19.8562463+00:00"}, "a/V/bL6vh+ucRje8LBfzUzV7io4VQonS2OuDpWdC6jI=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\vpeqkkohp4-jj8uyg4cgr.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "yMmUgmJbqObDWGevNvi2TO4E9sCNTvyiyvpCZO+5v30=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\xebldw98hj-s347mgww9m.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=s347mgww9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5pfhykvkvo", "Integrity": "R3SIdpqL8y6PiSGebskjzI2caNwrefpULuBDQo4LK3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56671, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "Z90sJmuPHMmEOhmPXy0pCh8NEiHyvYFqH4KyE2rG37U=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\e80p743m1k-5plt2zstor.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=5plt2zstor}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c4tpossgrn", "Integrity": "VNgrnoYI31jrKT05yfbH2PJgq98+SOhYXJJ30IeU5pE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29572, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "5Gm8XtZfcJETbpHUJb5H62QCdJSNFcZdI6eIf1womXE=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\d7ilm089y9-6gi56h49eb.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=6gi56h49eb}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3qjzwiwgdc", "Integrity": "m/bf83aWBgBKX3XTrg7tjFwXfzK08+CGtZ01fhafObw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64429, "LastWriteTime": "2025-06-22T09:51:19.887496+00:00"}, "gAZQYTBlgOY463e0yK45rJSAsyFCPigX+5akFNuSSgg=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\2l892qe3oo-63fj8s7r0e.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-06-22T09:51:19.887496+00:00"}, "4qHAbOX6dp7dU5FByJDyVnrdZuw5Bpk8l2Lz59GhlMw=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\d671g2e4t6-icnra8k7s3.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=icnra8k7s3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n26y8fseyu", "Integrity": "wchDVQg+aG34icaSimlC8wBWG3abJ4xV8qAofPysJ/I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55851, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "TbAHnrCVU4Y9agRFPyc73A60dcCfoEwsD4I/qdmyUm4=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\q9qu3cm8pu-iag0ou56lh.gz", "SourceId": "Baytara.Client", "SourceType": "Discovered", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "sample-data/weather#[.{fingerprint=iag0ou56lh}]?.json.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\wwwroot\\sample-data\\weather.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4cbitfuojg", "Integrity": "HD3vAUwurZXW96vdgG5RVLhMjmVeSgBs4iKLSw2+Uwk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\wwwroot\\sample-data\\weather.json", "FileLength": 153, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "+SLZifEmKW55s4tzT8oseLDo+iGa8lcrbL8wDmUjH/M=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\822nxsd125-md9yvkcqlf.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/blazor.webassembly.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.webassembly.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k1uijd3xue", "Integrity": "aODHHN99ZO6xBdRfQYKCKDEdMXpBUI2D7x+JOLJt8MQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.webassembly.js", "FileLength": 18128, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "kFGH+e/N8Urwj7F4eBjcVHxCRgAbqd+vzczn7lOEwWg=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\kz1w4pi8zk-tki07pftty.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "Baytara.Client#[.{fingerprint=tki07pftty}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Baytara.Client.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9u6cbnf9yp", "Integrity": "X3tAZCru3ZXEgDBVx5T+ralRDclYylFQwPeoffx+qIs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Baytara.Client.styles.css", "FileLength": 1398, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "TWtGzSwZLCS3jq0/Dl+KqrPR6XyVbNoGWepV/wwlnaQ=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\wtdtswkgzr-tki07pftty.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "Baytara.Client#[.{fingerprint=tki07pftty}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Baytara.Client.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9u6cbnf9yp", "Integrity": "X3tAZCru3ZXEgDBVx5T+ralRDclYylFQwPeoffx+qIs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Baytara.Client.bundle.scp.css", "FileLength": 1398, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "sqXTLhjBGtU1J7fFaIjTJSmrpam/sF71Ug6wOl/lTLA=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\c7ik5zb56r-phaa9r44xv.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Authorization#[.{fingerprint=phaa9r44xv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Authorization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h1wbebuwn2", "Integrity": "C88wh6eEqbi8Hoc4PKhx8UNyomO12poDTt/rDLYEYJQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Authorization.wasm", "FileLength": 18067, "LastWriteTime": "2025-06-22T09:51:19.887496+00:00"}, "haVzW1KK5GLl7qSrCT/TW6bNQRa5WssOt0zp0ERCSuI=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\t25r49r91d-4o2vz6uw5j.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components#[.{fingerprint=4o2vz6uw5j}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4y0l43e5dt", "Integrity": "9Hev8/0eZcmh+of1wNG8/+TTG0MFt51/61jxeiQnYxw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.wasm", "FileLength": 135110, "LastWriteTime": "2025-06-22T09:51:19.887496+00:00"}, "YQrc+JlM+IZTlf+iX6eD1vFVEVVuOZyoRaHjzXGy5IA=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\9l6aj725io-1ddspp16i2.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Forms#[.{fingerprint=1ddspp16i2}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Forms.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0gn2vv6660", "Integrity": "MB6sOe2NWpkRiDSZzxAVoMX/X6NV1/eMJOS66g5Q8Vs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Forms.wasm", "FileLength": 16717, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "uFpJZCRa3YpDxJM93zGKOBpaXt7XRKC3aWXwS2xAZao=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\px7xpcse41-wjexe30cog.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Web#[.{fingerprint=wjexe30cog}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pgta27qa2t", "Integrity": "qZEhkf9TdTZW+klN9B+tuKckh1JvV2CZfAiGQxjg3/8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Web.wasm", "FileLength": 72582, "LastWriteTime": "2025-06-22T09:51:19.887496+00:00"}, "GwYIM4JyyuVYYDprWL9cQTCUZnYTXibY5YMroiTYjKs=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\e473lqm2zu-2yt2k81j3x.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.WebAssembly#[.{fingerprint=2yt2k81j3x}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w0mar6tbe4", "Integrity": "AA8FXYqBKD+AeJHcD6nsSs9niqApfhrdTTJMxYziv7k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.wasm", "FileLength": 67505, "LastWriteTime": "2025-06-22T09:51:19.9343738+00:00"}, "4aP+PEZMZ4ANsYHnGO1YkG1PMrOkumWKmVQAOZrsuKE=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\0ivkd7hswk-4eagaotj1c.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Metadata#[.{fingerprint=4eagaotj1c}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Metadata.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r4wlhab6a5", "Integrity": "6952h5QnELAnl07RYQ5YcHd3bmur48JI0qD03I89Iv4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Metadata.wasm", "FileLength": 2425, "LastWriteTime": "2025-06-22T09:51:19.9343738+00:00"}, "AV9TTNWjbJa0siaecj047Z29mz9btZnOoqEiQz7Enlw=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\4mwk0gdzmo-a20cmtwj3w.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration#[.{fingerprint=a20cmtwj3w}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xn1jd8qxqc", "Integrity": "00P47iSCyJDIt/4Q/ncEuYfTma+v/DXQfcVMtOYdy7U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.wasm", "FileLength": 15913, "LastWriteTime": "2025-06-22T09:51:19.9343738+00:00"}, "u5ePn4O/sPUc3kaeZ0Ge3TpYWh5xxI3idwbJI80nj/Q=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\02wz4v1k9d-jdjwdbrxb5.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Abstractions#[.{fingerprint=jdjwdbrxb5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y1skf6eqlh", "Integrity": "+o0SlaGN3hTshQrxRJg4gpDLcOm8yCqtvFUtEKt14VE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Abstractions.wasm", "FileLength": 8462, "LastWriteTime": "2025-06-22T09:51:19.9343738+00:00"}, "GEPC/ONCW9GnpyXpemKgwCk55mZMeJkWd9lD6fAYyf0=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\0pj57sw62y-y7ybdi8i13.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Binder#[.{fingerprint=y7ybdi8i13}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Binder.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l67n2vaw3g", "Integrity": "473QSC4QGff/9qmMp6ZYUUo1aOQ2m37xzR9E3A9VxWs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Binder.wasm", "FileLength": 14894, "LastWriteTime": "2025-06-22T09:51:19.9343738+00:00"}, "T20XUK60GKCMfwshFFAokDHzEca7LxMjNgNWrK3a1Ps=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\hajxju3w56-6zj77w12m9.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.FileExtensions#[.{fingerprint=6zj77w12m9}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.FileExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i0s2268p4w", "Integrity": "UtSFma7fZr8jBKW9dSWVRUlV2meJputzxAk6Z/T38/M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.FileExtensions.wasm", "FileLength": 8399, "LastWriteTime": "2025-06-22T09:51:19.9499992+00:00"}, "cMUDQUbtdSc8aUHtG2Pb9BqnfJRy5BezFVhsDnDYWDE=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\q7wkfgcy3s-rzh7ctjkaz.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Json#[.{fingerprint=rzh7ctjkaz}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sptztho42y", "Integrity": "0N3N00Tn83K8Oz80rQAS7KB9FAf98Swlt+wjujKkB9o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Json.wasm", "FileLength": 8205, "LastWriteTime": "2025-06-22T09:51:19.9499992+00:00"}, "afibiGEXI0UeL3/WRYx0ZYlf1ALZPXLs64kibG7WFfw=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\cfr88ro2af-tlmqx4gkln.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection#[.{fingerprint=tlmqx4gkln}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zc0d7aia9q", "Integrity": "7U1a0MfPd/PmjfgVLxZ1ocGNsMOKIIBWFaREtGFd24k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.wasm", "FileLength": 36323, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "jk3WwayXE+TGNWvOzd7cFdgXhKhc/SlKQCzizULfL9Y=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ivbghkyzlz-lcrc3gl2ab.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection.Abstractions#[.{fingerprint=lcrc3gl2ab}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qm4hkrzgij", "Integrity": "skE9zViT62EMSHppqKIfhF8Occ2aB9yF6wSoNFNXCkc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "FileLength": 21988, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "/Syt5dBmiGSfw8YhSizAu3xx5pV0rRO6jxk2H5XovaU=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\2dgjtg30ge-c6bzkprovy.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Diagnostics#[.{fingerprint=c6bzkprovy}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Diagnostics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ciq6qzdmsk", "Integrity": "tOEcCOdhv6oB+ZgA8FCZkoL6C7TGdKZMYsZr27B8bdI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Diagnostics.wasm", "FileLength": 12693, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "953avpU8NX8zMiCwVjwi4VDJImBqtqOE3nsse90vImE=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\am9bjpp4pm-73wjgm467y.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Diagnostics.Abstractions#[.{fingerprint=73wjgm467y}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Diagnostics.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y0yi0hl72w", "Integrity": "Po978iRdnsW5M2dQPQZSO2rKZaK5Lwu6fW4bFpTC5wU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Diagnostics.Abstractions.wasm", "FileLength": 8976, "LastWriteTime": "2025-06-22T09:51:19.7937449+00:00"}, "WxMl5ia+0hvKbrYPSqZ2BDVNkiQIX0V/jTAHBHlIKMk=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\h2lwp66dso-w4n6sx9nop.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Abstractions#[.{fingerprint=w4n6sx9nop}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8ys1p7g9rv", "Integrity": "0L00QJp2yU1zol4bh/DkeIMNJ90u11L1BS2Sbh9AFrA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Abstractions.wasm", "FileLength": 5721, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "sOM7rKDujb2srBeF9zlP+kHYJu2h+RZkcrPjqs9+7r8=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\gd2y8h43kg-ily916jl2z.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Physical#[.{fingerprint=ily916jl2z}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Physical.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i6hgjj9558", "Integrity": "BlZPfbzFU3HnDx0NNnxpBFh0mnt3e1ExVc6kpcaktF8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Physical.wasm", "FileLength": 17352, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "W9ePCOF69+Mka9EMctxlODtgeBQEG1dsZjzTQH1iFB4=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\nv8mj92s2h-sdsdr06lyk.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileSystemGlobbing#[.{fingerprint=sdsdr06lyk}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileSystemGlobbing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1bx4nliwij", "Integrity": "Y3OKv/EdzFKbAgS+P37p6INWYQiG55ulhvi9NGDi20M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileSystemGlobbing.wasm", "FileLength": 16770, "LastWriteTime": "2025-06-22T09:51:19.8249957+00:00"}, "IhENr426QevFbYOS4TscuKYVJbegNB82ZaDgbkBPYFU=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\x2b8ddlvqg-dygr8vckda.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Http#[.{fingerprint=dygr8vckda}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Http.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kadetxpmq3", "Integrity": "zKuYXEkXged/rxBH8zO+piemfmlgMhWJ5s+uuRC+w/M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Http.wasm", "FileLength": 34601, "LastWriteTime": "2025-06-22T09:51:19.8249957+00:00"}, "GMdfmvXyLE0dr0P1hHyaLAfZLchxOiW1p2WEV1BgAGw=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\v7spksupwj-bvn14pws96.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Localization#[.{fingerprint=bvn14pws96}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Localization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "66g4pb54t1", "Integrity": "3j+uzjtmX1lwC4MpuV+yMliVB3KiWyDykTBd7obL1X8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Localization.wasm", "FileLength": 10094, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "VWKulVdXHCCjF6j9v0io2JLMakjJs8RH08iplJ/QK6U=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\osr8kk5xoq-o4jp2hcm79.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Localization.Abstractions#[.{fingerprint=o4jp2hcm79}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Localization.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iijz9b5a5s", "Integrity": "di088bLcKqrTdZHmkGePxdnl2/xV1E88Yi4KjByq9Pw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Localization.Abstractions.wasm", "FileLength": 3752, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "6RjzpgByjb09swYGL9KDgoPbgayOxbaZ8gKJ5oifoyo=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\fb2g6j1383-tgyhlz8gnr.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging#[.{fingerprint=tgyhlz8gnr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rumzc3hdrf", "Integrity": "cyZN9giYMtRBQ8LjXh/3uDCeD25D2MWtIfdCQ0IKIjI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.wasm", "FileLength": 19451, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "8h7dYGBuugsYQNub4xKI0gMfrGbneykhR8N4qxdWbZA=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\97qa01rzoz-7bglk34tl5.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging.Abstractions#[.{fingerprint=7bglk34tl5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sd9qjilbhf", "Integrity": "AMMAQlSi0FW+DfVMmShYCW1pConz1W37wB/WKDEyCfU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.Abstractions.wasm", "FileLength": 25074, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "z6TpaVq/eKv1iRsAF1ecqgRJd53437/K7O/TXqkN/lw=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\089p7jo6sh-bwt6p2r0a3.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Options#[.{fingerprint=bwt6p2r0a3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ab9ehcnd8b", "Integrity": "pjdlorHCUQQVRU5TsDiBE+5VjAXF3W6pu6syZd8SlB4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.wasm", "FileLength": 24178, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "zqfzaQsOz9XUCG1yeLXw7lNSHYp/Msnr4ytFhS4Tr6E=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\jmse9n9yml-jjv0bwm5n5.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Options.ConfigurationExtensions#[.{fingerprint=jjv0bwm5n5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.ConfigurationExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b1xz2hwf63", "Integrity": "AYg0unBEdFy1yHbsecCURwR0fwmVs5TAJxH6jwHRU28=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.ConfigurationExtensions.wasm", "FileLength": 5526, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "hO5IH+9kHdP3fLN/0IqcgEixpLVoWo0bUVcFqBc87VE=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\xiz7uvkaot-vutb1mf5cz.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Primitives#[.{fingerprint=vutb1mf5cz}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y3i8028kvp", "Integrity": "rtWDssgo0h07fkdnHyitJSHSM4QvZ6WGBfpAfBtx23E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Primitives.wasm", "FileLength": 15640, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "fjHFi7ivuG/IQ1Dq1hoVAYT/5gXUvw+06si73wwNp74=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\hikvg494gs-fzkuir7tme.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop#[.{fingerprint=fzkuir7tme}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "shgbnygtw2", "Integrity": "wFhaikCkYvD54rlnM4bj+s4fK2hfI9vREVLBaCovhwg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.wasm", "FileLength": 24108, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "S2bQSaSHggsPLHEqcQsv34y0qGL278UIIRYz2I07IKs=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\9279xo82tl-btoflm7i7s.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop.WebAssembly#[.{fingerprint=btoflm7i7s}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.WebAssembly.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ctuqs418nf", "Integrity": "gKUAuG3xlQKyCwpryhYkC3t4qcLVg/y1yZmlgPwcFmQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.WebAssembly.wasm", "FileLength": 5801, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "HM99XkOjAF6eS/HTwhofMWqSTQWNVodCOMOHmeuC7B8=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\asj721qfh1-vvc91ohnsh.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/MudBlazor#[.{fingerprint=vvc91ohnsh}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\MudBlazor.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c20sqdmfcq", "Integrity": "VL2tCOBJXm1ds2e0BKMPixGOrHt4r2Mi9LrDfIchu9Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\MudBlazor.wasm", "FileLength": 2015416, "LastWriteTime": "2025-06-22T09:51:20.2312467+00:00"}, "/VHYniogYAa77rb6Blf6AttFK0X9KDjARSEFdXk0B8M=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\mfe83pckm6-9gws8s7zmg.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.CSharp#[.{fingerprint=9gws8s7zmg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.CSharp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v6tjj<PERSON>hub", "Integrity": "h3FbgFwrL3kXanvRLYavpbgXWAIothfDB/V7dQ708cQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.CSharp.wasm", "FileLength": 132476, "LastWriteTime": "2025-06-22T09:51:20.2468727+00:00"}, "Wmpj2VKdNgJSYdhbS01fK5p++8S121CTEgVB9l14pdk=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\pwo1h2ec3y-hev5t09xbg.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic.Core#[.{fingerprint=hev5t09xbg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yy0g3heyum", "Integrity": "6o/KKZQCVvhYYDVFI63J5No2TOWU9mfc/BT/1DkDMNo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.Core.wasm", "FileLength": 171163, "LastWriteTime": "2025-06-22T09:51:20.262494+00:00"}, "SNIxfM8jPCW34dmvoGJwf+Evdo/BPhnnKUvr/7g/JVI=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\8c77pede38-wy3cb00pkv.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic#[.{fingerprint=wy3cb00pkv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t36syn8yrj", "Integrity": "F9QLZFW+PvDJ1nw7qQRupF8/mnBuTqIMqLkJN6pCUi4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.wasm", "FileLength": 2878, "LastWriteTime": "2025-06-22T09:51:20.262494+00:00"}, "Ks5cgItK4GDuankUe43UX29cRRffY97fb/gLEUPTI9c=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\583es7sec5-nt18748s0w.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Primitives#[.{fingerprint=nt18748s0w}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x3ckyz3fhx", "Integrity": "s8cdHG+gfT+jcGbRXZUZR28c0xY3HLxvVedg7mFAjAU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Primitives.wasm", "FileLength": 2195, "LastWriteTime": "2025-06-22T09:51:20.262494+00:00"}, "XZwbMXkD0VPyUgXYbQpJoZJ1mpk1zUFcrcWS2LGoGDI=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\8mrwtrotlz-ykr6iyjchr.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Registry#[.{fingerprint=ykr6iyjchr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Registry.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xr4e51ylan", "Integrity": "U8e5qCG3mnHvdH4zePBI3tcVZ0MIasz3TwrgNRarMkA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Registry.wasm", "FileLength": 9275, "LastWriteTime": "2025-06-22T09:51:20.262494+00:00"}, "kTH2UUKVB8jex9PrhfjhHKY1ixs894zzcPDZH5rhyX4=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\blpmfjhzrs-3h1likbfvx.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.AppContext#[.{fingerprint=3h1likbfvx}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.AppContext.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a5e7izvoan", "Integrity": "og7qwPp31QK8/RTaUZH2+g4QF+BNvuIVHRnFyOYxw/Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.AppContext.wasm", "FileLength": 2094, "LastWriteTime": "2025-06-22T09:51:20.262494+00:00"}, "zTeER2YzeIBK6/5xoKXsEDhmhrE4RzDLP4jaMkxgxhU=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\0g898iif7w-wt7n1r1ovk.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Buffers#[.{fingerprint=wt7n1r1ovk}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Buffers.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r6f7t7ehnf", "Integrity": "Xd4QgqSMM3//nwkXmCGJzTaBydvvNvIf92C6lrRTcjk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Buffers.wasm", "FileLength": 2104, "LastWriteTime": "2025-06-22T09:51:20.262494+00:00"}, "GCdEkP4U/u3EGuL1G7hDelS3Yz152EqbB2T1Tqo9SAE=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\047tn4wl9m-65adg6natn.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Concurrent#[.{fingerprint=65adg6natn}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Concurrent.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tzhmjt3k84", "Integrity": "mbu9hjSCtj0PoETpc+agPXIKSX8j42w6h0jVbZza22Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Concurrent.wasm", "FileLength": 34476, "LastWriteTime": "2025-06-22T09:51:19.8562463+00:00"}, "g1oBUkIrilq0qtr7mpIxI7gLRf5COyGXLz89/ROQySU=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\kk45q10za1-dufaq3kp3z.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Immutable#[.{fingerprint=dufaq3kp3z}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Immutable.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "51bml0oz3e", "Integrity": "WVaR2xDPST4CgXO3OdT5xeMc5uiAlsH+wVtAhPRqNFE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Immutable.wasm", "FileLength": 100293, "LastWriteTime": "2025-06-22T09:51:19.8562463+00:00"}, "EiBwQWSRTraWT6uI+9sy7I/2Gan4t/fFkYdJGKyBMiw=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\khhd7mic44-rxjrzzpp9g.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.NonGeneric#[.{fingerprint=rxjrzzpp9g}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.NonGeneric.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fjmcmjmsnx", "Integrity": "5aAzNQ8ZDjSSAAu9t4e6ds2UfLDe2Vvkxs2KKvrX1PQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.NonGeneric.wasm", "FileLength": 14907, "LastWriteTime": "2025-06-22T09:51:19.8562463+00:00"}, "CV1KEfkRu0FdbfYxORaUUulZF1Wf/Bp5y0xEfcz4Dgk=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\n4nvcw40ck-grj2h3kseq.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Specialized#[.{fingerprint=grj2h3kseq}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Specialized.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ngshtamhf5", "Integrity": "qRChNtZxN7W55HtfMsIBBjpapXjReOHsiNrK9hSsUcA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Specialized.wasm", "FileLength": 16543, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "k1bBXa6ubadu4P56COCEj4qo8yuVA04Bed42OOAcGh8=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\43j3ac0dwp-cip8dbnu43.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections#[.{fingerprint=cip8dbnu43}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2qjzwtvdih", "Integrity": "mjL0X9XwHa06uSx1d+NkRqRjnkVijmpN4XYBqgv5rjQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.wasm", "FileLength": 49322, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "x5ZGTtWRAk6TWnzOEbZRRtCXEnkp0qpQCYg54ndhk+U=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\xeg4897h0y-6hr3q9fx89.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Annotations#[.{fingerprint=6hr3q9fx89}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Annotations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qng43wbgo3", "Integrity": "qr89TS1VK57/yWuwEqPd3AhDawr335Eqy/7PdT+PuRs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Annotations.wasm", "FileLength": 36234, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "xNjfQQ/tt+frKYG4iuCsitc0CVU/ihLO/jCxYDJY82Q=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\6xfibseaa1-k6p4pn9w0l.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.DataAnnotations#[.{fingerprint=k6p4pn9w0l}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.DataAnnotations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ojz6ujm605", "Integrity": "dOOEfjsQYKMiMojuJhNWY5AgtLsogJzmlp4SGxPRDp8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.DataAnnotations.wasm", "FileLength": 2568, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "a+sxWWY5ibOCwB4Kxm2iiDtPodtaYrPD5V1cz/eQDnw=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\gjvo7ls599-p61cj2koso.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.EventBasedAsync#[.{fingerprint=p61cj2koso}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.EventBasedAsync.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2j2ygq395", "Integrity": "DM9SmylVIa0ABDd6N4cURrMyw35GmGLeoZa4x3LaoFA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.EventBasedAsync.wasm", "FileLength": 6876, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "Xo+PLHo80B4RDEScHietxgZm06mm2SqI5fCkYC/MJF4=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\a3rnb2mvqs-fea7hw9xtf.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Primitives#[.{fingerprint=fea7hw9xtf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "umzqgkzc1r", "Integrity": "5kY5evyFlu5oD/u6tyDKQGQF6EMFhyJsIkYGrLcOrc0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Primitives.wasm", "FileLength": 13556, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "lcj+Y6nAPUsOEvQPrfDz6yyXj1287qBmHKQbM4nWxcA=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ll1mlmmfeq-etd3dkcep2.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.TypeConverter#[.{fingerprint=etd3dkcep2}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.TypeConverter.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaeumn0mnw", "Integrity": "IEphPSWR/t+qkYdT9h/iFtb98MrE1npIIoUElqr4/74=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.TypeConverter.wasm", "FileLength": 124639, "LastWriteTime": "2025-06-22T09:51:19.887496+00:00"}, "s+wLPy/hdn/7pDsZmWC7+mB6eppH2+UCGCKuFEXdfic=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\cwnbn444sk-0lm42x51au.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel#[.{fingerprint=0lm42x51au}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9ehupmmhqs", "Integrity": "kSQI0YW7tLwMq3TR8YRUm6WPHgZ5RkBQqSfNmedmzfk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.wasm", "FileLength": 2558, "LastWriteTime": "2025-06-22T09:51:19.887496+00:00"}, "uHPKJYF7YXirMGRU4BECs/gtx9M4IoPPNscpgQhSTf0=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\lvj5n6b6ve-ex6vy58iyk.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Configuration#[.{fingerprint=ex6vy58iyk}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Configuration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "161caa1cwf", "Integrity": "6VL56SWXDcFwHnrmu4YOmvosUAJxzMfvAKctu9qmQxQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Configuration.wasm", "FileLength": 3125, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "Txi+g9LCDG6BwZ7ltk/0zZIS4zCr2l0qy64Cngee+N4=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\uoubio2h9o-s0qgw5psci.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Console#[.{fingerprint=s0qgw5psci}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Console.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rz0hfc8q1n", "Integrity": "ucGbBzPuZQrqYvKyRP7PQUo0z3oFYjiZMOXd2n3s34w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Console.wasm", "FileLength": 19992, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "NwJBGP7xbeZ1Jc6kcIgjY2i4uB2W1dyppMLMbtWEkl8=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\1buw148u2h-zknkrutld3.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Core#[.{fingerprint=zknkrutld3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qqei9kh7rf", "Integrity": "ImTTjwMF7dQpY81I7yc8aPn+ZeH1Eo8K5Qf8NxRZKTs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Core.wasm", "FileLength": 4589, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "F5QnK3KfHhsZuPcPIy4wQ1DciTmCHRBL+IoqxeZM1po=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\p9ux0odz6h-lu92ceoi50.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.Common#[.{fingerprint=lu92ceoi50}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.Common.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qi1rc7r7xc", "Integrity": "crhsAAg9PX4q9OW9EGA3dQo8SUUOISfEtF6jcyfGO7s=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.Common.wasm", "FileLength": 378856, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "COi3L+Q8XwwzFVtIMvbdnisIEUSdE0Zd/hvJj78Ai3w=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\6wyzm4w8zf-2ddk0zm05l.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.DataSetExtensions#[.{fingerprint=2ddk0zm05l}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.DataSetExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cjoiwnua7r", "Integrity": "SHfEC7ChbrKyyh6PfHr62WC6ycP0oYGXIpPOrOv9J4Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.DataSetExtensions.wasm", "FileLength": 2056, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "46gAaX55GHPFx/5iSsaO8WyHg7F7uitptK0HNvgAl00=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\j6k04i6j89-3adg3wr0gn.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data#[.{fingerprint=3adg3wr0gn}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y8tj0c52nf", "Integrity": "0prIMCFH8IgWHw6nctqMm4lcx0MaI7hubWrHw8quhMc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.wasm", "FileLength": 5069, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "/7Ggm0UlqSLzA4TBoF9lY9QaHQQmWgEDkbbS/iB142s=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\n0ttgmg6j2-voyqcmzm7a.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Contracts#[.{fingerprint=voyqcmzm7a}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Contracts.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k6w8b89xvf", "Integrity": "7C/uR2dFUhA93iftWCFS629PxEKWZvRt/BZAaMtSF/M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Contracts.wasm", "FileLength": 2385, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "Bm7m3oOftn7lQQjno5qD0VhiSUOUO7/ZwRD4kxWKvQo=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\092d8vgdgc-tuw7jnpdtf.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Debug#[.{fingerprint=tuw7jnpdtf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Debug.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rygd58ty2p", "Integrity": "3dNhkuxdSU+EpP7VAaKj6rBakddE8ncWP42q14EcQB0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Debug.wasm", "FileLength": 2277, "LastWriteTime": "2025-06-22T09:51:19.8562463+00:00"}, "12NsLuvFwR/Q9mK0OshZkFJ3Jj0vf1JnhehXJDEQMOA=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\nxz6o3tjzw-orwvw7tsnw.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.DiagnosticSource#[.{fingerprint=orwvw7tsnw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.DiagnosticSource.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mhrovf2af9", "Integrity": "KlyLGJDJR+0mx9PwGhLkbgEYbUbG+MEQAwKrqYPMrXw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.DiagnosticSource.wasm", "FileLength": 74360, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "+LY3xzBBGmW9Zeo6pw05Do+PRxMf5UCZbLUEevz1Ljw=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\m940o8z8zu-i2nxqnh8ia.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.FileVersionInfo#[.{fingerprint=i2nxqnh8ia}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.FileVersionInfo.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0axe9t43j1", "Integrity": "CCOc1MLJ7eisi3VSu3+oZ3nR98Ye839tReOKUtpeH8c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.FileVersionInfo.wasm", "FileLength": 5156, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "LGri2WSbIMlx3OqK2NXCkgY3oOXG1TzHSHab7PHXeAc=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\znv03lotbq-yj1m2auw1z.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Process#[.{fingerprint=yj1m2auw1z}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Process.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "10sf8j26li", "Integrity": "f/AsC9ola8vGwZvfaXCsAmUSfYTg1E8ya/O2sG6DVbI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Process.wasm", "FileLength": 16541, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "0KPp+HItAJf1oUlBOR/7fALCU41PrxcbJKjy/MLz3cc=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\5n67cghtjc-9u6hm41m9t.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.StackTrace#[.{fingerprint=9u6hm41m9t}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.StackTrace.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6xgm2t8zkg", "Integrity": "KhWXN8J4lLw4WpkY7Gd1HAal9EKCMKHyS+6vr4Ca9AI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.StackTrace.wasm", "FileLength": 7498, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "viHCgNACPnM8OhHlSGjB8pNnA+mI+/vy++j7eCTUXOA=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\cxsxrp13we-670flx7nki.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TextWriterTraceListener#[.{fingerprint=670flx7nki}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TextWriterTraceListener.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e0tfv27xw9", "Integrity": "YkixRJbYaqyJnUYiAkA0fPAezF2PiukyZIS/3X4vuvc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TextWriterTraceListener.wasm", "FileLength": 9526, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "bA/VtOHb7OXyukLAwYIayVCHEpsHyOD0j9zu5RSC+9Y=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\znjtbx6a1o-2vqkac8ysr.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tools#[.{fingerprint=2vqkac8ysr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tools.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "24y7wsp8ye", "Integrity": "ckjYladl6O5FBWdtYhFRpyUV2ZuYVxfsa6HJusPe74c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tools.wasm", "FileLength": 2173, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "X9Qjqf/Tx0uY00rkMB6N6y/TbO6HvMX1SrXS7zAVLes=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\nbqseuqh9p-n515vmkk2p.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TraceSource#[.{fingerprint=n515vmkk2p}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TraceSource.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cz94m8pb8a", "Integrity": "iFHB34NwrXFuyh3wBBf62BiMg1485sCOejBRBCLAt6k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TraceSource.wasm", "FileLength": 20409, "LastWriteTime": "2025-06-22T09:51:19.887496+00:00"}, "fFd0Dc23eZmLN6gUpr4KuHa6BkjkbqvjR+78KNmGNsM=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\hc59i5rabf-ogliygwa1r.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tracing#[.{fingerprint=ogliygwa1r}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tracing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ikt92ild76", "Integrity": "8lc+1jB1s6pXbMzjX/E2qeUZoA032+8OI/H81MVOf4I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tracing.wasm", "FileLength": 2495, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "hSlmRVB096g04HKuxvIxgJyOXaGkUN9nN0HPD05ME50=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\z0yr2iojkc-zk693pwck8.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Drawing.Primitives#[.{fingerprint=zk693pwck8}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Drawing.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Integrity": "p08ECfEknMjloete7JyFbSdA3pXFm1a11VFl2HtFBIo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Drawing.Primitives.wasm", "FileLength": 24538, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "Ny3HxALMaO5Ah1OtsxKZFIOX+ZbJiEDShzRWfWSuyeA=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\2d1rzsvr2y-wxhr0xa5hb.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Drawing#[.{fingerprint=wxhr0xa5hb}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Drawing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hzcngcw3oq", "Integrity": "g96YTrZ+6Tvlx6oGIVXG+mjQMMsXm5kmT9zhNoZSjk8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Drawing.wasm", "FileLength": 3881, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "Akrjk0/OfvC+NB8Z26s+LT5cpaXlRrohgSazGZyz2tc=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\7pfz9zkwta-ipprcrczgj.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Dynamic.Runtime#[.{fingerprint=ipprcrczgj}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Dynamic.Runtime.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trdcu1r7x0", "Integrity": "ucIPC6pR96gFThOIsvjCWMDCesYit5akdK4GFqMP2Pk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Dynamic.Runtime.wasm", "FileLength": 2437, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "kSfmaULKXNHOYoF7t6cgOIrHw1YZ7RlPzOZsOpxp3Vo=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\i1ryyyc2b9-okhe897m5z.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Formats.Asn1#[.{fingerprint=okhe897m5z}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Formats.Asn1.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9ahg0qs4kw", "Integrity": "gSPTp+vFVWYkUKKF2mJMgYpjKxEeUX4Ccu+OlD780qA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Formats.Asn1.wasm", "FileLength": 35942, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "+oSCI+4LSBTPwvt4uiIfMXzXrf3kZ9+SSrYUJrbuCUc=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\mcgh5g5292-i93u5bq4fn.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Formats.Tar#[.{fingerprint=i93u5bq4fn}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Formats.Tar.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0xmwxg04yc", "Integrity": "/MIly763ny7eEQnXo4Cy4h4MjC28HFhSy6Fwhi0ONrU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Formats.Tar.wasm", "FileLength": 10561, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "gydkvK33J+M8vkwYkO8LJPaAGDpj7GZVkYISTVe6FxY=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\l4x95g7985-x0sb683rhi.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Calendars#[.{fingerprint=x0sb683rhi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.Calendars.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b09pqnjr7p", "Integrity": "2/96J+UH0M8S5w5imkG5bdb4gYCddY8hYPFJrNZ1wGc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.Calendars.wasm", "FileLength": 2282, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "32m54c1TThYZMTHnUscvO8W4gxExGFM6xn8AqMcFtfQ=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\eirmct8v8a-o54lsqobzb.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Extensions#[.{fingerprint=o54lsqobzb}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r9ux1wf8xd", "Integrity": "fyjf11+6g/9R3hLVgYlFCJGJtPCqHmSnAISOJJ0GiA8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.Extensions.wasm", "FileLength": 2162, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "fa7Ww7QibKCj9lm9DNje449P7wzH4OyQHTdo5fPxG10=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ja2nic4su0-tde8zuw0yw.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization#[.{fingerprint=tde8zuw0yw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ticidl90z5", "Integrity": "5UDY9F04tR8iE0DoCjavbc9mm4C6tPtDcyTdk65JFek=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.wasm", "FileLength": 2254, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "DDs9JxyFiYH7vHOQx2En8eVpYBVqaYc7n3898UcFE0U=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\6zclugb5ex-vx3bcge4ol.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.Brotli#[.{fingerprint=vx3bcge4ol}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.Brotli.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wx647a6fef", "Integrity": "JOu6LJao8o4ayZQkmGEeaQgEWBNiEM4YH+fC3zMMpJU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.Brotli.wasm", "FileLength": 7041, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "Zs8Il33Mba0U5zTM+PaDCzilS93DJbSElaEBMhHbG4U=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\y292lodfv6-yhtj6e0w69.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.FileSystem#[.{fingerprint=yhtj6e0w69}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.FileSystem.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4xro9lhb94", "Integrity": "qG9CDyiUjSK95cMkSdLGAL5Qa2D2KqJLK8X7DkGjye8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.FileSystem.wasm", "FileLength": 1974, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "pa26SaSOJx1rjWPZbM7zbWp9sZDa2/FbJvkQYm9uF/k=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\dnpscd8ifx-quahjtap8r.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.ZipFile#[.{fingerprint=quahjtap8r}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.ZipFile.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "czn0iedi5i", "Integrity": "aBNU6qlxP2cj7WkvjlSHXWcHIRCEzsNGgETttcHFZo0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.ZipFile.wasm", "FileLength": 12714, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "xhyX9JTS4zEbh3ZhUxvVSffhzeg8weZSta/c9fmcnck=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\05b0ng3a74-jtaurxkbzi.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression#[.{fingerprint=jtaurxkbzi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sy6t9pb4pg", "Integrity": "fNoiKRaK+kHauQOirNJ/hwOx0U5JbCaTLaRmMyftDvE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.wasm", "FileLength": 43792, "LastWriteTime": "2025-06-22T09:51:19.9343738+00:00"}, "JDgoJ8Y82uQh7aQOVligdCz1J4jCh6DpcsCNgsOETXk=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\r0mn7mvsjz-3yfpgyrku1.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.AccessControl#[.{fingerprint=3yfpgyrku1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wkpchzpsje", "Integrity": "gEZ8LtWz90iB1FdyQdFu6duZLNLLUPSUwQGG1ZqwjIc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.AccessControl.wasm", "FileLength": 8602, "LastWriteTime": "2025-06-22T09:51:19.9343738+00:00"}, "leqIpWvbAB0b5knJrPUwKF2QjtxaoZsAEea4joJwdmQ=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\dy9r3twe3i-8nnv647ull.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.DriveInfo#[.{fingerprint=8nnv647ull}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.DriveInfo.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ovw9kmd9cm", "Integrity": "tMqpLxArg5g4Vc7lfXRGm7wtEIFA8m4dv2ltM6ghbhE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.DriveInfo.wasm", "FileLength": 6070, "LastWriteTime": "2025-06-22T09:51:19.9343738+00:00"}, "b0ehLnJZgdIcCiF38M3fpXRi2bUihIt1iZDUwSSez00=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\llz7r8eoev-ir5j8vbyan.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Primitives#[.{fingerprint=ir5j8vbyan}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "atyl34fv2q", "Integrity": "fsZMAa/NK5oQ2pgkRPsXh4IOcIRlNbS3KOmsoKGtzmY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Primitives.wasm", "FileLength": 2169, "LastWriteTime": "2025-06-22T09:51:19.9343738+00:00"}, "Fpd+3vewWxPrkUss8kBb9m8GVSX01LQuRJF+Q0KEzyI=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\4dya8gbl7e-1lxrwwxsho.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Watcher#[.{fingerprint=1lxrwwxsho}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Watcher.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zklz78gk0n", "Integrity": "/SBkDx88vw3qPUSNKhsPhnmoUHLwYohsanGcTcmPiuA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Watcher.wasm", "FileLength": 8898, "LastWriteTime": "2025-06-22T09:51:19.9343738+00:00"}, "GycsDipbShJQhznFRTi4iiA4UMTUUGA9SnGK6DtfoF4=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ry5ak41fk1-gyxexdekj3.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem#[.{fingerprint=gyxexdekj3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1c7fueudid", "Integrity": "Hzdcr3OsbVyzhI1SeoXjVXy32LwG/vtrDAGVU726434=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.wasm", "FileLength": 2295, "LastWriteTime": "2025-06-22T09:51:19.9343738+00:00"}, "oTGDbuY746DRtYFaRa88ABBsZQU83d8PE5rNBloxcSw=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\3zgx7pexln-tsgf6g1ztd.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.IsolatedStorage#[.{fingerprint=tsgf6g1ztd}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.IsolatedStorage.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mcqtiplk0j", "Integrity": "ptDhHuVd1jAS51ZGnuosQpDI0sUrN7X8oB44f3GB0C0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.IsolatedStorage.wasm", "FileLength": 9499, "LastWriteTime": "2025-06-22T09:51:19.7937449+00:00"}, "ASqlNohfcLJSmRSphleUa7g6V+ZHSmgRetNN1HB3MPs=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\qah6ud1v0c-j4sjofqyi5.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.MemoryMappedFiles#[.{fingerprint=j4sjofqyi5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.MemoryMappedFiles.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ffm2r4izrt", "Integrity": "C0nBSKPvXEw4MKWiTyFaaY9lda/FQFMJIXdSz9Jz1QY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.MemoryMappedFiles.wasm", "FileLength": 16989, "LastWriteTime": "2025-06-22T09:51:19.7937449+00:00"}, "IGdXU17J3To1XDQcbY74YJq12bbjHE99OUIcAjIjvoY=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\cnnfgh2tn8-jiaey0kmyh.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipelines#[.{fingerprint=jiaey0kmyh}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipelines.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lpicxxeh18", "Integrity": "RxGUijC5cvupdZNNHeF4TjGNICa3CZinpaJ7Em3DS7A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipelines.wasm", "FileLength": 31007, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "jtvQUtyYlbJ6r9ZKwguptC+NVY0EYBGt4X21E2OHXp4=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\l4fr8wg0hd-qfh40ih8l6.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes.AccessControl#[.{fingerprint=qfh40ih8l6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ryu1awleqh", "Integrity": "A+3H7AaNPdMiUVARgceILfWP4VZHQyH4zzb4rB3/CVc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.AccessControl.wasm", "FileLength": 5643, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "FPKOoJONl2BsFwdhm7GCtEUUiMesulRuHU/62Iz3zFc=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\cd240ipm6e-al6w1uowde.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes#[.{fingerprint=al6w1uowde}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0lvixrqynr", "Integrity": "nGIWKQX284ya25NttDOPsDCyOD66aJ/K6w74zNATLQU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.wasm", "FileLength": 11575, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "FlUaN11EtR3P/reBa3Dw7dBIFJeox3zBP+ufcFelw58=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ul5ywabm60-d0g45p3x9u.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.UnmanagedMemoryStream#[.{fingerprint=d0g45p3x9u}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.UnmanagedMemoryStream.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i6gslhk1ow", "Integrity": "6tmWhu6Ysd1waq9WcuLOmJVFll3DY+iQB3GjlTcp6Ek=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.UnmanagedMemoryStream.wasm", "FileLength": 2198, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "rF6K7l5gNDJNzfcMP0NkWEIDLzzj025AKiiL+/x5Xj8=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\c9did8j2ns-2zge8rv4ra.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO#[.{fingerprint=2zge8rv4ra}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "df01jor508", "Integrity": "lwS76UQUVLTwsq9siQ5IG7h43tyvHNPw/WwO2qdIT8g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.wasm", "FileLength": 2253, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "zVtbMMv+livcidEn3Pkg6IGb5vggFWfePr84wCi7zYc=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\tq1j3a2kz1-2lw1u6ymmp.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Expressions#[.{fingerprint=2lw1u6ymmp}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Expressions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1ohh1siyxu", "Integrity": "saQsl6KCY5hEzCOx21HJo+aAtG1ZO98HUiS9uimYo3Q=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Expressions.wasm", "FileLength": 217763, "LastWriteTime": "2025-06-22T09:51:19.8249957+00:00"}, "3p91K2YH6AdBrMAoLuVBcYUlCg8Zye5JRF6ue0qJX4s=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\a2k68937xi-1jvfownmci.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Parallel#[.{fingerprint=1jvfownmci}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Parallel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jmh3uz7npq", "Integrity": "BQpmqXAMrcabLPdF8uD/fTJNER32caQYEmiJDLGbvPQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Parallel.wasm", "FileLength": 88000, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "mgnDwptjAW1DO/EpxYicyqXpNkIEE2qIfff/YgpOt34=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\9lo1ab5h68-mv4cb7fqwu.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Queryable#[.{fingerprint=mv4cb7fqwu}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Queryable.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "npkxpdocax", "Integrity": "5YvqHvO21zbsqu1VOfz8a6HqFY66WsPRM2yZ2d2slmY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Queryable.wasm", "FileLength": 21318, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "piC0AwpI7/pbuxPIzhAS3ro6TbWPhsezkhV/luEEsPk=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\92kk2br7n0-3djr1lshgb.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq#[.{fingerprint=3djr1lshgb}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0i5ihx2j71", "Integrity": "WKHXXdh+Dtdjo1f0q2cSrA3rPuh9sS6rG0iWsTVk1+0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.wasm", "FileLength": 56608, "LastWriteTime": "2025-06-22T09:51:19.8249957+00:00"}, "/2WL8AMw5j9fzW3Qy6S4GM/d2yoIKVR9Y+vjyjDmywY=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\zlroalg7g1-ub9sra6ubv.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Memory#[.{fingerprint=ub9sra6ubv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Memory.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3fp9prlp73", "Integrity": "EYIVlPtgWW7kfC97Gju5QH4J5P6sY0eTs8ges6jTVM8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Memory.wasm", "FileLength": 21074, "LastWriteTime": "2025-06-22T09:51:19.8249957+00:00"}, "j7Wt/uiRxqL8A69kqtCxsg1EuYbB32UTKnSMt4q824U=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\7qdb0ner4l-346n69ja1w.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Http.Json#[.{fingerprint=346n69ja1w}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Http.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "be9q0v4p3b", "Integrity": "7fGT2ZKIhV3OljAQgQ9Yrs8aszQGIWwj5qDi8Rpm9y8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Http.Json.wasm", "FileLength": 19903, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "Jb3upwCgUfZ+QwMT1w3TpxrQlDzxRGk6xSsUVrgNWCA=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\gvepi159hm-eupgag7vx5.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Http#[.{fingerprint=eupgag7vx5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Http.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1lxk9mv4kt", "Integrity": "+2fs60sb0YnF2AGTnq4JslmlK5yHryy4qIwiK0G06js=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Http.wasm", "FileLength": 115942, "LastWriteTime": "2025-06-22T09:51:19.8562463+00:00"}, "AgmYkv4g8BwMBXw4AHzA/iQyaS6ejI3ziHwtXewUqA8=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\oc45k8bbiy-m0tberhw26.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.HttpListener#[.{fingerprint=m0tberhw26}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.HttpListener.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pcla<PERSON><PERSON>", "Integrity": "magIrSS4jHVs/HwBQYok5WsEYavuHhrNk12oXpPgT7w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.HttpListener.wasm", "FileLength": 16311, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "IemvcaxmqSL1Q2qbO6lE/svWm3L168OXWi+paCmnJIQ=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\m9paoljv5l-7wmkfq1voo.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Mail#[.{fingerprint=7wmkfq1voo}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Mail.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ayj7kwl1ly", "Integrity": "PvsdEMzBpJqIDM3tfzojVZDqSWvdKrVUZDkYr/HiuC8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Mail.wasm", "FileLength": 42447, "LastWriteTime": "2025-06-22T09:51:19.8562463+00:00"}, "W1k8Kj6p7+QWvvb6f5EStq0eCNhzwkhEstSTAyytebA=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\c3i3mokybs-ee8vwc4vcc.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.NameResolution#[.{fingerprint=ee8vwc4vcc}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.NameResolution.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "etmoa1k240", "Integrity": "gje60v116S8HAqXlBYYsmtuMnqcgv9e5REny+X8amxI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.NameResolution.wasm", "FileLength": 5986, "LastWriteTime": "2025-06-22T09:51:19.8562463+00:00"}, "oVtKGT3NMFd1tAudD3cQUZ12/bqM1w9hUjMj0hBu53U=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\1w7yl2316b-h1hduhi84u.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.NetworkInformation#[.{fingerprint=h1hduhi84u}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.NetworkInformation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f9bljqqp7j", "Integrity": "Q6e5/VF0sDIbIH3P6ResDbsGzZWa85zG6cghh68KcRM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.NetworkInformation.wasm", "FileLength": 13038, "LastWriteTime": "2025-06-22T09:51:19.8562463+00:00"}, "BjfpOO83/koMEu803xttd347UxYvxuDD9Jr1cJIRmYg=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\mbg54xmxzh-y4g427qvfa.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Ping#[.{fingerprint=y4g427qvfa}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Ping.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j1gyre08o6", "Integrity": "SYfs44pK+EqsM+eCOtJoEThY6sulnpTb4oyua/yklgE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Ping.wasm", "FileLength": 7663, "LastWriteTime": "2025-06-22T09:51:19.8562463+00:00"}, "5rQB6b8risEzE+L82h6MRjOI25hSkSVqJfQdtnF1zwo=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\86i6bmgmf5-zv1ut64ban.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Primitives#[.{fingerprint=zv1ut64ban}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a6vs5bag0f", "Integrity": "LqKPpaGjIQKOwe41JryYnyXgogRjvHRBDiGmNLcxKBI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Primitives.wasm", "FileLength": 46557, "LastWriteTime": "2025-06-22T09:51:19.8562463+00:00"}, "oYw3Gb+0E7r/UCEbUhq3C9Xq2DRgZe+wioqI16gDakg=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\wdsn0rxyrl-lnozeoe9re.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Quic#[.{fingerprint=lnozeoe9re}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Quic.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wfeibltbo9", "Integrity": "nAhE7/bNkukzXN2aJ+sETkztBrcoXbpc7btP9LPI6GI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Quic.wasm", "FileLength": 11091, "LastWriteTime": "2025-06-22T09:51:19.8562463+00:00"}, "guUN9k/uYpFl0tN9VCZYy0m7qVVZeaxFa9imNUAoeDc=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\4ybzo0ts61-omoxxcqo90.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Requests#[.{fingerprint=omoxxcqo90}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Requests.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f3cqkzok6g", "Integrity": "uz/kbnyucLQQXeX6eLTH4sum4ER8SWUr424WdSZOcNQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Requests.wasm", "FileLength": 20744, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "hR+5SJIYS24mdic2DUfxjKWhNIC8cGhwFbaUjUcHSjA=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\7e73evga9t-t3a07csu2b.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Security#[.{fingerprint=t3a07csu2b}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Security.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rkd68dufdk", "Integrity": "xXkt4vnA8kP53fuAuK1atqQWSKyBicGTDo7fX/8NSi0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Security.wasm", "FileLength": 33472, "LastWriteTime": "2025-06-22T09:51:19.8562463+00:00"}, "Ji6JoZ1rFPmvB8UUvfXh4i7OoKP/r66re+m6PZA75D8=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\31hdh8z2uy-5v95sh5c67.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.ServicePoint#[.{fingerprint=5v95sh5c67}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.ServicePoint.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8nnw350zec", "Integrity": "tBZ6lTKEIPEf354p6RQHJ7JBKjnnqOlf2ZOhYYBNchs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.ServicePoint.wasm", "FileLength": 2159, "LastWriteTime": "2025-06-22T09:51:19.8562463+00:00"}, "I195yGkLt/zosOA5koV0QB7hwpgDe7pUGqmjwGDxkSg=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\wf6oo5oepj-ww3h8yu74p.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Sockets#[.{fingerprint=ww3h8yu74p}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Sockets.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rlg7eryezk", "Integrity": "zwmmBlqrESIa1CHNAOPvMOY40CPZKA5g/WTA47VY6Io=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Sockets.wasm", "FileLength": 23478, "LastWriteTime": "2025-06-22T09:51:19.8562463+00:00"}, "oEQObTrvb/68dIgQsHXAHTiCgj2f9jOsR+HXYtGGE00=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\vc43hpgb73-345793p9fr.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebClient#[.{fingerprint=345793p9fr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebClient.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vsgt99ha8q", "Integrity": "sQj2Rs7Lmh6pH8LQzvxVhE9WQ4MD6/Ec8EVUzo9U68I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebClient.wasm", "FileLength": 14614, "LastWriteTime": "2025-06-22T09:51:19.8562463+00:00"}, "nXQI0tQpwbjH3DtlCBtt1A15NwQSPLXacBu7kKkca3g=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\64kbjrldre-odv41wuu54.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebHeaderCollection#[.{fingerprint=odv41wuu54}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebHeaderCollection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "08adncjoc5", "Integrity": "ADRVFBitBfvJW/qLgSapAlDshYrLZYhKJ8XLeVrWkwo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebHeaderCollection.wasm", "FileLength": 10375, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "lmHICGOasYSyiFV/LfBJ4NTjUqnvH5decDqN7FeXqT8=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\f50xrqu6yt-ksx7w94zni.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebProxy#[.{fingerprint=ksx7w94zni}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebProxy.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yi67r2s07g", "Integrity": "NuAk/mOUZhjoZ2dMQ+Z7c30rn03fp2+0p7w6CkzCrvg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebProxy.wasm", "FileLength": 5661, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "vneSpUY6a1eExH0GBxKC0rOE4s76SaXG4gcdthf9L7c=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\dgtu894v03-b37svw0y4i.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets.Client#[.{fingerprint=b37svw0y4i}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.Client.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ks02kds5ez", "Integrity": "hFiD6A4vT4RlCIKTPe3NJQi/i7rIMigUdXK54pZhY7I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.Client.wasm", "FileLength": 17384, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "vMbC8mPhPfOYekQtWmVw6KVYIv6ehpMnSx6Wl4Wk24c=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\2lzjr8pk90-9fasahbeiq.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets#[.{fingerprint=9fasahbeiq}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cqkvjudxxv", "Integrity": "TEoczHzZquDVkTWugG/H8EneY6VmoaAndKJ6/D6Wz2g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.wasm", "FileLength": 39051, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "Q99B1wBpxlWYguOpojK62xjv8ORLHj6O7b620bMDGCU=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\lqec3rw4j4-qt5fpja9tg.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net#[.{fingerprint=qt5fpja9tg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vmrcq80u9o", "Integrity": "5xBtqJkMtzUkZ7BFaaaRNaR0xKCmzrQEVSlUC5ukKxQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.wasm", "FileLength": 2746, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "p1moZYaQplrAi7/6jlABqA1THO47ED2DL/+S5YMPVgI=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\guk7dcvv1w-i6kirq3og4.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Numerics.Vectors#[.{fingerprint=i6kirq3og4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Numerics.Vectors.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rbvtkhkzzk", "Integrity": "17tNiDQJJqALQ9hxdVlW/EFMkZisfnLbgl2jxA0OPJQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Numerics.Vectors.wasm", "FileLength": 2256, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "drA2lsSZq8iIevQQzdJFuKGG8svALkeusTQ77yvvnQU=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\eb09opds16-497r8m9pev.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Numerics#[.{fingerprint=497r8m9pev}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Numerics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vkn8yyoqll", "Integrity": "oayTBh7+E5+fD6nO51MB31eCGPeW8/Wjp03bCpP4Cgk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Numerics.wasm", "FileLength": 2019, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "ODDO0paFFH6snzWdrdKKY6GziIRxYRJ8YElsaJ8WYl4=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\bipwzzp8rz-k9az0iuxjb.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ObjectModel#[.{fingerprint=k9az0iuxjb}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ObjectModel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "b4a28c0t3c", "Integrity": "RWhB2O0xoMbp+OVzALiHQO3SJxdthMZQnioHj3SRBMo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ObjectModel.wasm", "FileLength": 13583, "LastWriteTime": "2025-06-22T09:51:19.7937449+00:00"}, "z9KK6nRA4Nx10EMFR/jNfKlh/2HbMZMflXsPR/sJMX0=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\t4lnufzrn4-mnc7tnpegn.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.DataContractSerialization#[.{fingerprint=mnc7tnpegn}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.DataContractSerialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gwfjrw7x86", "Integrity": "QALWD9Ku6IgKc7U6oZFwaC0ydPCCXFiYJrhy7gUtXbc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.DataContractSerialization.wasm", "FileLength": 304574, "LastWriteTime": "2025-06-22T09:51:19.9031207+00:00"}, "I5c1cD6Rph0XzQiDI7Vv2sPJOWFmW9YsDQCzkQ8eBk4=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\czk4id43yr-58q5onb7r6.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Uri#[.{fingerprint=58q5onb7r6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Uri.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ss818p49ge", "Integrity": "NJHTBTQamCCxtXTIPXSxsQe1+XKmyinODiTV1e75npw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Uri.wasm", "FileLength": 42184, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "/BKNYVq5/XvFLSZfp6ikE7uMbkO2ragv75WeXzlmMJc=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\abzlu62u6m-35ud51k85s.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml.Linq#[.{fingerprint=35ud51k85s}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Xml.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y4j9krw9sf", "Integrity": "IukwXbkF31cR4AObCQy726I029y+g55ByDDIZ0wNGyk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Xml.Linq.wasm", "FileLength": 59684, "LastWriteTime": "2025-06-22T09:51:19.8249957+00:00"}, "/2o4UaMtb8R7ef+5Ay8kLnWHNRcw33jnEgPSsklVSNg=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\10stvju5nj-ygkocwikl4.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml#[.{fingerprint=ygkocwikl4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "838nx5q8zw", "Integrity": "tB1QsY92OH0AZR2peqj8R1X7+pylNAkMkR3W4rBFzbQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Xml.wasm", "FileLength": 1069742, "LastWriteTime": "2025-06-22T09:51:19.9968741+00:00"}, "UXVjadJLAD/DdiFGQ/aopW3VhnIRb7gK17UQl2vrrtI=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\6ywu70lv1w-13d6e679le.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.DispatchProxy#[.{fingerprint=13d6e679le}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.DispatchProxy.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e0hth2pwrk", "Integrity": "onvGLQfaVR+HNRAdSRHr22tA4PaJv1fYJXk+GpfwXig=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.DispatchProxy.wasm", "FileLength": 13131, "LastWriteTime": "2025-06-22T09:51:20.0124974+00:00"}, "gRuxNLDnQCWMDVWKZz7HRhV8UuZOUch14XT6C7PO4cM=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\4pwia0m2a0-tnlqh325q4.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.ILGeneration#[.{fingerprint=tnlqh325q4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.ILGeneration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "csk9ppffhe", "Integrity": "fD7CLVLKiyVy/osi1hIzvcpYkFW1kN77anE48h8yq/8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.ILGeneration.wasm", "FileLength": 2268, "LastWriteTime": "2025-06-22T09:51:20.0124974+00:00"}, "pa+s7qiVjdkfwp/6nvtdOGcplY9qgiW/K656Cs3xyG0=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\hvgkomj5p4-6pezgz31ve.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.Lightweight#[.{fingerprint=6pezgz31ve}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.Lightweight.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0h355faxxz", "Integrity": "LL7Sm1n53cbE8zF3WmZCFEi+TLSmAoO8MBfi0MyHX94=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.Lightweight.wasm", "FileLength": 2217, "LastWriteTime": "2025-06-22T09:51:20.0124974+00:00"}, "w1ReQo6ybMWnUw0aOoEqeCfYVYPwknwEi8LZGSYm8UI=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\f3yil2sd26-1sfjh9emmw.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit#[.{fingerprint=1sfjh9emmw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5cidbmcpe1", "Integrity": "6163fQjpazHzBRW4H7AWNdlHgGS0+FB1vIfFmc791J4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.wasm", "FileLength": 52818, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "Zcud9DumH2hN5tbSef8ZOKThjRunr0gCxVWIRBuZ3cY=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\1u5nf91b1b-o3fapkxyot.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Extensions#[.{fingerprint=o3fapkxyot}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z3t2vqrng5", "Integrity": "35YDB4eGpHJ95LTvaoehiNmLjkSwVDDti/iwDFKfgb8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Extensions.wasm", "FileLength": 2136, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "arEHBNmYJX/LjkU0hE4UX+UE1QcUaLpdwwN13Gbr6rw=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ztobwnunug-tx83z6ho7l.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Metadata#[.{fingerprint=tx83z6ho7l}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Metadata.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gxxc4ke464", "Integrity": "xGqWgtj5ydVw2ZtVAW0Mg3FspmDYWlvKghXB9k6ldcA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Metadata.wasm", "FileLength": 195553, "LastWriteTime": "2025-06-22T09:51:19.887496+00:00"}, "CM0E8yjgM9F9gXN+vEwl90WTZ/PVOTa5OuNKGEfclpw=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\onh7od9mfc-z6035msxdy.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Primitives#[.{fingerprint=z6035msxdy}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rswt4l55nv", "Integrity": "tSzP0Irs8TW4dRrHh4sLZYEUaGJITHI/IhpcJ/TgVNI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Primitives.wasm", "FileLength": 2359, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "nDuIcyvwzpExZKfVw8lnWansAK8HBqd0z0OHijd/2BQ=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\gpyh0hbuw1-xqbpbwu9vz.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.TypeExtensions#[.{fingerprint=xqbpbwu9vz}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.TypeExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6lmrv0yvu7", "Integrity": "Ggoi1qpg1yFvHN1ioFnCjsga4g0Gh2wiyvflol7BrFA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.TypeExtensions.wasm", "FileLength": 5721, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "f4UUj9e5XZkKaSJipghr88auOWTTyn9pKVxkusYDXwU=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\5e1o11zuut-1kaq8volf4.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection#[.{fingerprint=1kaq8volf4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c1qg3au3j3", "Integrity": "MC2f919LZk6jBH+OVLTlu7SnJeaY/edmHsAhLnw8bdY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.wasm", "FileLength": 2448, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "3erY9CrH3GcS5SNAad7C+4D/zoiRoF+tTBRbsrTiyv8=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\yum1iez11r-3d1gwadcaj.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.Reader#[.{fingerprint=3d1gwadcaj}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.Reader.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gb9qigays7", "Integrity": "rGNt2PU+erB+akk3TyoYPZX5zVyIgV7j/3g+iiYCEUY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.Reader.wasm", "FileLength": 2109, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "SUP3eJKjeHNzgM815p1/j+FV9Vw20lFuR28R3BG7UUQ=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\es97lu0ofc-pdb0cwov9g.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.ResourceManager#[.{fingerprint=pdb0cwov9g}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.ResourceManager.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v037ityzo6", "Integrity": "D9GpO60c8ldX4Hiu6tJpzctK4mxAq+jDSqpgxL8Kz4o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.ResourceManager.wasm", "FileLength": 2233, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "GiMwU7tnGWbNWHj4ctw5nIHHR7CxIO3LKf+KBwwycC0=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\vmefll79a3-wfwt17t25p.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.Writer#[.{fingerprint=wfwt17t25p}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.Writer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "34sp6w4qs6", "Integrity": "bZ8stjLg53ebSTM15Et8OGa/y32APeRn8qhm8ma8uz8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.Writer.wasm", "FileLength": 7733, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "Ny9s8gTfuBbgV+mWwJy55LM23TqBfWsiZPsLutkNjnY=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\2lmyqeiivu-rt5a291rko.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.Unsafe#[.{fingerprint=rt5a291rko}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.Unsafe.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mvyuthobt5", "Integrity": "7pL2LLz5OqzqdAK/M1rc67gHKYRwI+Mt+q910KYHcZw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.Unsafe.wasm", "FileLength": 2113, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "V7jEHpG9QfCy6FnrTL1Pw6/OT8wkcpHHuqUxKaW8kBY=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ql4vrvr5xs-gigtt0ldg1.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.VisualC#[.{fingerprint=gigtt0ldg1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.VisualC.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nkhifwy7jq", "Integrity": "iQff77t5uL6vhGYfsFOU1vC3FHfLITDN0w19YkxdEfc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.VisualC.wasm", "FileLength": 3070, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "sdLJZa7FShotFG418dhbN4Jy5fPwD7xpQRKHVeIIWio=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\x8zcqs9shm-kaw15hufc0.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Extensions#[.{fingerprint=kaw15hufc0}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wlyzqzr1hp", "Integrity": "DLD9+xmA6GaAeZPdsATM2G1HFtJoZbGmL9W98XtpNR4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Extensions.wasm", "FileLength": 2992, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "PfyQNqEDJWeUduhvSARVVUiOr3PGvbKWUBFnQoAsVbY=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\7ctxoc39mv-7qypx0bvu1.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Handles#[.{fingerprint=7qypx0bvu1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Handles.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zj0i4nyg8b", "Integrity": "scwPVG7kXwmmJ3+YW62Z+9/A9GVpiLvCx44EtXtzik0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Handles.wasm", "FileLength": 2193, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "CGniF0ullgBW8hjAZTj0qyEU2a8ZOSoqJ0046RULF5A=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\fzpagszpqm-k67jm10rbw.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.JavaScript#[.{fingerprint=k67jm10rbw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.JavaScript.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jdlhwgd1zl", "Integrity": "d/YDaVXGLKxYot1ip+7a+REVxsB8tnP5+ysRM03Ztz4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.JavaScript.wasm", "FileLength": 31673, "LastWriteTime": "2025-06-22T09:51:19.9343738+00:00"}, "4FR7XHzh0Q/dqyYyjD0Gbi1MVmxMavk/jEbL0xLUYB0=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ejecmpjgbs-uanr5ywdiz.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.RuntimeInformation#[.{fingerprint=uanr5ywdiz}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.RuntimeInformation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1ze9ycbr99", "Integrity": "88lu31uH9h2xMFILpjvLvRwPNAFzcLey//zoZxeqImA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.RuntimeInformation.wasm", "FileLength": 2135, "LastWriteTime": "2025-06-22T09:51:19.9343738+00:00"}, "jLYB/cN+qyjStBJDeR1fZ5A4NLsN6uUvw4870MkkczA=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\0by6xj74qj-fel5k50x7l.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices#[.{fingerprint=fel5k50x7l}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7zll2bh1yf", "Integrity": "ff8bsek39Z2vrgQBtSiGxaGvflbpGj8tZQt+tzgzeqI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.wasm", "FileLength": 23803, "LastWriteTime": "2025-06-22T09:51:19.9343738+00:00"}, "3OTyvHevk+rtQtKZpl4+rxS9nJuSh5OaFoo04qK0cdI=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\3omn4wg7am-eoagj84dsy.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Intrinsics#[.{fingerprint=eoagj84dsy}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Intrinsics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wtqtjw6gpd", "Integrity": "7P8zvAT5TW20qwA/9UfVgVJrH/MxbVEZkFIWt50oFEo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Intrinsics.wasm", "FileLength": 2726, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "8+tnTnJ7RkOLAQ0B3oiSZo8MKiMpjKUqblk2cG7+YCE=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\wdj0qkbnmh-7g62ykjls0.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Loader#[.{fingerprint=7g62ykjls0}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Loader.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c2x1oz8a9f", "Integrity": "pOEYLfLx5/lnCSMAf4uTFAzWdstRZRTRxDUibLELHqQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Loader.wasm", "FileLength": 2310, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "LxC8ycnNy5TrbDzMyP4OReRXPVRNPUCJIgSmUegqI3s=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\1g9k5ulnya-tp0shtj6gv.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Numerics#[.{fingerprint=tp0shtj6gv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Numerics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5ph2wzbi14", "Integrity": "531+Q9qB1pHWDp1Fg41z+vh4pdnO/nJF6cX7MlmnHWA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Numerics.wasm", "FileLength": 53377, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "A75LqydAP3mKiwYajXLaA4jBlr6x5T8ajfLHvyyXCsU=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\w2x3293hhu-nvsnsgm1il.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Formatters#[.{fingerprint=nvsnsgm1il}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Formatters.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rcg3dlejl2", "Integrity": "i7ZNg4n9i4VIVuVSW5/VzUw4cMQ88XPx0grUvnW3/zg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Formatters.wasm", "FileLength": 24570, "LastWriteTime": "2025-06-22T09:51:19.887496+00:00"}, "Uy1DCH+lP1Cu/H05ahudfqrUX6nkh2ZiSmaYhiQJ7y0=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\k94m19iflk-4t62p34f9u.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Json#[.{fingerprint=4t62p34f9u}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1n3etz794o", "Integrity": "7WKjoeWfL2oKUX4/wPI+ykVjFPEGYgu8ez2XTdzJ/KE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Json.wasm", "FileLength": 2240, "LastWriteTime": "2025-06-22T09:51:19.887496+00:00"}, "RNS3IY7D7wnBrI0JiFHIoBXTSKCxqXfDr+bYieYiTFA=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\jxh59xukja-8mh3k1xubv.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Primitives#[.{fingerprint=8mh3k1xubv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Integrity": "LNrPc8x9DyxajRmoZrbBk71JCQzrGzQ2cI+l423Z9Nc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Primitives.wasm", "FileLength": 5495, "LastWriteTime": "2025-06-22T09:51:19.887496+00:00"}, "CBMPYqV+sDC+6r+1B5pQRRWeyCkZ2h6+sEkxxsbHpSA=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\yaan75m201-6heyz9oosd.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Xml#[.{fingerprint=6heyz9oosd}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xnndll60qb", "Integrity": "DcKjKFoexelIV2bxQJ+kAwymeWC6jeeO5UCGSJFa9tE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Xml.wasm", "FileLength": 2549, "LastWriteTime": "2025-06-22T09:51:19.887496+00:00"}, "Z9jCh61t8IgmWlqRdy+NyAmHyK6U0q/vgpk6oKiQsS4=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\nqfmgz803a-1oa8jl3amd.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization#[.{fingerprint=1oa8jl3amd}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bcmg3094io", "Integrity": "G0gLgJHuAPt4GEQgxTNBPKWF4afqxpT6kP4nAoy4IEo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.wasm", "FileLength": 2494, "LastWriteTime": "2025-06-22T09:51:19.887496+00:00"}, "dmuS6CItSDhVI/VogMWeguncmcIojBYMUwNlX0zP7LU=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\2qwoi9fl35-xqvdvko8po.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime#[.{fingerprint=xqvdvko8po}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3w9md5z1ys", "Integrity": "k6xhAVn2hOgVSjpcJKUwcChqt0oYg1UxPLd03vdz+VA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.wasm", "FileLength": 10714, "LastWriteTime": "2025-06-22T09:51:19.887496+00:00"}, "smicuEfoNjHjXCdySwmiRTtyv0Da7GyQ4ALmshV0BHE=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\66dqee4i3d-jtnq7vre8d.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.AccessControl#[.{fingerprint=jtnq7vre8d}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "v3yey0jx5i", "Integrity": "1Ym8LYop3didT2lrqPCvlrgJjEzGlSlsylOh5uxNYKc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.AccessControl.wasm", "FileLength": 17216, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "cJhc3TvkKzGVkLyvfrizXftHrxU/xsoi+8FnB/a97fw=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\7d73snp3lk-9fyr8onzdl.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Claims#[.{fingerprint=9fyr8onzdl}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Claims.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dumalpyqb", "Integrity": "y+78FSqxnOTAkaxgQSrTl513KkoQAJtiHnqzMWHNfUs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Claims.wasm", "FileLength": 16441, "LastWriteTime": "2025-06-22T09:51:19.7937449+00:00"}, "lsmnJlqOzV9o4ghptE5SJw/TOkiQ/Dy07qjbpFC6Gvw=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\7c46vztfyl-49z3p61zui.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Algorithms#[.{fingerprint=49z3p61zui}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Algorithms.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fxni3ae6l2", "Integrity": "XQuGFeix4PHme5ttyIzHz3hf6ZEZx3aAZmtzvIZNO2s=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Algorithms.wasm", "FileLength": 2711, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "uIBY+D5ApIjAvPkfH3YkJWlZv4LITI3XunMr83qanDU=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ldchrd2ota-01efu89mjc.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Cng#[.{fingerprint=01efu89mjc}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Cng.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bl6uopkdqg", "Integrity": "Ied4eBbUoibS/4YvIh/9d2SP5Br+PMElZpwpwYS+SUA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Cng.wasm", "FileLength": 2465, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "xTyP4PxGYlbNM0AgCl++C8Y8VOt6CqJ0rsWNsrGUKiE=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\cu1bqe1l66-m6kt5rkphi.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Csp#[.{fingerprint=m6kt5rkphi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Csp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l8k76ne4z5", "Integrity": "yzspymtMcrrVsAF06N7A4Li+aqibd0k7d+OOwFlEJps=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Csp.wasm", "FileLength": 2337, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "/zsYxBjeUTW6GCS/gJxHtKCao8hiwSCIFrqzQMl8T8I=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\asx1gpn1le-4j2304etti.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Encoding#[.{fingerprint=4j2304etti}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Encoding.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bh55ti10w3", "Integrity": "AYC6S7nA6hG1FzlgKdFL7sSs5myXWo1Afmxd0n+4GLM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Encoding.wasm", "FileLength": 2267, "LastWriteTime": "2025-06-22T09:51:19.8249957+00:00"}, "HK7tNU4L2TozAGN+es5Dbid1BLiWAHPNBZuzerBpXRY=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\xysbwwi2pa-rgf4gnhaju.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.OpenSsl#[.{fingerprint=rgf4gnhaju}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.OpenSsl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7h4x28z4fw", "Integrity": "6yZXPBxdIqVTMQqwxuocdBNBY6f20EUIWmLwEydfWN0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.OpenSsl.wasm", "FileLength": 2199, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "fsaSZY4Rqp7/K5wBxutNO5skClP2pNvHE8FdZGQvNqQ=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\06q9fio76v-wqi94vu5m0.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Primitives#[.{fingerprint=wqi94vu5m0}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rgx2r1g9kw", "Integrity": "6/Q1nLCsqlsLH0gMZynIxrzsRYIzrsCfVis8loaZij8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Primitives.wasm", "FileLength": 2328, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "R0TwJxQbx+T0LpnLC7xXkJLRoBkJk9YFuD0FDB5O/9c=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\lq3eyvxidg-e4s9csihna.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.X509Certificates#[.{fingerprint=e4s9csihna}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.X509Certificates.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2xmdaof6am", "Integrity": "Rs9CzwX955M6+J5cSE5+UtYp/kd2+k2qwntwNqM5A58=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.X509Certificates.wasm", "FileLength": 2668, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "6+SImCeGYN/R21UI3TEBfijUBoFv6/1iGHsfE9zKoZY=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\tz3iutuh8k-z9o6jihhaw.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography#[.{fingerprint=z9o6jihhaw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xxa6bxtj1c", "Integrity": "G+QteTbO5tnfPdBGQsv0Zr+pUUn1rlL3RPAUUOzmDcw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.wasm", "FileLength": 192141, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "Yo3nDZ96GX5DCzEHqnZdVm0VQCFSAqDpk3WtMr3OY5k=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\3tkjo3sq2x-d93pggsupp.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal.Windows#[.{fingerprint=d93pggsupp}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Principal.Windows.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4o9pv9buaz", "Integrity": "NHADR5aEs5o28yGOXqSHmsBO9Zy5ri/BkJAE6Teozxk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Principal.Windows.wasm", "FileLength": 11366, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "yMRDDZxQuiwf9FswTe6P99c0xw/VHQNoAoKBnILyQh8=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\rib056jhl1-sa193kq3m2.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal#[.{fingerprint=sa193kq3m2}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Principal.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1qh6t283fw", "Integrity": "OWQzw4Za3nteSzWsPES44yknwXZhgqp3r5KGCde6q/w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Principal.wasm", "FileLength": 2148, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "SeMpf69fs1/0+Yn6nriEONsiCt96wRsulXZpxbMZ/YI=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\kgttb1i5ls-z4ma9duddm.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.SecureString#[.{fingerprint=z4ma9duddm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.SecureString.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5j82ul3idq", "Integrity": "T2ZLclHRPGrl/GipYza2YeBIG7obvhp8Ok9tH0XtLd8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.SecureString.wasm", "FileLength": 2178, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "+IAASAnMKRFMEP73va3xYKaSVjdB/Yx1e+5d/m4bsH8=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ecjnv8uddc-rztf0whns2.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security#[.{fingerprint=rztf0whns2}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6ws3zfk55n", "Integrity": "BtTIpA0y7Hg3Y2L+P/umobP0CpSc+NRMV00qR7mZiQA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.wasm", "FileLength": 2975, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "n2uKpDmwJEgt+fvE4slIV/XY+73lWJPIQKqYhuFBtEM=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\po3xh0abvb-bnlcmxi1w6.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ServiceModel.Web#[.{fingerprint=bnlcmxi1w6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ServiceModel.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qnfhl8a7m8", "Integrity": "hh68E26kzzMIOxfcDDPfs1dZCg9HgEC2jksd69R5u0U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ServiceModel.Web.wasm", "FileLength": 2536, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "MBhA0MX+v0JzLSPVU9RX2C4HgqFqjDa1481NXwLFJCM=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\radirn3peg-drbdk7bquo.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ServiceProcess#[.{fingerprint=drbdk7bquo}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ServiceProcess.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0o2as0h125", "Integrity": "AOj2B3FNj2/Ixiwa6tfgejJBd5lNZAMe6gJ9WhxYSpk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ServiceProcess.wasm", "FileLength": 2290, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "rKV/tFraK24l88Fx+MP+U7/aa1OudmZ8+69XG0CgAns=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\5kx88tufpw-7iff0d2lb6.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.CodePages#[.{fingerprint=7iff0d2lb6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.CodePages.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2vtkvr7sfd", "Integrity": "Lg/kcZFbiO0FE3/7DOjFHgY1G5tMsCH9J8pdWCkarig=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.CodePages.wasm", "FileLength": 518369, "LastWriteTime": "2025-06-22T09:51:19.9499992+00:00"}, "P4XohDJ/3bhPQhseCvmanl3uUA3hbfHn33D+zWE1yO4=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\zv5xb7pdxi-dc711vstge.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.Extensions#[.{fingerprint=dc711vstge}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ntap45v1i2", "Integrity": "C+soKeklt2AF1yBlXQyCoynz3Iylfrn3GZ0WrcwifLM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.Extensions.wasm", "FileLength": 2243, "LastWriteTime": "2025-06-22T09:51:19.9499992+00:00"}, "bXIWatQYfa+TlVq7Nw/2O4aKYC+UFB49CZ/MMg9dSh8=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\qd3x16gmyn-3696nx7xrc.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding#[.{fingerprint=3696nx7xrc}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "leeatf83wu", "Integrity": "k3+G/lsFhhCq1B7AAKVCtcPG5UoJDTTZruusOkdNh/o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.wasm", "FileLength": 2230, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "XPd9yYh/C/lPHmkXNxSYpaclxpJsuNkXO3eVFdxOCLE=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\m2wq4fipdh-ksemyzm5ld.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encodings.Web#[.{fingerprint=ksemyzm5ld}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encodings.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "od24wa2zc5", "Integrity": "9LKtbkx+vEj07nUq1jubni5vfAl1tycI6V9Dv/ZbIUk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encodings.Web.wasm", "FileLength": 23947, "LastWriteTime": "2025-06-22T09:51:19.8562463+00:00"}, "llYdYNvGDr7IuR4ipyiuwQo3nVXK0xPpBI5Nj6vdncI=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\o29qy48hz7-x92ye0v3y1.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Json#[.{fingerprint=x92ye0v3y1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t701n6znlm", "Integrity": "Wfbxj4fsGd8RBghABUZEBF8YDLhx6YsmfpPSPPJ9zyg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Json.wasm", "FileLength": 221068, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "T2mhiHtHlmWOTG4ySvB+r9b+o5mhS0bp8vMorUGUq8s=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\d5tsrwye6m-dnj9z23s0g.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.RegularExpressions#[.{fingerprint=dnj9z23s0g}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.RegularExpressions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pm9l8lcd3g", "Integrity": "/1/zoRMl9NOalOaeAp9B3hj5YcGPRuF8bVJfstAyZH0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.RegularExpressions.wasm", "FileLength": 156828, "LastWriteTime": "2025-06-22T09:51:19.887496+00:00"}, "KRUkMUqNSUHI2xsZyUsRJjvYtgPSKb1fGQjghRlj7oY=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\kfejvvjs6u-tomvzoqfcf.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Channels#[.{fingerprint=tomvzoqfcf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Channels.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3h7dtb1n99", "Integrity": "6X82iHcnII3dTl2enW4OOzzBU1lv2RgmE09udheqCh8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Channels.wasm", "FileLength": 21003, "LastWriteTime": "2025-06-22T09:51:19.887496+00:00"}, "R+cS7SzVMJBSvU8S8WwYTtB+LppnAuj5h2xBfcIHSwc=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\2lnccv90rh-0g3k20op8c.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Overlapped#[.{fingerprint=0g3k20op8c}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Overlapped.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jggnz8wud5", "Integrity": "E1aFmjga0iAdsoKdBzoUcehW/CPkfIe0BMJC/IDWCp8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Overlapped.wasm", "FileLength": 2294, "LastWriteTime": "2025-06-22T09:51:19.887496+00:00"}, "tijtS6DdndBVRWY7CDhzHdGenmFc408ti4uXAShg2uA=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\hlfs1kkasw-55tewhp7kf.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Dataflow#[.{fingerprint=55tewhp7kf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Dataflow.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fnn2z7a742", "Integrity": "xTxs0XlJRWyXMWLzJEjNgm8FIJahGJELz0sOsiCoyvk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Dataflow.wasm", "FileLength": 74082, "LastWriteTime": "2025-06-22T09:51:19.887496+00:00"}, "u96P9p1YST0M3NXV++JMVCQwUuA9IyAbq/+0/N+aA4o=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\erbawa0wx7-sce61xpslf.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Extensions#[.{fingerprint=sce61xpslf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l86mmsxm7d", "Integrity": "6+C8ZMjr5MzTX2QkIHFvL7QbodsxysQCokm9JITGTK8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Extensions.wasm", "FileLength": 2293, "LastWriteTime": "2025-06-22T09:51:19.9187492+00:00"}, "iPP9cVroRHuv9z2U/JwRcaU6UAyFq04+AIXlZ9NL53I=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\jettazdogb-548crbk151.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Parallel#[.{fingerprint=548crbk151}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Parallel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "otm9gs4gkl", "Integrity": "zBuzsvnFhLJv4jPUinrND4oS4eurGslzTORlmmqlpjg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Parallel.wasm", "FileLength": 21510, "LastWriteTime": "2025-06-22T09:51:19.9343738+00:00"}, "p1vvE0dCVj50gPbWunsWD25e0PM4cI7M2COOSPHZsJg=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\i4ve7v6q56-x86n4j91or.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks#[.{fingerprint=x86n4j91or}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y3o7o8ez1e", "Integrity": "MrJfPBMuv/KdBnPTOOfA8D1AKzPyTYL8sXn88of13gk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.wasm", "FileLength": 2558, "LastWriteTime": "2025-06-22T09:51:19.9343738+00:00"}, "QMuElbyloH84teWNCmiLiuTuihUJHANQvV9VGqnOWGw=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ibi3o1lmq5-07bttawl88.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Thread#[.{fingerprint=07bttawl88}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Thread.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5x5ytvgqzz", "Integrity": "eevbnNJRnoZurKE4BR8GZIhQGibNr3QdTnAv6ZLHtE8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Thread.wasm", "FileLength": 2326, "LastWriteTime": "2025-06-22T09:51:19.9343738+00:00"}, "OtSNH+V4FSgSJGXGZkbjprXLvWdOmjGVDOHfiyvVF5g=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\0crezbyjyp-zt447d1n6v.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.ThreadPool#[.{fingerprint=zt447d1n6v}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.ThreadPool.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a50n3sx6h2", "Integrity": "fsNmThN5cq0Mt/ymgJQg3FfNKjWuRYAe9K5reWHeYCs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.ThreadPool.wasm", "FileLength": 2248, "LastWriteTime": "2025-06-22T09:51:19.9343738+00:00"}, "JBuX8VbnZwah9i1H2eTxcvc7medhGxzZ/vV6Z+y7ekI=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\syanezeado-r3c1h58f9w.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Timer#[.{fingerprint=r3c1h58f9w}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Timer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rifr52ofi6", "Integrity": "rctNuwi/MAv5qJpXrGFu5z3r/2b+SDHKKnEc0VH9sWg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Timer.wasm", "FileLength": 2115, "LastWriteTime": "2025-06-22T09:51:19.887496+00:00"}, "tX2ZFzU1NQrL5KAOlSUreJ0NbS3GNa0EddV8qaaUCKw=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\h1pclr2djq-4z6mzh73ny.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading#[.{fingerprint=4z6mzh73ny}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "89qmmkhfst", "Integrity": "bKQOk5IGQ34BE/Y/jzJJ/zIr3M1siIbJr9+6C/0mn/o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.wasm", "FileLength": 14941, "LastWriteTime": "2025-06-22T09:51:19.887496+00:00"}, "IycPHIDseMlqfBWWh/2JvFkJymEAQ3rH6b4UEdFgbLQ=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\om7kli6l15-pil3cjgvw5.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Transactions.Local#[.{fingerprint=pil3cjgvw5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Transactions.Local.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vgpahe5lyc", "Integrity": "kSe3I1xWPAufcnWvY7Xq/8YT2iK4rY/NX+MrioJVtkQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Transactions.Local.wasm", "FileLength": 52477, "LastWriteTime": "2025-06-22T09:51:19.887496+00:00"}, "SA2eFZbHmONN85/zm5gDK3/hfFtNSUltBcbDalUoHOw=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\p4pdpk54sb-g6ni30uafv.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Transactions#[.{fingerprint=g6ni30uafv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Transactions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l1gdhuk2kv", "Integrity": "h8TpydIBvtLjBYRqPX1JaCF7peCV9edefjFw8daFXNs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Transactions.wasm", "FileLength": 2361, "LastWriteTime": "2025-06-22T09:51:19.7937449+00:00"}, "tLM9nNpA0IR60B7dEVIjreJtXlQtceDrulK5e+1WJ4U=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\tb1sbmlzq7-adv6hyw1vi.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ValueTuple#[.{fingerprint=adv6hyw1vi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ValueTuple.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uk8d9h4xhb", "Integrity": "rnmEasDldWxCjZLOI5Wia0+lB4zr+uHaT+M2BWJ9SpA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ValueTuple.wasm", "FileLength": 2162, "LastWriteTime": "2025-06-22T09:51:19.7937449+00:00"}, "07PpuyqN8YYZ/RQe+RsLChevLU7VV4SEQai+EgwJgXY=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\dgxvboka7h-4yi0atwy17.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Web.HttpUtility#[.{fingerprint=4yi0atwy17}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Web.HttpUtility.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7klcrsyr07", "Integrity": "2TxsLC4iLUfX8bRf+rnVHzo6bJtio9FCxMBLPa6OHPA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Web.HttpUtility.wasm", "FileLength": 10064, "LastWriteTime": "2025-06-22T09:51:19.7937449+00:00"}, "HekucyI8Lpj58ye5GYyR0JtQRfehiPzC2RAr6KVtbyY=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\y7gw1kgtnu-8uickrr2w7.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Web#[.{fingerprint=8uickrr2w7}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9hqzmdmg5d", "Integrity": "MwXwvecCl+SGQrtp/IB8h8FfvevcK89G+0JjywN+C+s=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Web.wasm", "FileLength": 2106, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "dw4m3gR3z+BkKvA67e/S7Vjs64J+6OqJj9riunUosEA=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\d4ndgctfln-idlgil0u1u.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Windows#[.{fingerprint=idlgil0u1u}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Windows.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "eekhdpembb", "Integrity": "eu3I5qgLq5W4qWFwLmSmG8Z35hIe9ctCNUeivjXh3WI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Windows.wasm", "FileLength": 2264, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "GvDge+wgCBqZ0bTGsGaSPloojTXaQkq2/8j76eXmrp8=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\fj21328flb-jhjtvo31q0.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.Linq#[.{fingerprint=jhjtvo31q0}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n40nabuvce", "Integrity": "mamYlqSUvXvUlF00FqByjjJHF5WnvlEerUwlNms01JU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.Linq.wasm", "FileLength": 2205, "LastWriteTime": "2025-06-22T09:51:19.8249957+00:00"}, "g7phg/DAS6+MOcc253nYWetidKtsGpPPiTd0u3+BgF0=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\lbefjm3fer-pcqwh7wu97.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.ReaderWriter#[.{fingerprint=pcqwh7wu97}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.ReaderWriter.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5twxwprbc2", "Integrity": "p0dyu6/gjEqt7HCPOVYRYLdOpalZPOSWc5PKBuExWA0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.ReaderWriter.wasm", "FileLength": 4022, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "8Ylx0gm34XGLwfoTA3zhTt84mZFdGBphN/h1FC0WOkA=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\lticvmvabn-0x6beqi7zp.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.Serialization#[.{fingerprint=0x6beqi7zp}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.Serialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mc2pljwaz7", "Integrity": "Jrk1R79xeAFdCbq0Kc1d7QHIDXfuH9kXXf1D3ZaqOWM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.Serialization.wasm", "FileLength": 2234, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "gvww0pxIDjhdfLFAAd2c//yOQVhXEpISG7WCAxPHQLA=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\iajbevbx7x-8luigpl137.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XDocument#[.{fingerprint=8luigpl137}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fbmrz5dke3", "Integrity": "u7CkA6egzyEHbHc7L8+b7LT9DidsxOaKsR+fpcOj2Ac=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XDocument.wasm", "FileLength": 2388, "LastWriteTime": "2025-06-22T09:51:19.8093703+00:00"}, "3s5qVmrHDWdlsz5AryYLo2aNo/jbIvun6F9Wqi3ae44=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\i2snljizbn-rps120mzwr.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath.XDocument#[.{fingerprint=rps120mzwr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.XDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gq3ft88dnn", "Integrity": "WCLUHbMkDvH3esV5A4Gui8Jgdby9rwRWFO8jGCFxgiA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.XDocument.wasm", "FileLength": 2471, "LastWriteTime": "2025-06-22T09:51:19.8249957+00:00"}, "gf9xeWOihcbLGUGTGLzSGWc2UC8POLtLpP3WWsHQHuA=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\qp5h3espka-nj6o6nhskf.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath#[.{fingerprint=nj6o6nhskf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bodr2nb7ol", "Integrity": "++uAK+mUEMon+RcnZC7TFIr5HUcae/dnJjaj2G+BOac=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.wasm", "FileLength": 2304, "LastWriteTime": "2025-06-22T09:51:19.8562463+00:00"}, "8OJ4ZMhEmPNyE59uufx99FqUWC+bjs2qchxoqQPtCYs=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\htuzvqynu1-t7u25q5to4.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlDocument#[.{fingerprint=t7u25q5to4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XmlDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7s1grkdd27", "Integrity": "B0gfSt72UNpE20N7SjH/fqE0e9oo4ZFzwPoFgX8esA0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XmlDocument.wasm", "FileLength": 2345, "LastWriteTime": "2025-06-22T09:51:19.8562463+00:00"}, "CCkOtVN/jqLVeqPwnhShNatFzKzpLpoROZBsNgK+g/Q=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\bzzmnifaxi-ig2qir1wep.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlSerializer#[.{fingerprint=ig2qir1wep}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XmlSerializer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "is4ytwjijb", "Integrity": "PkZ+EyfQnZrYKPRFQ6MiSkbuJRS8u/bDxht3wk0rkkg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XmlSerializer.wasm", "FileLength": 2846, "LastWriteTime": "2025-06-22T09:51:19.8562463+00:00"}, "QXNP0YOfmKeTQHiHwJnEOqCtL0lNbdvnBxVNWfQx7mM=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\u9czy4sbgq-lnwczuoimm.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml#[.{fingerprint=lnwczuoimm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q4ukya97pt", "Integrity": "iu/I+gH6y+kkF3PiDqcN1MfrstHppZAGB7Qtqm76EOQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.wasm", "FileLength": 4234, "LastWriteTime": "2025-06-22T09:51:19.8718703+00:00"}, "WNZk0Kj19Clf38t89QzSEssoVPyHw4/X4JDE9rP3wSA=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\pejbvlp1jq-00ls1afmp9.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System#[.{fingerprint=00ls1afmp9}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "frqj36ovfx", "Integrity": "OwkxzLI1iHJHnDG0234yccZ21jqD7BQbgqdaX3KA5lk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.wasm", "FileLength": 11846, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "6C9PeI4lVWk/N11CCRdcjeQxhfea4IAuygV+Fl1zigY=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\zsl1wupbag-u25hol0de4.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/WindowsBase#[.{fingerprint=u25hol0de4}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\WindowsBase.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "57i10gl44u", "Integrity": "YaJsYRR7iSBjrEfxtDToxuLFXqdXo0r9g3uDrwA1uSo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\WindowsBase.wasm", "FileLength": 2504, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "yNZSLTpYNwCCd0xSb/WUKABPhxyu2XUz7SenH7epBR8=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\b6z1yv90xl-brg9pkj3je.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/mscorlib#[.{fingerprint=brg9pkj3je}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\mscorlib.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ypb5slrtpd", "Integrity": "lsphfcbp9xwONswYJJqYCzrQVqvHy0mclKUNzeLpRdY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\mscorlib.wasm", "FileLength": 14878, "LastWriteTime": "2025-06-22T09:51:19.8406198+00:00"}, "POEYGns4yi23smPNe3aeDAupJbRChBHvwhIXt2CLcY8=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\98whem6pgh-wuzd3f1y6v.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/netstandard#[.{fingerprint=wuzd3f1y6v}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\netstandard.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4l4mji57i8", "Integrity": "SLRIACbRkUXoCjQDdpp9LirIHrdinKXs3lqI3BdrMdo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\netstandard.wasm", "FileLength": 26233, "LastWriteTime": "2025-06-22T09:51:19.887496+00:00"}, "YXtBB8kDt0j/SacREOeebCF4zD+dJMhtNGHNpaApViA=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\5aylwruku9-05ksnw82w3.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.CoreLib#[.{fingerprint=05ksnw82w3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.CoreLib.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rxvosxm41u", "Integrity": "UShkkIOMntK28YIObvxfAAbRh3p9t/miDqz8JmKNPVM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.CoreLib.wasm", "FileLength": 1533168, "LastWriteTime": "2025-06-22T09:51:20.0906218+00:00"}, "8AYtDHMiO4c6DrN1Q15LGd9whDeyBUNrtJiJ4nm1d4k=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\7jar0yvv5d-vr46os3pyt.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet#[.{fingerprint=vr46os3pyt}]?.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z8vxgrbjrs", "Integrity": "jRffxLcG2kO4KvKQDS4JewXvtth/4aMckdAFsS7Ntr8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.js", "FileLength": 12785, "LastWriteTime": "2025-06-22T09:51:20.0906218+00:00"}, "IXgW+EzOawAVmZgrZPNaPt//u+YRpWkcStIwecr5z24=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\p15yc418pl-es3ekshrlb.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.js#[.{fingerprint=es3ekshrlb}]?.map.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dazcf3t92r", "Integrity": "W1djn9Nvf/ugFH4BHhvUKv/0tQsE5Mamf0CA8tRLQdw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.js.map", "FileLength": 21303, "LastWriteTime": "2025-06-22T09:51:20.0906218+00:00"}, "lfONajwS8qyPYYhr9tnLpvk9hx7BjoNT2XFbGGep6J4=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\rbkc9ujucj-rtblh4npr3.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.native#[.{fingerprint=rtblh4npr3}]!.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.native.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7hrrqjrs99", "Integrity": "2/kK375OV1gVSakmVUOOLQXTGCPzHIs/YTLd72uF7v4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.native.js", "FileLength": 35025, "LastWriteTime": "2025-06-22T09:51:20.0906218+00:00"}, "P4S3jABjG94isim3A8UrjNsmYz71KyPDRmRgpiMa4xo=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\we8pxh1ekj-aqhezbunpl.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.native#[.{fingerprint=aqhezbunpl}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.native.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "912ednjrrt", "Integrity": "zdppwT9yq8U2F6jID4314pv4fL86vFWR6ukNtGDPxYg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.native.wasm", "FileLength": 1198967, "LastWriteTime": "2025-06-22T09:51:20.0281219+00:00"}, "Vmq7SS3omqv56Q9m6CfGPNA+Fs+IALEjP+4WQF7/bl4=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\iyinirezft-d1pzlaz2ez.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.runtime#[.{fingerprint=d1pzlaz2ez}]!.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fmigfovg0n", "Integrity": "R4Eu19IscrhtfS9W/5WbwUHDmFbT7M+2f4QVMNV7NeE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js", "FileLength": 56236, "LastWriteTime": "2025-06-22T09:51:20.0281219+00:00"}, "zTUjnHOaBbnKsrMhd2E0V6qzWLtRY/6Ix1bvHyy4+ok=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\j1rnvbyur3-ctf2q9h8m2.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.runtime.js#[.{fingerprint=ctf2q9h8m2}]?.map.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bzl5x4v6yg", "Integrity": "h78GQCle4nEpvR7UesZohTc9u1Q0FUS8TJK6eQ0GiII=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js.map", "FileLength": 88605, "LastWriteTime": "2025-06-22T09:51:20.0437483+00:00"}, "Fc7lMx/TubvFTsZMTYVbv9paIBVW3DmDWqnElpngWnc=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\bw5jamie8q-tjcz0u77k5.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_CJK#[.{fingerprint=tjcz0u77k5}]!.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_CJK.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ffu5aujli6", "Integrity": "Sxt3k51yp5RyINpi7a/YQNnTWexafADiNQYBnBjQrYc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_CJK.dat", "FileLength": 359724, "LastWriteTime": "2025-06-22T09:51:20.0906218+00:00"}, "Vo88GI4jQ5zGBGvLPFPJzU5sF2UMJX17UAdO9d/9pQ0=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\ik2mr4q057-tptq2av103.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_EFIGS#[.{fingerprint=tptq2av103}]!.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_EFIGS.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kcp4n5gllp", "Integrity": "rLsEn/DgWXf7nRK2qegv2ARpYrcwixMOaXOoFcVmgjg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_EFIGS.dat", "FileLength": 220055, "LastWriteTime": "2025-06-22T09:51:20.1062457+00:00"}, "cgSgjh/4QRRYM1X0Yl+ayqiLG8gfCGbl0YbvQv24I0U=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\0sdup4ez0f-lfu7j35m59.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_no_CJK#[.{fingerprint=lfu7j35m59}]!.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_no_CJK.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m3twp0zama", "Integrity": "UsST+ZWYFgogrz0pZB/4gGQWboPgRkurfdpi/0/ENCA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_no_CJK.dat", "FileLength": 347023, "LastWriteTime": "2025-06-22T09:51:20.1531224+00:00"}, "R8PdHylNExoyGNkBOhuBXyqgOKU5kW1nhP9s4q8rmVU=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\8iiqvwjzvo-2vf8dcmeoz.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Baytara.Shared#[.{fingerprint=2vf8dcmeoz}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Baytara.Shared.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iq7rz9bj0x", "Integrity": "FmmT+T7Zt76ViC+LhBvSR5enejFR7cPJiTnqgTiEVzM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Baytara.Shared.wasm", "FileLength": 11483, "LastWriteTime": "2025-06-22T09:51:20.1531224+00:00"}, "GlIuLk7vsFEXf8mnbd3/keCqCwSucnhPjUr/g10/lmE=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\oka2omkyb2-gkmkl1gpm3.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Baytara.Client#[.{fingerprint=gkmkl1gpm3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Baytara.Client.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o6cev7cp3o", "Integrity": "MDA713uOMdoBuiGSdp+TiU63RUrJNtvUIIoTMs0NP/o=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Baytara.Client.wasm", "FileLength": 122309, "LastWriteTime": "2025-06-23T10:20:55.7168367+00:00"}, "xSVa5pDOP8Fufcz6S0dNUb+wMV3Tc0gr8MioNDgdvJU=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\51sg03yyfk-juidjho7lo.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Baytara.Client#[.{fingerprint=juidjho7lo}]!.pdb.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Baytara.Client.pdb", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zjqepw85hf", "Integrity": "73YsHESBDY0yuq2QAYO6rnGHL8PEHREf6B3/bRFwz4I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Baytara.Client.pdb", "FileLength": 175273, "LastWriteTime": "2025-06-23T10:20:55.7007556+00:00"}, "o81or9snCI6xoLmPX2s0d1Ib6bvAW3RYr3mUZ+HC87M=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\lt97u3j6w9-oxo6hhf4ww.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Baytara.Shared#[.{fingerprint=oxo6hhf4ww}]!.pdb.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Baytara.Shared.pdb", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1bej3u7uvh", "Integrity": "0yU+4/gJVh+UzjUgZWUo+qxlu0Bx+TeWXnte76AOqC4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Baytara.Shared.pdb", "FileLength": 9419, "LastWriteTime": "2025-06-23T04:30:02.1952348+00:00"}, "oElTycyt/w3XHCs8nzgKKdrKxpvGdmbmYAzXNmB5gxk=": {"Identity": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\gme93mt5pd-7n7b44h7rj.gz", "SourceId": "Baytara.Client", "SourceType": "Computed", "ContentRoot": "E:\\baytara\\Baytara.Client\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/blazor.boot.json.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.boot.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z5s79z2dmj", "Integrity": "V//ZHA+8aWdywYKr23lc+5YOlH8ar16SpGdqfec90ms=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "E:\\baytara\\Baytara.Client\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.boot.json", "FileLength": 12831, "LastWriteTime": "2025-06-25T03:48:39.551768+00:00"}}, "CachedCopyCandidates": {}}